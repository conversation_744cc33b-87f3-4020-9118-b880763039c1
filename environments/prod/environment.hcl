# Production environment configuration

locals {
  environment = "prod"

  # Environment-specific settings
  instance_type_default = "t3.large"
  auto_scaling_min      = 3
  auto_scaling_max      = 6

  # Backup and retention settings
  backup_retention_days = 30
  log_retention_days    = 90

  # Security settings
  enable_detailed_monitoring = true
  enable_deletion_protection = true

  # Cost optimization
  enable_auto_scaling      = true
  enable_cost_optimization = true

  # Environment-specific tags
  environment_tags = {
    Environment     = local.environment
    CriticalService = "true"
    DataSensitivity = "high"
  }
}
