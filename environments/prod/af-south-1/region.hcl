# environments/prod/af-south-1/region.hcl
# South Africa region configuration

locals {
  aws_region = "af-south-1"

  # Availability zones
  azs = ["af-south-1a", "af-south-1b", "af-south-1c"]

  # Region-specific settings
  instance_type_override = null # Use environment default

  # Data residency and compliance
  data_residency    = "south-africa"
  compliance_region = "za"
  timezone          = "Africa/Johannesburg"

  # Region-specific AMI mappings (Ubuntu 22.04 LTS)
  ami_mappings = {
    ubuntu = "ami-0b8b8b8b8b8b8b8b8" # Update with actual AMI ID
  }

  # CloudFront edge locations (for future CDN)
  cloudfront_price_class = "PriceClass_100" # US, Europe, Asia, Africa

  # Backup strategy
  backup_vault_kms_key_id = null # Will be created by KMS module

  # Monitoring and logging
  cloudwatch_log_group_retention = 90 # Days

  # Cost optimization
  reserved_instance_recommendations = [
    "t3.large",
    "t3.xlarge",
    "db.r5.large",
    "db.r5.xlarge"
  ]
}
