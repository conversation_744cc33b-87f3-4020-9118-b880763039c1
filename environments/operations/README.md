# Operations Environment

The Operations environment hosts cross-environment services that serve all other environments (dev, prod) and provide centralized monitoring, security, and compliance capabilities.

## Architecture Overview

```
Operations Environment (********/16)
├── Network Layer
│   ├── VPC (Cross-environment networking)
│   └── DNS (Cloudflare global DNS)
├── Security Layer
│   ├── KMS (Central encryption keys)
│   ├── Estate Wazuh (Mac fleet monitoring - Public subnet)
│   └── Server Wazuh (Infrastructure monitoring - Private subnet)
├── Analytics Layer
│   └── OpenSearch (Central logging platform)
├── Storage Layer
│   ├── OpenSearch S3 (Unified log storage)
│   └── Wazuh S3 (Unified security data)
└── Access Layer
    └── Bastion (Operations gateway)
```

## Services Deployed

### 🌐 Network Services
- **VPC**: Central networking infrastructure
- **DNS**: Cloudflare DNS management for all environments

### 🛡️ Security Services
- **Estate Wazuh**: Mac fleet monitoring (35 Mac endpoints)
- **Server Wazuh**: Infrastructure security monitoring
- **KMS**: Central encryption key management

### 📊 Analytics Services
- **OpenSearch**: Central logging and analytics platform

### 🗂️ Storage Services
- **OpenSearch S3**: Unified log storage (10-year retention)
- **Wazuh S3**: Unified security data storage (10-year retention)

### 🖥️ Access Services
- **Bastion**: Secure gateway for operations access

## Deployment Structure

```
environments/operations/af-south-1/
├── network/
│   ├── vpc/                    # Central VPC
│   └── dns/                    # Cloudflare DNS
├── security/
│   ├── kms/                    # Encryption keys
│   └── wazuh/
│       ├── estate-endpoint/    # Mac fleet monitoring
│       └── server-endpoint/    # Infrastructure monitoring
├── analytics/
│   └── opensearch/             # Central logging
├── storage/
│   ├── opensearch-s3-compliance/  # Log storage
│   └── wazuh-s3-compliance/        # Security storage
└── ec2/
    └── bastion/                # Operations gateway
```

## Key Features

### 🎯 Centralized Operations
- Single pane of glass for all environments
- Unified compliance reporting
- Cross-environment threat correlation

### 🔒 Security Architecture
- Estate Wazuh: Public subnet for Mac fleet access
- Server Wazuh: Private subnet for infrastructure security
- IP whitelisting for South African ranges

### 📋 Compliance Ready
- 10-year log retention
- ISO 27001, POPIA, GDPR compliance
- Automated backup and protection

### 🌍 Multi-Country Ready
- Designed for African expansion
- Centralized hub architecture
- Regional data aggregation

## Deployment Order

1. **KMS** - Encryption foundation
2. **VPC** - Network infrastructure
3. **Storage** - S3 buckets for data
4. **OpenSearch** - Central logging platform
5. **Wazuh** - Security monitoring
6. **Bastion** - Access gateway
7. **DNS** - Global DNS management

## Access Patterns

### Mac Fleet (Estate Endpoint)
- **Connection**: Direct internet → Public Wazuh
- **IP Ranges**: South African ISP ranges
- **Purpose**: Endpoint security monitoring

### Infrastructure (Server Endpoint)
- **Connection**: VPC internal → Private Wazuh
- **Access**: Bastion tunnel required
- **Purpose**: Infrastructure security monitoring

### Operations Team
- **Connection**: Bastion → Internal services
- **Access**: SSH tunneling for secure access
- **Purpose**: Platform management and monitoring

## Resource Naming Convention

All resources use the `infodocs-operations-*` prefix:
- VPC: `infodocs-operations-vpc`
- Wazuh Estate: `infodocs-estate-wazuh-server-1`
- Wazuh Server: `infodocs-central-wazuh-server-1`
- OpenSearch: `infodocs-operations-opensearch`
- S3 Buckets: `infodocs-operations-opensearch`, `infodocs-operations-wazuh`

## Compliance & Retention

### Data Retention
- **Log Data**: 10 years (3650 days)
- **Security Events**: 10 years (3650 days)
- **Snapshots**: 30 days
- **Backups**: Cross-region replication ready

### Protection Features
- S3 Object Lock enabled
- Versioning enabled
- MFA delete protection
- Deletion protection enabled

## Future Expansion

### EU Backup Region
```
environments/operations/eu-west-1/
├── network/vpc/           # Replicated VPC
├── storage/               # Cross-region replication
└── security/              # Backup Wazuh instances
```
