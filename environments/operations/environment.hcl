# Operations environment configuration
locals {
  environment = "operations"
  region      = "af-south-1"

  # Operations-specific settings
  cost_optimization = "enabled"
  monitoring_level  = "enhanced"
  backup_retention  = "10-years"

  # Security settings for operations environment
  security_level        = "high"
  compliance_frameworks = ["POPIA", "GDPR", "SOX", "HIPAA", "PCI-DSS"]

  # Network settings
  vpc_cidr = "********/16"

  # Common tags for operations environment
  common_tags = {
    Environment = "operations"
    Region      = "af-south-1"
    Purpose     = "cross-environment-operations"
    Scope       = "global-operations"
  }
}
