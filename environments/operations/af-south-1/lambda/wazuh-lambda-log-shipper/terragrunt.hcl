include "root" {
  path = find_in_parent_folders()
}

include "env" {
  path = find_in_parent_folders("environment.hcl")
}

terraform {
  source = "../../../../../modules//lambda/wazuh-lambda-log-shipper"
}

dependency "kms" {
  config_path = "../../security/kms"
}

dependency "s3_wazuh" {
  config_path = "../../s3/wazuh-bucket"
}

dependency "s3_legal_hold" {
  config_path = "../../s3/legal-hold-bucket"
}

inputs = {
  name_prefix = "infodocs-central"
  environment = "central"

  # S3 Bucket Dependencies
  wazuh_s3_bucket      = dependency.s3_wazuh.outputs.bucket_name
  legal_hold_s3_bucket = dependency.s3_legal_hold.outputs.bucket_name

  # KMS Configuration
  kms_key_id  = dependency.kms.outputs.key_id
  kms_key_arn = dependency.kms.outputs.key_arn

  # Wazuh-specific Configuration
  wazuh_alert_threshold      = 10
  enable_threat_intelligence = true
  security_classification    = "confidential"
  compliance_frameworks      = ["POPIA", "GDPR", "SOX", "HIPAA", "PCI-DSS"]

  # Lambda Configuration
  lambda_log_retention_days  = 90
  enable_enhanced_monitoring = true

  # Security Monitoring
  wazuh_log_types           = ["alerts", "events", "archives", "firewall", "authentication"]
  security_alarm_sns_topics = [] # Add SNS topic ARNs when available

  tags = {
    Environment = "operations"
    Project     = "infodocs"
    ManagedBy   = "opentofu"
    Purpose     = "wazuh-security-log-processing"
    LogSource   = "wazuh"
    DataClass   = "confidential"
    Compliance  = "security-monitoring"
    CostCenter  = "infrastructure"
    Owner       = "infodocs-operations"
  }
}
