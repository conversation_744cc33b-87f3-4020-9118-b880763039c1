{"statusCode": 200, "body": "{\"message\": \"Started S3 export for 3 log groups\", \"exportedGroups\": [{\"logGroup\": \"/aws/apigateway/welcome\", \"taskId\": \"25420ba8-6ca6-466a-9ca4-8c9084d42944\", \"s3Prefix\": \"cloudwatch-logs/2025/07/11/-aws-apigateway-welcome\", \"status\": \"RUNNING\"}, {\"logGroup\": \"/aws/lambda/ValidationLambda\", \"taskId\": \"cc8d4483-1b20-434d-af27-0aafd4d3b7ad\", \"s3Prefix\": \"cloudwatch-logs/2025/07/11/-aws-lambda-ValidationLambda\", \"status\": \"RUNNING\"}, {\"logGroup\": \"RDSOSMetrics\", \"taskId\": \"33c36f53-4d27-427c-8cee-85b15aaa8715\", \"s3Prefix\": \"cloudwatch-logs/2025/07/11/RDSOSMetrics\", \"status\": \"RUNNING\"}], \"note\": \"S3 exports will trigger automatic processing to OpenSearch when complete\"}", "headers": {"Content-Type": "application/json"}}