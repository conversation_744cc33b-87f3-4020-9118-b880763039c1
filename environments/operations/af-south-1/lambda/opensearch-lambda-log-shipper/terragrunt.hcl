include {
  path = find_in_parent_folders()
}

locals {
  # Retrieve KMS keys from Parameter Store
  kms_key_id  = run_cmd("aws", "ssm", "get-parameter", "--name", "/infodocs/infrastructure/kms/key_id", "--query", "Parameter.Value", "--output", "text")
  kms_key_arn = run_cmd("aws", "ssm", "get-parameter", "--name", "/infodocs/infrastructure/kms/key_arn", "--query", "Parameter.Value", "--output", "text")
}

terraform {
  source = "../../../../../modules//lambda/opensearch-lambda-log-shipper"
}

# KMS dependency removed - using alias directly

# Use data sources instead of dependencies to avoid circular dependency

dependency "opensearch" {
  config_path = "../../analytics/opensearch"

  mock_outputs = {
    domain_endpoint = "vpc-mock-opensearch.af-south-1.es.amazonaws.com"
  }
}

dependency "vpc" {
  config_path = "../../network/vpc"
}

inputs = {
  name_prefix = "infodocs-operations"
  environment = "operations"

  # S3 Bucket Configuration (uses data sources to look up existing resources)
  opensearch_s3_bucket = "infodocs-operations-opensearch"  # Actual bucket name
  legal_hold_s3_bucket = "infodocs-operations-opensearch"  # Actual bucket name

  # OpenSearch Configuration (uses data sources to look up domain endpoint)
  opensearch_cluster_endpoints = []  # Empty - will use data source to look up domain

  # KMS Configuration - disable for CloudWatch Logs temporarily to get data flowing
  kms_key_id  = null                             # Disable CloudWatch Logs encryption for now
  kms_key_arn = local.kms_key_arn                # IAM policies still use full ARN

  # OpenSearch-specific Configuration
  data_classification      = "confidential"
  compliance_frameworks    = ["POPIA", "GDPR", "SOX"]
  enable_cost_optimization = true

  # Lambda Configuration
  lambda_log_retention_days = 90 # Shorter retention for application logs
  enable_monitoring         = true

  # VPC Configuration - Lambda needs to be in same VPC as OpenSearch
  vpc_subnet_ids         = dependency.vpc.outputs.private_subnets  # Use private subnets from VPC
  vpc_security_group_ids = ["sg-0a6c646a0c24cebd6"]               # OpenSearch security group

  # OpenSearch Features
  opensearch_log_types         = ["application", "search", "index", "cluster"]
  opensearch_cluster_endpoints = [] # Add when OpenSearch is deployed
  alarm_sns_topics             = [] # Add SNS topic ARNs when available

  tags = {
    Environment = "operations"
    Project     = "infodocs"
    ManagedBy   = "opentofu"
    Purpose     = "opensearch-application-log-processing"
    LogSource   = "opensearch"
    DataClass   = "confidential"
    Compliance  = "application-monitoring"
    CostCenter  = "infrastructure"
    Owner       = "infodocs-operations"
  }
}
