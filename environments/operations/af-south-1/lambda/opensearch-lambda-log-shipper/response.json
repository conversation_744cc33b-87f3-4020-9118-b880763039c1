{"statusCode": 200, "body": "{\"message\": \"Started S3 export for 4 log groups\", \"exportedGroups\": [{\"logGroup\": \"/aws/apigateway/welcome\", \"taskId\": \"30b9ac39-0dc7-4744-bd97-f6b60a91bc1d\", \"s3Prefix\": \"cloudwatch-logs/2025/07/11/-aws-apigateway-welcome\", \"status\": \"RUNNING\"}, {\"logGroup\": \"/aws/lambda/bots-selenium3\", \"taskId\": \"019212fe-a6ef-4193-9255-8ff531160a23\", \"s3Prefix\": \"cloudwatch-logs/2025/07/11/-aws-lambda-bots-selenium3\", \"status\": \"RUNNING\"}, {\"logGroup\": \"/aws/lambda/infodocs-operations-opensearch-log-shipper\", \"taskId\": \"17fef3d8-32aa-4166-bd11-6e98281abd18\", \"s3Prefix\": \"cloudwatch-logs/2025/07/11/-aws-lambda-infodocs-operations-opensearch-log-shipper\", \"status\": \"RUNNING\"}, {\"logGroup\": \"/aws/lambda/snsSlackNotifications\", \"taskId\": \"70d0a070-1997-44e6-8d74-5d15d717e5bc\", \"s3Prefix\": \"cloudwatch-logs/2025/07/11/-aws-lambda-snsSlackNotifications\", \"status\": \"RUNNING\"}], \"note\": \"S3 exports will trigger automatic processing to OpenSearch when complete\"}", "headers": {"Content-Type": "application/json"}}