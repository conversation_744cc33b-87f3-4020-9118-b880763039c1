{"statusCode": 200, "body": "{\"message\": \"Started S3 export for 6 log groups\", \"exportedGroups\": [{\"logGroup\": \"/aws/apigateway/welcome\", \"taskId\": \"515f1bc1-a37e-4748-98ca-6f3800c25447\", \"s3Prefix\": \"cloudwatch-logs/2025/07/11/-aws-apigateway-welcome\", \"status\": \"RUNNING\"}, {\"logGroup\": \"/aws/lambda-insights\", \"taskId\": \"84b4bf5c-5e83-4235-bcd1-8b4508dc6555\", \"s3Prefix\": \"cloudwatch-logs/2025/07/11/-aws-lambda-insights\", \"status\": \"RUNNING\"}, {\"logGroup\": \"/aws/lambda/Regbot2\", \"taskId\": \"94939d5c-9e90-4c35-ba6f-24b9234fd03f\", \"s3Prefix\": \"cloudwatch-logs/2025/07/11/-aws-lambda-Regbot2\", \"status\": \"RUNNING\"}, {\"logGroup\": \"/aws/lambda/ValidationLambda\", \"taskId\": \"4807061e-d722-462a-82b0-b6513bcae55f\", \"s3Prefix\": \"cloudwatch-logs/2025/07/11/-aws-lambda-ValidationLambda\", \"status\": \"RUNNING\"}, {\"logGroup\": \"/aws/lambda/sample-app-HelloWorldFunction-jKTdeHtwX1uB\", \"taskId\": \"a4693c4d-f86f-47a0-a4a3-5106393a9fab\", \"s3Prefix\": \"cloudwatch-logs/2025/07/11/-aws-lambda-sample-app-HelloWorldFunction-jKTdeHtwX1uB\", \"status\": \"RUNNING\"}, {\"logGroup\": \"RDSOSMetrics\", \"taskId\": \"5857d64a-4623-49e5-93f5-544ccab0f57f\", \"s3Prefix\": \"cloudwatch-logs/2025/07/11/RDSOSMetrics\", \"status\": \"RUNNING\"}], \"note\": \"S3 exports will trigger automatic processing to OpenSearch when complete\"}", "headers": {"Content-Type": "application/json"}}