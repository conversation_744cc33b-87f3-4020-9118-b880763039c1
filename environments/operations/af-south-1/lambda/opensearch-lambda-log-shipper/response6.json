{"statusCode": 200, "body": "{\"message\": \"Started S3 export for 6 log groups\", \"exportedGroups\": [{\"logGroup\": \"/aws/lambda/auditorVerifyV1\", \"taskId\": \"5ac08fb3-7adc-470d-9226-f782e80817e9\", \"s3Prefix\": \"cloudwatch-logs/2025/07/11/-aws-lambda-auditorVerifyV1\", \"status\": \"RUNNING\"}, {\"logGroup\": \"/aws/lambda/demo\", \"taskId\": \"c3722dbd-218d-42fb-9b3f-7ab13f08af53\", \"s3Prefix\": \"cloudwatch-logs/2025/07/11/-aws-lambda-demo\", \"status\": \"RUNNING\"}, {\"logGroup\": \"/aws/lambda/directorChangeVerifyV1\", \"taskId\": \"843cae62-7cb4-4bde-a29c-31d3dbae27e0\", \"s3Prefix\": \"cloudwatch-logs/2025/07/11/-aws-lambda-directorChangeVerifyV1\", \"status\": \"RUNNING\"}, {\"logGroup\": \"/aws/lambda/nameChangeV1\", \"taskId\": \"f2c66419-d230-48cf-b4a7-0f8517d10cd4\", \"s3Prefix\": \"cloudwatch-logs/2025/07/11/-aws-lambda-nameChangeV1\", \"status\": \"RUNNING\"}, {\"logGroup\": \"/aws/lambda/newCompanyRegistrationV1\", \"taskId\": \"0f876f3a-ab3c-494c-a68b-eb8b3b26fc82\", \"s3Prefix\": \"cloudwatch-logs/2025/07/11/-aws-lambda-newCompanyRegistrationV1\", \"status\": \"RUNNING\"}, {\"logGroup\": \"/aws/s3/infodocs-central-opensearch-access\", \"taskId\": \"f330d415-007b-42ec-8bb5-79903a5f8dee\", \"s3Prefix\": \"cloudwatch-logs/2025/07/11/-aws-s3-infodocs-central-opensearch-access\", \"status\": \"RUNNING\"}], \"note\": \"S3 exports will trigger automatic processing to OpenSearch when complete\"}", "headers": {"Content-Type": "application/json"}}