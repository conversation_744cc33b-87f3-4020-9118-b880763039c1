include "root" {
  path = find_in_parent_folders()
}

terraform {
  source = "../../../../../modules//analytics/grafana"
}

dependency "opensearch" {
  config_path = "../opensearch"

  mock_outputs = {
    domain_endpoint = "https://search-infodocs-operations-os-abcdefghijklmnop.af-south-1.es.amazonaws.com"
  }
}

inputs = {
  # Environment Configuration
  environment = "operations"

  # OpenSearch Integration
  opensearch_endpoint = dependency.opensearch.outputs.domain_endpoint

  # CloudWatch Log Groups to Monitor
  cloudwatch_log_groups = [
    "/aws/lambda/infodocs-operations-opensearch-log-shipper",
    "/aws/lambda/infodocs-operations-wazuh-log-shipper",
    "/aws/ec2/bastion",
    "/aws/opensearch/infodocs-operations-os",
    "/infodocs/laravel/application",
    "/infodocs/laravel/audit"
  ]

  # Alert Configuration
  alert_evaluation_interval = "1m"

  # Slack Webhooks - Replace with your actual webhook URLs
  slack_webhooks = {
    critical       = "https://hooks.slack.com/services/T1234567/B1234567/REPLACE_WITH_YOUR_CRITICAL_WEBHOOK"
    infrastructure = "https://hooks.slack.com/services/T1234567/B1234567/REPLACE_WITH_YOUR_INFRASTRUCTURE_WEBHOOK"
    application    = "https://hooks.slack.com/services/T1234567/B1234567/REPLACE_WITH_YOUR_APPLICATION_WEBHOOK"
    security       = "https://hooks.slack.com/services/T1234567/B1234567/REPLACE_WITH_YOUR_SECURITY_WEBHOOK"
  }

  # Tags
  tags = {
    Environment = "operations"
    Project     = "infodocs"
    ManagedBy   = "opentofu"
    Purpose     = "grafana-configuration"
    Service     = "monitoring"
    CostCenter  = "infrastructure"
    Owner       = "devops-team"
  }
}
