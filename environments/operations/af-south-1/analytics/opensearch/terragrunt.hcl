include "root" {
  path = find_in_parent_folders()
}

terraform {
  source = "../../../../../modules//analytics/opensearch"
}

dependency "vpc" {
  config_path = "../../network/vpc"

  mock_outputs = {
    vpc_id          = "vpc-********"
    private_subnets = ["subnet-********", "subnet-********"]
    vpc_cidr_block  = "********/16"
  }
}

dependency "kms" {
  config_path = "../../security/kms"

  mock_outputs = {
    key_arn = "arn:aws:kms:af-south-1:${get_aws_account_id()}:key/********-1234-1234-1234-********9012"
    key_id  = "********-1234-1234-1234-********9012"
  }
}

# dependency "lambda_log_shipper" {
#   config_path = "../../lambda/opensearch-lambda-log-shipper"
#
#   mock_outputs = {
#     function_arn = "arn:aws:lambda:af-south-1:${get_aws_account_id()}:function:infodocs-operations-opensearch-log-shipper"
#   }
# }

inputs = {
  # Basic Configuration
  domain_name = "infodocs-operations-os"
  name_prefix = "infodocs-operations"
  environment = "operations"

  # Instance Configuration
  instance_type  = "t3.small.search"
  instance_count = 1
  volume_size    = 10

  # Network Configuration
  vpc_id              = dependency.vpc.outputs.vpc_id
  subnet_ids          = dependency.vpc.outputs.private_subnets
  allowed_cidr_blocks = [dependency.vpc.outputs.vpc_cidr_block] # Only allow VPC access

  # Security Configuration
  kms_key_arn = dependency.kms.outputs.key_arn

  # Log Configuration - disable S3 publishing temporarily to fix permissions
  enable_s3_log_publishing = false # Disable until permissions are fixed
  log_retention_days       = 365
  lambda_log_shipper_arn   = "arn:aws:lambda:af-south-1:************:function:infodocs-operations-opensearch-log-shipper"

  # Tags
  tags = {
    Environment = "operations"
    Project     = "infodocs"
    ManagedBy   = "opentofu"
    CostCenter  = "infrastructure"
    Owner       = "infodocs-operations"
    Application = "infodocs-opensearch"
  }
}
