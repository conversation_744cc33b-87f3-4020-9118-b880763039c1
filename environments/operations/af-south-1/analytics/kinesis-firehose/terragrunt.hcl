include {
  path = find_in_parent_folders()
}

terraform {
  source = "../../../../../modules//analytics/kinesis-firehose"
}

dependency "kms" {
  config_path = "../../security/kms"
}

inputs = {
  name_prefix = "infodocs-operations"
  environment = "operations"

  s3_bucket_name = "infodocs-operations-opensearch"

  kms_key_id  = null
  kms_key_arn = dependency.kms.outputs.key_arn

  buffer_size     = 1
  buffer_interval = 60

  log_retention_days = 30
  enable_monitoring  = true
  alarm_sns_topics   = []

  tags = {
    Environment = "operations"
    Project     = "infodocs"
    ManagedBy   = "opentofu"
    CostCenter  = "infrastructure"
    Owner       = "infodocs-operations"
    Application = "kinesis-firehose-log-streaming"
    Purpose     = "real-time-log-streaming"
    DataClass   = "confidential"
    Compliance  = "POPIA-GDPR-SOX"
  }
}