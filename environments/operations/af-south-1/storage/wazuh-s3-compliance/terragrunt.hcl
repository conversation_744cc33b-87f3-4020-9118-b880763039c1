include "root" {
  path = find_in_parent_folders()
}

include "env" {
  path = find_in_parent_folders("environment.hcl")
}

dependency "kms" {
  config_path = "../../security/kms"
}

terraform {
  source = "../../../../../modules//storage/s3/wazuh"
}

inputs = {
  bucket_name = "infodocs-operations-wazuh" # Unified naming convention
  name_prefix = "infodocs-operations"
  environment = "operations"

  kms_key_arn = dependency.kms.outputs.key_arn

  # Wazuh-specific Configuration
  wazuh_retention_years   = 10
  security_classification = "confidential"
  compliance_frameworks   = ["POPIA", "GDPR", "SOX", "HIPAA", "PCI-DSS"]

  # Protection Settings
  enable_deletion_protection = true
  enable_object_lock         = true
  enable_versioning          = true
  enable_mfa_delete          = true

  # Security Features
  enable_cross_region_replication = false
  wazuh_agent_access_cidrs        = ["********/16"] # VPC CIDR

  tags = {
    Environment = "operations"
    Project     = "infodocs"
    ManagedBy   = "opentofu"
    Purpose     = "wazuh-security-logs"
    LogSource   = "wazuh"
    DataClass   = "confidential"
    Compliance  = "security-monitoring"
    Retention   = "10-years"
    CostCenter  = "infrastructure"
    Owner       = "infodocs-operations"
  }
}
