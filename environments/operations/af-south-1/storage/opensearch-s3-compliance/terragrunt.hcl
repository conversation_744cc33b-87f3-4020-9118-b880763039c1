include "root" {
  path = find_in_parent_folders()
}

include "env" {
  path = find_in_parent_folders("environment.hcl")
}

dependency "kms" {
  config_path = "../../security/kms"
}

dependency "lambda" {
  config_path = "../../lambda/opensearch-lambda-log-shipper"
}

terraform {
  source = "../../../../../modules//storage/s3/opensearch"
}

inputs = {
  bucket_name = "infodocs-operations-opensearch" # Unified naming convention
  name_prefix = "infodocs-operations"
  environment = "operations"

  kms_key_arn         = dependency.kms.outputs.key_arn
  lambda_function_arn = dependency.lambda.outputs.function_arn

  # OpenSearch-specific Configuration
  retention_days            = 3650 # 10 years retention
  enable_automatic_deletion = false
  data_classification       = "confidential"
  compliance_frameworks     = ["POPIA", "GDPR", "SOX"]

  # Protection Settings
  enable_deletion_protection = true
  enable_object_lock         = true
  enable_versioning          = true
  enable_mfa_delete          = true

  # Cost Optimization Features
  enable_intelligent_tiering = true
  access_log_retention_days  = 365 # 1 year for application logs

  # OpenSearch Features
  opensearch_cluster_access_cidrs = ["********/16"] # VPC CIDR
  log_types                       = ["application", "search", "index", "cluster"]

  tags = {
    Environment = "operations"
    Project     = "infodocs"
    ManagedBy   = "opentofu"
    Purpose     = "opensearch-application-logs"
    LogSource   = "opensearch"
    DataClass   = "confidential"
    Compliance  = "application-monitoring"
    Retention   = "10-years"
    CostCenter  = "infrastructure"
    Owner       = "infodocs-operations"
  }
}
