include {
  path = find_in_parent_folders()
}

dependency "vpc" {
  config_path = "../../network/vpc"

  mock_outputs = {
    vpc_id          = "vpc-dev123"
    public_subnets  = ["subnet-dev-pub1", "subnet-dev-pub2"]
    private_subnets = ["subnet-dev-priv1", "subnet-dev-priv2"]
    vpc_cidr_block  = "10.1.0.0/16"
  }
}

dependency "kms" {
  config_path = "../../security/kms"

  mock_outputs = {
    key_arn = "arn:aws:kms:af-south-1:${get_aws_account_id()}:key/********-1234-1234-1234-********9012"
    key_id  = "********-1234-1234-1234-********9012"
  }
}

terraform {
  source = "../../../../../modules//ec2/bastion"
}

dependency "ssm" {
  config_path = "../../security/ssm"

  mock_outputs = {
    parameters = {
      "ec2/key_name" = "infodocs-infrastructure"
    }
  }
}

inputs = {
  # Basic Configuration
  name_prefix = "infodocs-operations"
  environment = "operations"

  # Network Configuration
  vpc_id           = dependency.vpc.outputs.vpc_id
  public_subnet_id = dependency.vpc.outputs.public_subnets[0]

  # Security Configuration - SSH key name read from SSM via data source
  kms_key_id = dependency.kms.outputs.key_id

  # Instance Configuration
  instance_type = "t3.small" # Upgraded from t3.micro for better SSH performance

  # Access Configuration - Allow SSH from your IPs (direct + WARP)
  allowed_ssh_cidr_blocks = [
    "**************/32", # Juandré Van Der Westhuize | Home IP Address (Direct)
    "*************/32"   # Juandré Van Der Westhuize | Home IP Address (via Cloudflare WARP)
  ]

  admin_users = ["ec2-user", "admin"]

  enable_cloudwatch_logs   = true
  cloudwatch_log_retention = 120
  allocate_eip             = true # Enable Elastic IP to prevent IP changes
  setup_cloudflare_tunnel  = true

  tags = {
    Environment = "operations"
    Project     = "infodocs"
    ManagedBy   = "opentofu"
    Purpose     = "secure-access-bastion"
    Scope       = "all-environments"
  }
}
