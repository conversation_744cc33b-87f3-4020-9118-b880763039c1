include {
  path = find_in_parent_folders()
}

terraform {
  source = "../../../../../../modules//dns/cloudflare"
}

inputs = {
  environment = "operations"
  domain      = "infodocs.co.za"
  ip_address  = "***********" # Bastion public IP

  dns_records = {
    # Main domain
    "root-1" = {
      name    = "infodocs.co.za"
      content = "***********"
      type    = "A"
      ttl     = 1 # Changed from 300 (required for proxied)
      proxied = true
    }
    "root-2" = {
      name    = "infodocs.co.za"
      content = "************"
      type    = "A"
      ttl     = 1 # Changed from 300 (required for proxied)
      proxied = true
    }

    # Subdomains - proxy public-facing records
    "api" = {
      name    = "api.infodocs.co.za"
      content = "*************"
      type    = "A"
      ttl     = 1 # Changed from 300 (required for proxied)
      proxied = true
    }
    "api2" = {
      name    = "api2.infodocs.co.za"
      content = "*************"
      type    = "A"
      ttl     = 1 # Changed from 300 (required for proxied)
      proxied = true
    }
    "dev-api" = {
      name    = "dev-api.infodocs.co.za"
      content = "*************"
      type    = "A"
      ttl     = 1 # Changed from 300 (required for proxied)
      proxied = true
    }
    "dev" = {
      name    = "dev.infodocs.co.za"
      content = "*************"
      type    = "A"
      ttl     = 1 # Changed from 300 (required for proxied)
      proxied = true
    }
    "lego" = {
      name    = "lego.infodocs.co.za"
      content = "54.154.111.248"
      type    = "A"
      ttl     = 1 # Changed from 3600 (required for proxied)
      proxied = true
    }
    "lego2" = {
      name    = "lego2.infodocs.co.za"
      content = "13.246.160.246"
      type    = "A"
      ttl     = 1 # Changed from 300 (required for proxied)
      proxied = true
    }
    "secure" = {
      name    = "secure.infodocs.co.za"
      content = "*************"
      type    = "A"
      ttl     = 1 # Changed from 300 (required for proxied)
      proxied = true
    }
    "staging" = {
      name    = "staging.infodocs.co.za"
      content = "13.246.160.246"
      type    = "A"
      ttl     = 1 # Changed from 3600 (required for proxied)
      proxied = true
    }
    "uat-api" = {
      name    = "uat-api.infodocs.co.za"
      content = "13.244.50.225"
      type    = "A"
      ttl     = 1 # Changed from 300 (required for proxied)
      proxied = true
    }
    "uat" = {
      name    = "uat.infodocs.co.za"
      content = "13.244.50.225"
      type    = "A"
      ttl     = 1 # Changed from 300 (required for proxied)
      proxied = true
    }
    "verify" = {
      name    = "verify.infodocs.co.za"
      content = "102.133.128.247"
      type    = "A"
      ttl     = 1 # Changed from 300 (required for proxied)
      proxied = true
    }

    # CNAME records - proxy where applicable
    "www" = {
      name    = "www.infodocs.co.za"
      content = "sites.framer.app"
      type    = "CNAME"
      ttl     = 1 # Changed from 300 (required for proxied)
      proxied = true
    }
    "help" = {
      name    = "help.infodocs.co.za"
      content = "help-infodocs-co-za-0424c768-8856-45d9-9f69-8a56c347f98d.saas.atlassian.com"
      type    = "CNAME"
      ttl     = 1 # Changed from 300 (required for proxied)
      proxied = true
    }
    "support" = {
      name    = "support.infodocs.co.za"
      content = "custom.crisp.help"
      type    = "CNAME"
      ttl     = 1 # Changed from 300 (required for proxied)
      proxied = true
    }
    "links-mail" = {
      name    = "links.mail.infodocs.co.za"
      content = "links.custom.crisp.email"
      type    = "CNAME"
      ttl     = 300
      proxied = false
    }

    # Infrastructure services - keep non-proxied
    "wazuh-server" = {
      name    = "wazuh-server.infodocs.co.za"
      content = "***********"
      type    = "A"
      ttl     = 3600
      proxied = false
    }
    "wazuh-estate" = {
      name    = "wazuh-estate.infodocs.co.za"
      content = "13.246.36.164"
      type    = "A"
      ttl     = 3600
      proxied = false
    }
    # opensearch DNS record now managed by Cloudflare Tunnel module

    # Missing subdomains from Route53 - proxy public-facing
    "dashboard" = {
      name    = "dashboard.infodocs.co.za"
      content = "*************"
      type    = "A"
      ttl     = 1 # Changed from 300 (required for proxied)
      proxied = true
    }
    "docbot" = {
      name    = "docbot.infodocs.co.za"
      content = "*************"
      type    = "A"
      ttl     = 1 # Changed from 300 (required for proxied)
      proxied = true
    }
    "veribot-1" = {
      name    = "veribot.infodocs.co.za"
      content = "*************"
      type    = "A"
      ttl     = 1 # Changed from 300 (required for proxied)
      proxied = true
    }
    "veribot-2" = {
      name    = "veribot.infodocs.co.za"
      content = "************"
      type    = "A"
      ttl     = 1 # Changed from 300 (required for proxied)
      proxied = true
    }

    # Mail-related subdomains - use MX records
    "mail-infodocs-1" = {
      name     = "mail.infodocs.co.za"
      content  = "feedback-smtp.eu-west-1.amazonses.com" # Changed from "10 feedback-smtp.eu-west-1.amazonses.com"
      type     = "MX"
      ttl      = 300
      proxied  = false
      priority = 10
    }
    "mail-infodocs-2" = {
      name     = "mail.infodocs.co.za"
      content  = "inbound.crisp.email" # Changed from "20 inbound.crisp.email"
      type     = "MX"
      ttl      = 300
      proxied  = false
      priority = 20
    }
    "mail-docbot-1" = {
      name     = "mail.docbot.infodocs.co.za"
      content  = "feedback-smtp.eu-west-1.amazonses.com" # Changed from "10 feedback-smtp.eu-west-1.amazonses.com"
      type     = "MX"
      ttl      = 300
      proxied  = false
      priority = 10
    }
    "mail-veribot-1" = {
      name     = "mail.veribot.infodocs.co.za"
      content  = "feedback-smtp.eu-west-1.amazonses.com" # Changed from "10 feedback-smtp.eu-west-1.amazonses.com"
      type     = "MX"
      ttl      = 300
      proxied  = false
      priority = 10
    }
    "ses-mail-1" = {
      name     = "ses.mail.infodocs.co.za"
      content  = "feedback-smtp.eu-west-1.amazonses.com" # Changed from "10 feedback-smtp.eu-west-1.amazonses.com"
      type     = "MX"
      ttl      = 300
      proxied  = false
      priority = 10
    }

    "customer-support" = {
      name    = "customer.support.infodocs.co.za"
      content = "customer-support-infodocs-241accd3-0e15-402a-a672-d8c5b9159274.saas.atlassian.com"
      type    = "CNAME"
      ttl     = 1 # Changed from 300 (required for proxied)
      proxied = true
    }
    "old-support" = {
      name    = "old.support.infodocs.co.za"
      content = "ec2-13-246-14-29.af-south-1.compute.amazonaws.com"
      type    = "CNAME"
      ttl     = 1 # Changed from 300 (required for proxied)
      proxied = true
    }

    # Email Records - MX Records for Google Workspace
    "mx-1" = {
      name     = "infodocs.co.za"
      content  = "aspmx.l.google.com" # Changed from "1 aspmx.l.google.com"
      type     = "MX"
      ttl      = 86400
      proxied  = false
      priority = 1
    }
    "mx-2" = {
      name     = "infodocs.co.za"
      content  = "alt1.aspmx.l.google.com" # Changed from "5 alt1.aspmx.l.google.com"
      type     = "MX"
      ttl      = 86400
      proxied  = false
      priority = 5
    }
    "mx-3" = {
      name     = "infodocs.co.za"
      content  = "alt2.aspmx.l.google.com" # Changed from "5 alt2.aspmx.l.google.com"
      type     = "MX"
      ttl      = 86400
      proxied  = false
      priority = 5
    }
    "mx-4" = {
      name     = "infodocs.co.za"
      content  = "alt3.aspmx.l.google.com" # Changed from "10 alt3.aspmx.l.google.com"
      type     = "MX"
      ttl      = 86400
      proxied  = false
      priority = 10
    }
    "mx-5" = {
      name     = "infodocs.co.za"
      content  = "alt4.aspmx.l.google.com" # Changed from "10 alt4.aspmx.l.google.com"
      type     = "MX"
      ttl      = 86400
      proxied  = false
      priority = 10
    }

    # SPF Record
    "spf" = {
      name    = "infodocs.co.za"
      content = "\"v=spf1 include:_spf.google.com include:amazonses.com ~all\""
      type    = "TXT"
      ttl     = 300
      proxied = false
    }

    # DMARC Record with Cloudflare reporting
    "dmarc" = {
      name    = "_dmarc.infodocs.co.za"
      content = "\"v=DMARC1; p=none; rua=mailto:<EMAIL>,mailto:<EMAIL>; ruf=mailto:<EMAIL>; fo=1; adkim=r; aspf=r; pct=100; rf=afrf; ri=86400\""
      type    = "TXT"
      ttl     = 300
      proxied = false
    }

    # Amazon SES Verification
    "amazonses" = {
      name    = "_amazonses.infodocs.co.za"
      content = "\"DHz2/Nk2XLuDMbcFIUcMYU4i1qemBMoTOqF4xW4i71M=\""
      type    = "TXT"
      ttl     = 3600
      proxied = false
    }

    # Additional CNAME records from Route53
    "crisp-mail" = {
      name    = "_crisp.mail.infodocs.co.za"
      content = "\"crisp-website-id=9b3721bc-8097-420d-bbe9-e55e891db8fd\""
      type    = "TXT"
      ttl     = 300
      proxied = false
    }
    "crisp-support" = {
      name    = "_crisp.support.infodocs.co.za"
      content = "\"crisp-website-id=9b3721bc-8097-420d-bbe9-e55e891db8fd\""
      type    = "TXT"
      ttl     = 300
      proxied = false
    }

    # SSL Certificate Validation Records (ACM)
    "ssl-validation-1" = {
      name    = "_9a19ebcb1893f7dbe160049dd51d1b5d.infodocs.co.za"
      content = "_1db07872d467c39da1d23ca1cef1be94.acm-validations.aws"
      type    = "CNAME"
      ttl     = 3600
      proxied = false
    }
    "ssl-validation-help" = {
      name    = "_e15d3abe37fa8c949693871f1bc9b6a1.help.infodocs.co.za"
      content = "help-infodocs-co-za-0424c768-8856-45d9-9f69-8a56c347f98d.ssl.atlassian.com"
      type    = "CNAME"
      ttl     = 300
      proxied = false
    }
    "ssl-validation-customer" = {
      name    = "_a5ba1e957a301f5d3cf78d5dfea4187a.customer.support.infodocs.co.za"
      content = "customer-support-infodocs-241accd3-0e15-402a-a672-d8c5b9159274.ssl.atlassian.com"
      type    = "CNAME"
      ttl     = 300
      proxied = false
    }

    # DKIM Records for Amazon SES (main domain)
    "dkim-1" = {
      name    = "a6htufffuzcfovy6iuhssbbnuahjnizs._domainkey.infodocs.co.za"
      content = "a6htufffuzcfovy6iuhssbbnuahjnizs.dkim.af-south-1.amazonses.com"
      type    = "CNAME"
      ttl     = 1800
      proxied = false
    }
    "dkim-2" = {
      name    = "arfzopdfxzlxnrk4r5zpway77ad25k32._domainkey.infodocs.co.za"
      content = "arfzopdfxzlxnrk4r5zpway77ad25k32.dkim.amazonses.com"
      type    = "CNAME"
      ttl     = 3600
      proxied = false
    }
    "dkim-3" = {
      name    = "huco5ycncasjlrkjmurko66ih3ilisdg._domainkey.infodocs.co.za"
      content = "huco5ycncasjlrkjmurko66ih3ilisdg.dkim.af-south-1.amazonses.com"
      type    = "CNAME"
      ttl     = 1800
      proxied = false
    }
    "dkim-4" = {
      name    = "jhk4w5mtrz6yr67vz47zhqtpjx7junep._domainkey.infodocs.co.za"
      content = "jhk4w5mtrz6yr67vz47zhqtpjx7junep.dkim.amazonses.com"
      type    = "CNAME"
      ttl     = 3600
      proxied = false
    }
    "dkim-5" = {
      name    = "r33tvtq63rztordahovz7fxfsxjb4hgd._domainkey.infodocs.co.za"
      content = "r33tvtq63rztordahovz7fxfsxjb4hgd.dkim.amazonses.com"
      type    = "CNAME"
      ttl     = 3600
      proxied = false
    }
    "dkim-6" = {
      name    = "xztktlhrm2zqsss7bra2gqykte7johko._domainkey.infodocs.co.za"
      content = "xztktlhrm2zqsss7bra2gqykte7johko.dkim.af-south-1.amazonses.com"
      type    = "CNAME"
      ttl     = 1800
      proxied = false
    }

    # DKIM Records for docbot subdomain
    "amazonses-docbot" = {
      name    = "_amazonses.docbot.infodocs.co.za"
      content = "\"/6ofzJB2i/7OnpNLYnMr2rAAuRRcBKfycWwsZjtV6io=\""
      type    = "TXT"
      ttl     = 3600
      proxied = false
    }
    "dkim-docbot-1" = {
      name    = "3o2vulhi6gxu4qvhw6ruyweko2xzin47._domainkey.docbot.infodocs.co.za"
      content = "3o2vulhi6gxu4qvhw6ruyweko2xzin47.dkim.amazonses.com"
      type    = "CNAME"
      ttl     = 300
      proxied = false
    }
    "dkim-docbot-2" = {
      name    = "gcbedwyrxt74duti55xggf3kcbbxhe4m._domainkey.docbot.infodocs.co.za"
      content = "gcbedwyrxt74duti55xggf3kcbbxhe4m.dkim.amazonses.com"
      type    = "CNAME"
      ttl     = 300
      proxied = false
    }
    "dkim-docbot-3" = {
      name    = "tb7mdte6a264zms5oxexuflmic4qanqq._domainkey.docbot.infodocs.co.za"
      content = "tb7mdte6a264zms5oxexuflmic4qanqq.dkim.amazonses.com"
      type    = "CNAME"
      ttl     = 300
      proxied = false
    }

    # DKIM Records for veribot subdomain
    "dkim-veribot-1" = {
      name    = "6ulag6fse6qg6hjqzpkwpvzsnp4gkyu4._domainkey.veribot.infodocs.co.za"
      content = "6ulag6fse6qg6hjqzpkwpvzsnp4gkyu4.dkim.amazonses.com"
      type    = "CNAME"
      ttl     = 1800
      proxied = false
    }
    "dkim-veribot-2" = {
      name    = "cq2mtzugpi5bm52pzbfgqalaxdnl5g5i._domainkey.veribot.infodocs.co.za"
      content = "cq2mtzugpi5bm52pzbfgqalaxdnl5g5i.dkim.amazonses.com"
      type    = "CNAME"
      ttl     = 1800
      proxied = false
    }
    "dkim-veribot-3" = {
      name    = "l3pca2kt3tqcvhln5ngtw65acf2k5kam._domainkey.veribot.infodocs.co.za"
      content = "l3pca2kt3tqcvhln5ngtw65acf2k5kam.dkim.amazonses.com"
      type    = "CNAME"
      ttl     = 1800
      proxied = false
    }

    # DKIM Records for mail subdomain
    "dmarc-mail" = {
      name    = "_dmarc.mail.infodocs.co.za"
      content = "_dmarc.crisp.email"
      type    = "CNAME"
      ttl     = 300
      proxied = false
    }
    "dkim-mail-1" = {
      name    = "7v4mbe3hfpfeuanda5mi7qta7qs5cla4._domainkey.mail.infodocs.co.za"
      content = "7v4mbe3hfpfeuanda5mi7qta7qs5cla4.dkim.amazonses.com"
      type    = "CNAME"
      ttl     = 1800
      proxied = false
    }
    "dkim-mail-crisp" = {
      name    = "crisp._domainkey.mail.infodocs.co.za"
      content = "crisp._domainkey.crisp.email"
      type    = "CNAME"
      ttl     = 300
      proxied = false
    }
    "dkim-mail-2" = {
      name    = "m5p7u6yy5xflud3te7swekvlurkcwbbv._domainkey.mail.infodocs.co.za"
      content = "m5p7u6yy5xflud3te7swekvlurkcwbbv.dkim.amazonses.com"
      type    = "CNAME"
      ttl     = 1800
      proxied = false
    }
    "dkim-mail-3" = {
      name    = "wluh5jxqctj5gkrvh7da3kmr6z6d572n._domainkey.mail.infodocs.co.za"
      content = "wluh5jxqctj5gkrvh7da3kmr6z6d572n.dkim.amazonses.com"
      type    = "CNAME"
      ttl     = 1800
      proxied = false
    }
  }

  tags = {
    Environment = "operations"
    Project     = "infodocs"
    ManagedBy   = "opentofu"
    CostCenter  = "infrastructure"
    Owner       = "infodocs-operations"
    Application = "infodocs-infrastructure-cloudflare"
  }
}
