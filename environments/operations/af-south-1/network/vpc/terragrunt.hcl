include {
  path = find_in_parent_folders()
}

terraform {
  source = "../../../../../modules//network/vpc"
}

inputs = {
  name                 = "infodocs-operations"
  cidr                 = "********/16"
  availability_zones   = ["af-south-1a", "af-south-1b", "af-south-1c"]
  private_subnets      = ["********/24", "********/24", "********/24"]
  public_subnets       = ["**********/24", "**********/24", "**********/24"]
  database_subnets     = ["**********/24", "**********/24", "**********/24"]
  enable_nat_gateway   = true # Will enable after EIP limit increase approval
  single_nat_gateway   = true
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = {
    Environment = "operations"
    Project     = "infodocs"
    ManagedBy   = "opentofu"
    CostCenter  = "infrastructure"
    Owner       = "infodocs-operations"
    Application = "infodocs-infrastructure-vpc"
  }
}
