include {
  path = find_in_parent_folders()
}

terraform {
  source = "../../../../../modules//cloudflare/access"
}

inputs = {
  # Basic Configuration
  environment = "operations"

  # Cloudflare Configuration (from SSM)
  cloudflare_account_id = data.aws_ssm_parameter.cloudflare_account_id.value

  # OpenSearch Configuration
  opensearch_hostname = "opensearch.infodocs.co.za"

  # Google Workspace SSO Configuration (from SSM)
  google_client_id       = data.aws_ssm_parameter.google_client_id.value
  google_client_secret   = data.aws_ssm_parameter.google_client_secret.value
  google_workspace_domain = data.aws_ssm_parameter.google_workspace_domain.value

  # WARP Configuration
  warp_connection_id = "" # Will be configured after WARP setup

  # Access Control
  allowed_emails = [
    "<EMAIL>",
    # Add other team members as needed
  ]

  tags = {
    Environment = "operations"
    Project     = "infodocs"
    ManagedBy   = "opentofu"
    Service     = "opensearch-access"
    Purpose     = "zero-trust-authentication"
  }
}
