include {
  path = find_in_parent_folders()
}

terraform {
  source = "../../../../../modules//cloudflare/tunnel"
}

dependency "opensearch" {
  config_path = "../../analytics/opensearch"

  mock_outputs = {
    domain_endpoint = "vpc-infodocs-operations-os-rkzbxjzeuwuyprqaqyk7qy22ky.af-south-1.es.amazonaws.com"
  }
}

dependency "access" {
  config_path = "../access"

  mock_outputs = {
    access_application_aud = "mock-aud-12345"
  }
}

inputs = {
  # Basic Configuration
  name_prefix  = "infodocs-operations"
  environment  = "operations"

  # Cloudflare Configuration (from SSM)
  cloudflare_account_id = data.aws_ssm_parameter.cloudflare_account_id.value
  cloudflare_zone_id    = data.aws_ssm_parameter.cloudflare_zone_id.value
  cloudflare_team_name  = data.aws_ssm_parameter.cloudflare_team_name.value

  # OpenSearch Configuration
  opensearch_hostname          = "opensearch.infodocs.co.za"
  opensearch_internal_endpoint = dependency.opensearch.outputs.domain_endpoint
  access_application_aud       = dependency.access.outputs.access_application_aud

  tags = {
    Environment = "operations"
    Project     = "infodocs"
    ManagedBy   = "opentofu"
    Service     = "opensearch-tunnel"
    Purpose     = "zero-trust-access"
  }
}
