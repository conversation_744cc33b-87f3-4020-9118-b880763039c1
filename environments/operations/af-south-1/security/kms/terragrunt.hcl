include {
  path = find_in_parent_folders()
}

terraform {
  source = "../../../../../modules//security/kms"
}

inputs = {
  name_prefix             = "infodocs-operations"
  environment             = "operations"
  deletion_window_in_days = 7

  # KMS Key Administrators - Full management access
  key_administrators = [
    "arn:aws:iam::${get_aws_account_id()}:user/infrastructure-as-code-kms", # Automation user
    "arn:aws:iam::${get_aws_account_id()}:root"                             # Root account (emergency access)
  ]

  # KMS Key Users - Can encrypt/decrypt data
  key_users = [
    "arn:aws:iam::${get_aws_account_id()}:user/infrastructure-as-code-kms",
    # AWS services will use the key via service-linked roles
  ]

  # CI/CD Access - For pipeline automation
  ci_cd_role_arns = [
    "arn:aws:iam::${get_aws_account_id()}:user/infrastructure-as-code-kms"
  ]

  tags = {
    Environment = "operations"
    Project     = "infodocs"
    ManagedBy   = "opentofu"
    CostCenter  = "infrastructure"
    Owner       = "infodocs-operations"
    Application = "infodocs-platform"
    Purpose     = "encryption-at-rest"
  }
}
