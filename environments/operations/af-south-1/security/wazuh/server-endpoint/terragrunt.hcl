include {
  path = find_in_parent_folders()
}

dependency "vpc" {
  config_path = "../../../network/vpc"

  mock_outputs = {
    vpc_id          = "vpc-********"
    private_subnets = ["subnet-********", "subnet-********", "subnet-********"]
  }
}

dependency "kms" {
  config_path = "../../../security/kms"

  mock_outputs = {
    key_arn = "arn:aws:kms:af-south-1:${get_aws_account_id()}:key/********-1234-1234-1234-************"
    key_id  = "********-1234-1234-1234-************"
  }
}

dependency "ssm" {
  config_path = "../../../security/ssm"

  mock_outputs = {
    parameter_value = "mock-value"
  }
}

terraform {
  source = "../../../../../../modules//security/wazuh"
}

inputs = {
  environment    = "operations"                    # Operations environment
  name_prefix    = "infodocs-central-wazuh-server" # Server endpoint for infrastructure monitoring
  instance_type  = "t3.medium"                     # Good size for central monitoring
  instance_count = 1

  vpc_id    = dependency.vpc.outputs.vpc_id
  subnet_id = dependency.vpc.outputs.private_subnets[0]

  # key_name is now read from SSM Parameter Store via data source
  root_volume_size = 100 # More storage for all environment logs
  wazuh_version    = "4.5.1"

  kms_key_id = dependency.kms.outputs.key_id

  ssm_parameter_path = "/infodocs/wazuh/server"

  enable_cloudwatch_logs   = true
  cloudwatch_log_retention = 120

  allowed_cidr_blocks = ["10.1.0.0/16"] # Only internal VPC access - NO PUBLIC ACCESS

  tags = {
    Environment = "operations"
    Project     = "infodocs"
    ManagedBy   = "opentofu"
    CostCenter  = "infrastructure"
    Owner       = "infodocs-operations"
    Application = "infodocs-infrastructure"
    Purpose     = "infrastructure-security-monitoring"
    Scope       = "server-environment"
  }
}
