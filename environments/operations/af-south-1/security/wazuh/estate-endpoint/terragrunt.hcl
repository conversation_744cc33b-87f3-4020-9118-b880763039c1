include {
  path = find_in_parent_folders()
}

dependency "vpc" {
  config_path = "../../../network/vpc"

  mock_outputs = {
    vpc_id         = "vpc-********"
    public_subnets = ["subnet-********", "subnet-********", "subnet-********"]
  }
}

dependency "kms" {
  config_path = "../../../security/kms"

  mock_outputs = {
    key_arn = "arn:aws:kms:af-south-1:${get_aws_account_id()}:key/********-1234-1234-1234-********9012"
    key_id  = "********-1234-1234-1234-********9012"
  }
}

dependency "ssm" {
  config_path = "../../../security/ssm"

  mock_outputs = {
    parameter_value = "mock-value"
  }
}

terraform {
  source = "../../../../../../modules//security/wazuh"
}

inputs = {
  environment    = "operations"                   # Operations environment
  name_prefix    = "infodocs-estate-wazuh-server" # Estate endpoint for Mac fleet monitoring
  instance_type  = "t3.medium"
  instance_count = 1

  vpc_id    = dependency.vpc.outputs.vpc_id
  subnet_id = dependency.vpc.outputs.public_subnets[0] # Public subnet for Mac fleet access

  # key_name is now read from SSM Parameter Store via data source
  root_volume_size = 100
  wazuh_version    = "4.5.1"

  kms_key_id = dependency.kms.outputs.key_id

  ssm_parameter_path = "/infodocs/wazuh/estate"

  enable_cloudwatch_logs   = true
  cloudwatch_log_retention = 120

  allowed_cidr_blocks = [
    "********/8",  # South African IP space for Mac fleet
    "*********/8", # African IP space
    "*********/8", # Additional SA ranges
    "********/16"  # Internal VPC access
  ]

  tags = {
    Environment = "operations"
    Project     = "infodocs"
    ManagedBy   = "opentofu"
    CostCenter  = "infrastructure"
    Owner       = "infodocs-operations"
    Application = "infodocs-infrastructure"
    Purpose     = "infrastructure-security-monitoring"
    Scope       = "estate-environment"
  }
}
