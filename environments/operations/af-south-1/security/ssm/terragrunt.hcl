include {
  path = find_in_parent_folders()
}

dependency "kms" {
  config_path = "../kms"
}

terraform {
  source = "../../../../../modules//security/ssm"
}

inputs = {
  environment = "operations"
  kms_key_id  = dependency.kms.outputs.key_id

  # Note: This module creates environment-specific parameters only
  # Global secrets are stored in Parameter Store at /infodocs/* paths
  # Run scripts/setup-ssm-secrets.sh to create global parameters first
  parameters = {
    # Environment-specific configuration parameters
    "environment/name" = {
      description = "Environment name for operations"
      value       = "operations"
      secure      = false
    }
    "environment/vpc_cidr" = {
      description = "VPC CIDR for operations environment"
      value       = "********/16"
      secure      = false
    }
    "environment/region" = {
      description = "AWS region for operations environment"
      value       = "af-south-1"
      secure      = false
    }

    # Server Configuration
    "server/main_ip_address" = {
      description = "Main server IP address for infodocs.co.za"
      value       = "***********"
      secure      = false
    }

    # AWS Account Configuration
    "aws/account_id" = {
      description = "AWS Account ID for operations environment"
      value       = "************"
      secure      = false
    }

    # IAM Configuration
    "iam/infrastructure_user_arn" = {
      description = "IAM user ARN for infrastructure automation"
      value       = "arn:aws:iam::************:user/infrastructure-as-code-kms"
      secure      = false
    }

    # Application Configuration for operations
    "app/log_level" = {
      description = "Application log level for operationselopment"
      value       = "DEBUG"
      secure      = false
    }
    "app/debug_mode" = {
      description = "Enable debug mode for operationselopment"
      value       = "true"
      secure      = false
    }

    # operations-specific settings
    "operations/enable_penetration_testing" = {
      description = "Enable penetration testing instances in operations"
      value       = "true"
      secure      = false
    }
    "operations/cost_optimization" = {
      description = "Enable cost optimization features in operations"
      value       = "true"
      secure      = false
    }
  }

  tags = {
    Environment = "operations"
    Project     = "infodocs"
    ManagedBy   = "opentofu"
    CostCenter  = "infrastructure"
    Owner       = "infodocs-operations"
    Application = "infodocs-platform"
  }
}
