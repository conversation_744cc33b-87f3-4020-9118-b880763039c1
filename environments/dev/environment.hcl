# Development environment configuration

locals {
  environment = "dev"

  # Environment-specific settings
  instance_type_default = "t3.small"
  auto_scaling_min      = 1
  auto_scaling_max      = 2

  # Backup and retention settings
  backup_retention_days = 7
  log_retention_days    = 30

  # Security settings
  enable_detailed_monitoring = false
  enable_deletion_protection = false

  # Cost optimization
  enable_auto_scaling      = true
  enable_cost_optimization = true

  # Environment-specific tags
  environment_tags = {
    Environment     = local.environment
    CriticalService = "false"
    DataSensitivity = "low"
  }
}
