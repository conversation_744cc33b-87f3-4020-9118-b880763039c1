include {
  path = find_in_parent_folders()
}

dependency "vpc" {
  config_path = "../vpc"
}

terraform {
  source = "../../../../modules//testing/selenium"
}

inputs = {
  name_prefix = "infodocs-dev"

  vpc_id    = dependency.vpc.outputs.vpc_id
  subnet_id = dependency.vpc.outputs.private_subnets[0]

  instance_type = "t3.medium"

  selenium_version = "4.10.0"
  browser_versions = {
    chrome  = "114.0"
    firefox = "115.0"
  }

  enable_recording = true

  tags = {
    Environment = "dev"
    Project     = "infodocs"
    ManagedBy   = "terragrunt"
  }
}
