include "root" {
  path = find_in_parent_folders()
}

include "env" {
  path = find_in_parent_folders("environment.hcl")
}

terraform {
  source = "../../../../../modules//ec2/kali-linux"
}

dependency "vpc" {
  config_path = "../../network/vpc"

  mock_outputs = {
    vpc_id             = "vpc-dev123"
    vpc_cidr_block     = "10.1.0.0/16"
    private_subnet_ids = ["subnet-dev-priv1", "subnet-dev-priv2"]
    public_subnet_ids  = ["subnet-dev-pub1", "subnet-dev-pub2"]
  }
}

dependency "kms" {
  config_path = "../../security/kms"

  mock_outputs = {
    key_arn = "arn:aws:kms:af-south-1:${get_aws_account_id()}:key/********-1234-1234-1234-************"
    key_id  = "********-1234-1234-1234-************"
  }
}

dependency "bastion" {
  config_path = "../bastion"

  mock_outputs = {
    security_group_id = "sg-dev-bastion"
    instance_id       = "i-dev-bastion"
  }
}

dependency "ssm" {
  config_path = "../../security/ssm"

  mock_outputs = {
    parameters = {
      "ec2/key_name" = "infodocs-dev-infrastructure"
    }
  }
}

inputs = {
  # Basic Configuration
  name_prefix = "infodocs-dev"
  environment = "dev"

  # Network Configuration
  vpc_id                    = dependency.vpc.outputs.vpc_id
  vpc_cidr                  = dependency.vpc.outputs.vpc_cidr_block
  private_subnet_id         = dependency.vpc.outputs.private_subnet_ids[0]
  bastion_security_group_id = dependency.bastion.outputs.security_group_id

  # Security Configuration - Use SSM parameter for key name
  key_name    = dependency.ssm.outputs.parameters["ec2/key_name"]
  kms_key_arn = dependency.kms.outputs.key_arn

  # Instance Configuration
  instance_type    = "t3.large"
  root_volume_size = 60 # Extra space for penetration testing tools

  # Cost Management
  enable_cost_monitoring  = true
  enable_patch_management = true
  cost_alarm_sns_topics   = [] # Add SNS topics when available

  # Testing Configuration
  allowed_testing_cidrs = [
    dependency.vpc.outputs.vpc_cidr_block # Allow testing within VPC
  ]

  tags = {
    Environment = "dev"
    Project     = "infodocs"
    ManagedBy   = "opentofu"
    Purpose     = "penetration-testing"
    Tool        = "kali-linux"
    CostCenter  = "infrastructure"
    Owner       = "devops-team"
    AutoStart   = "false"
    Usage       = "on-demand"
    Application = "infodocs-platform"
  }
}
