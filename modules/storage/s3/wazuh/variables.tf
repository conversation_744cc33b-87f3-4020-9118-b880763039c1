variable "name_prefix" {
  type        = string
  description = "Prefix for resource names"
}

variable "kms_key_arn" {
  type        = string
  description = "ARN of KMS key for encrypting Wazuh logs"
  validation {
    condition     = can(regex("^arn:aws:kms:", var.kms_key_arn))
    error_message = "KMS key ARN must be a valid KMS key ARN."
  }
}

variable "environment" {
  type        = string
  description = "Environment name (dev, prod, operations)"
  validation {
    condition     = contains(["dev", "prod", "operations"], var.environment)
    error_message = "Environment must be one of: dev, prod, operations."
  }
}

variable "wazuh_retention_years" {
  type        = number
  description = "Number of years to retain Wazuh security logs"
  default     = 7
  validation {
    condition     = var.wazuh_retention_years >= 5 && var.wazuh_retention_years <= 10
    error_message = "Wazuh log retention must be between 5 and 10 years for security compliance."
  }
}

variable "enable_cross_region_replication" {
  type        = bool
  description = "Enable cross-region replication for disaster recovery"
  default     = false
}

variable "replication_destination_region" {
  type        = string
  description = "Destination region for cross-region replication"
  default     = "eu-west-1"
}

variable "wazuh_agent_access_cidrs" {
  type        = list(string)
  description = "CIDR blocks allowed for Wazuh agent access"
  default     = []
}

variable "compliance_frameworks" {
  type        = list(string)
  description = "Compliance frameworks for Wazuh logs"
  default     = ["POPIA", "GDPR", "SOX", "HIPAA", "PCI-DSS"]
}

variable "security_classification" {
  type        = string
  description = "Security classification level for Wazuh logs"
  default     = "confidential"
  validation {
    condition     = contains(["internal", "confidential", "restricted", "top-secret"], var.security_classification)
    error_message = "Security classification must be one of: internal, confidential, restricted, top-secret."
  }
}

variable "tags" {
  type        = map(string)
  description = "Tags to apply to all Wazuh S3 resources"
  default     = {}
}
