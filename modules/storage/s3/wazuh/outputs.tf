output "bucket_name" {
  description = "Name of the Wazuh S3 bucket"
  value       = aws_s3_bucket.wazuh.bucket
}

output "bucket_arn" {
  description = "ARN of the Wazuh S3 bucket"
  value       = aws_s3_bucket.wazuh.arn
}

output "bucket_id" {
  description = "ID of the Wazuh S3 bucket"
  value       = aws_s3_bucket.wazuh.id
}

output "bucket_domain_name" {
  description = "Domain name of the Wazuh S3 bucket"
  value       = aws_s3_bucket.wazuh.bucket_domain_name
}

output "iam_policy_arn" {
  description = "ARN of the IAM policy for Wazuh log shipping"
  value       = aws_iam_policy.wazuh_log_shipper.arn
}

output "iam_policy_name" {
  description = "Name of the IAM policy for Wazuh log shipping"
  value       = aws_iam_policy.wazuh_log_shipper.name
}

output "cloudwatch_log_group_name" {
  description = "Name of the CloudWatch log group for S3 access"
  value       = aws_cloudwatch_log_group.wazuh_s3_access.name
}

output "cloudwatch_log_group_arn" {
  description = "ARN of the CloudWatch log group for S3 access"
  value       = aws_cloudwatch_log_group.wazuh_s3_access.arn
}

output "lifecycle_configuration" {
  description = "Wazuh-specific lifecycle policy summary"
  value = {
    purpose                         = "wazuh-security-logs"
    transition_to_ia_days           = 30
    transition_to_glacier_days      = 90
    transition_to_deep_archive_days = 365
    automatic_deletion              = false
    retention_policy                = "indefinite-security-compliance"
  }
}

output "compliance_summary" {
  description = "Wazuh compliance features summary"
  value = {
    security_classification = var.security_classification
    compliance_frameworks   = var.compliance_frameworks
    encryption_enabled      = true
    versioning_enabled      = true
    public_access_blocked   = true
    automatic_deletion      = false
    retention_years         = var.wazuh_retention_years
    purpose                 = "security-monitoring"
  }
}
