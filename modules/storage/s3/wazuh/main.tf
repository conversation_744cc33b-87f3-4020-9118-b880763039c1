# Wazuh-specific S3 bucket for security logs with compliance features
resource "aws_s3_bucket" "wazuh" {
  bucket = "${var.name_prefix}-wazuh"

  tags = merge(
    var.tags,
    {
      Name       = "${var.name_prefix}-wazuh"
      Purpose    = "wazuh-security"
      LogSource  = "wazuh"
      DataClass  = "confidential"
      Compliance = "security-monitoring"
    }
  )
}



# Enable versioning for data protection
resource "aws_s3_bucket_versioning" "wazuh" {
  bucket = aws_s3_bucket.wazuh.id
  versioning_configuration {
    status = "Enabled"
  }
}

# Server-side encryption with KMS
resource "aws_s3_bucket_server_side_encryption_configuration" "wazuh" {
  bucket = aws_s3_bucket.wazuh.id

  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = var.kms_key_arn
      sse_algorithm     = "aws:kms"
    }
    bucket_key_enabled = true
  }
}

# Block all public access (critical for security logs)
resource "aws_s3_bucket_public_access_block" "wazuh" {
  bucket = aws_s3_bucket.wazuh.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# Lifecycle policy optimized for Wazuh security logs (never delete automatically)
resource "aws_s3_bucket_lifecycle_configuration" "wazuh" {
  bucket = aws_s3_bucket.wazuh.id

  rule {
    id     = "wazuh_security_log_retention"
    status = "Enabled"

    filter {
      prefix = "logs/"
    }

    # Transition to IA after 30 days (security logs accessed less frequently)
    transition {
      days          = 30
      storage_class = "STANDARD_IA"
    }

    # Transition to Glacier after 90 days (compliance requirement)
    transition {
      days          = 90
      storage_class = "GLACIER"
    }

    # Transition to Deep Archive after 1 year (long-term compliance)
    transition {
      days          = 365
      storage_class = "DEEP_ARCHIVE"
    }

    # NEVER delete security logs automatically (compliance requirement)
    # No expiration rule for security logs

    # Clean up incomplete multipart uploads
    abort_incomplete_multipart_upload {
      days_after_initiation = 7
    }

    # Version management for compliance
    noncurrent_version_transition {
      noncurrent_days = 30
      storage_class   = "STANDARD_IA"
    }

    noncurrent_version_transition {
      noncurrent_days = 90
      storage_class   = "GLACIER"
    }

    # Keep old versions indefinitely for security compliance
  }
}

# CloudWatch log group for Wazuh S3 access logging
resource "aws_cloudwatch_log_group" "wazuh_s3_access" {
  name              = "/aws/s3/${var.name_prefix}-wazuh-access"
  retention_in_days = 2557 # Closest valid value to 7 years
  # Note: KMS encryption removed for CloudWatch logs to avoid permission complexity
  # The S3 bucket itself is still encrypted with KMS

  tags = var.tags
}

# S3 bucket notification for Wazuh security events (using SNS topic when available)
# Note: CloudWatch configuration is not directly supported in S3 bucket notifications
# This would typically use SNS, SQS, or Lambda function notifications
# resource "aws_s3_bucket_notification" "wazuh_logs" {
#   bucket = aws_s3_bucket.wazuh_logs.id
#
#   topic {
#     topic_arn     = var.sns_topic_arn  # Would need SNS topic for notifications
#     events        = ["s3:ObjectCreated:*"]
#     filter_prefix = "logs/wazuh/"
#   }
# }

# IAM policy for Wazuh log shipping
resource "aws_iam_policy" "wazuh_log_shipper" {
  name        = "${var.name_prefix}-wazuh-s3-access"
  description = "Policy for Wazuh log shipping to S3"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:PutObject",
          "s3:PutObjectAcl",
          "s3:GetObject",
          "s3:ListBucket"
        ]
        Resource = [
          aws_s3_bucket.wazuh.arn,
          "${aws_s3_bucket.wazuh.arn}/*"
        ]
        Condition = {
          StringEquals = {
            "s3:x-amz-server-side-encryption"                = "aws:kms"
            "s3:x-amz-server-side-encryption-aws-kms-key-id" = var.kms_key_arn
          }
        }
      },
      {
        Effect = "Allow"
        Action = [
          "kms:Encrypt",
          "kms:Decrypt",
          "kms:ReEncrypt*",
          "kms:GenerateDataKey*",
          "kms:DescribeKey"
        ]
        Resource = [var.kms_key_arn]
      }
    ]
  })

  tags = var.tags
}
