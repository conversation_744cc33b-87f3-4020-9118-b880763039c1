output "bucket_name" {
  description = "Name of the legal hold S3 bucket"
  value       = aws_s3_bucket.compliance.bucket
}

output "bucket_arn" {
  description = "ARN of the legal hold S3 bucket"
  value       = aws_s3_bucket.compliance.arn
}

output "bucket_id" {
  description = "ID of the legal hold S3 bucket"
  value       = aws_s3_bucket.compliance.id
}

output "iam_policy_arn" {
  description = "ARN of the IAM policy for legal hold access"
  value       = aws_iam_policy.legal_hold_access.arn
}

output "iam_policy_name" {
  description = "Name of the IAM policy for legal hold access"
  value       = aws_iam_policy.legal_hold_access.name
}

output "cloudwatch_log_group_name" {
  description = "Name of the CloudWatch log group for legal hold access"
  value       = aws_cloudwatch_log_group.legal_hold_access.name
}

output "cloudwatch_log_group_arn" {
  description = "ARN of the CloudWatch log group for legal hold access"
  value       = aws_cloudwatch_log_group.legal_hold_access.arn
}

output "object_lock_configuration" {
  description = "Object lock configuration summary"
  value = {
    mode                   = var.object_lock_mode
    default_retention_days = var.default_retention_days
    object_lock_enabled    = true
    immutable_storage      = true
  }
}

output "security_features" {
  description = "Security features enabled for legal hold"
  value = {
    object_lock_enabled      = true
    versioning_enabled       = true
    encryption_enabled       = true
    public_access_blocked    = true
    secure_transport_only    = true
    mfa_delete_required      = var.require_mfa_for_access
    security_monitoring      = var.enable_security_monitoring
    cross_region_replication = var.enable_cross_region_replication
  }
}

output "compliance_summary" {
  description = "Legal hold compliance features"
  value = {
    compliance_frameworks = var.compliance_frameworks
    legal_hold_types      = var.legal_hold_types
    retention_period      = "${var.default_retention_days} days"
    immutable_storage     = true
    audit_trail_enabled   = true
    purpose               = "legal-hold-evidence"
  }
}

output "access_instructions" {
  description = "Instructions for accessing legal hold data"
  value = {
    bucket_name           = aws_s3_bucket.compliance.bucket
    access_method         = "AWS CLI or Console with MFA"
    required_permissions  = "legal-hold-access policy"
    object_lock_mode      = var.object_lock_mode
    retention_period      = "${var.default_retention_days} days"
    security_requirements = "MFA required for all operations"
  }
}
