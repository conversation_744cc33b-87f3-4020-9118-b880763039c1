variable "name_prefix" {
  type        = string
  description = "Prefix for resource names"
}

variable "kms_key_arn" {
  type        = string
  description = "ARN of KMS key for encrypting legal hold data"
  validation {
    condition     = can(regex("^arn:aws:kms:", var.kms_key_arn))
    error_message = "KMS key ARN must be a valid KMS key ARN."
  }
}

variable "environment" {
  type        = string
  description = "Environment name"
}

variable "object_lock_mode" {
  type        = string
  description = "Object lock mode for legal hold"
  default     = "GOVERNANCE"
  validation {
    condition     = contains(["GOVERNANCE", "COMPLIANCE"], var.object_lock_mode)
    error_message = "Object lock mode must be either GOVERNANCE or COMPLIANCE."
  }
}

variable "default_retention_days" {
  type        = number
  description = "Default retention period for legal hold objects (days)"
  default     = 2555 # 7 years
  validation {
    condition     = var.default_retention_days >= 365
    error_message = "Legal hold retention must be at least 1 year (365 days)."
  }
}

variable "enable_security_monitoring" {
  type        = bool
  description = "Enable enhanced security monitoring for legal hold bucket"
  default     = true
}

variable "security_alarm_sns_topics" {
  type        = list(string)
  description = "SNS topic ARNs for security alerts"
  default     = []
}

variable "legal_hold_types" {
  type        = list(string)
  description = "Types of legal holds supported"
  default     = ["litigation", "investigation", "compliance", "audit"]
}

variable "compliance_frameworks" {
  type        = list(string)
  description = "Compliance frameworks for legal hold"
  default     = ["POPIA", "GDPR", "SOX", "LITIGATION", "INVESTIGATION"]
}

variable "authorized_legal_users" {
  type        = list(string)
  description = "List of authorized users for legal hold access"
  default     = []
}

variable "require_mfa_for_access" {
  type        = bool
  description = "Require MFA for legal hold bucket access"
  default     = true
}

variable "enable_cross_region_replication" {
  type        = bool
  description = "Enable cross-region replication for legal hold data"
  default     = true
}

variable "replication_destination_region" {
  type        = string
  description = "Destination region for legal hold replication"
  default     = "eu-west-1"
}

variable "tags" {
  type        = map(string)
  description = "Tags to apply to all legal hold resources"
  default     = {}
}
