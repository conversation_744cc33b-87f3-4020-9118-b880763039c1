# Legal Hold S3 bucket with object lock for litigation and compliance
resource "aws_s3_bucket" "legal_hold" {
  bucket        = "${var.name_prefix}-compliance-${random_id.bucket_suffix.hex}"
  force_destroy = false # Prevent accidental deletion

  tags = merge(
    var.tags,
    {
      Name         = "${var.name_prefix}-compliance"
      Purpose      = "compliance-evidence"
      DataClass    = "restricted"
      Compliance   = "litigation-hold"
      Retention    = "indefinite"
      CriticalData = "true"
    }
  )
}

resource "random_id" "bucket_suffix" {
  byte_length = 4
}

# Enable versioning (required for object lock)
resource "aws_s3_bucket_versioning" "legal_hold" {
  bucket = aws_s3_bucket.legal_hold.id
  versioning_configuration {
    status = "Enabled"
  }
}

# Object lock configuration for legal hold (immutable storage)
resource "aws_s3_bucket_object_lock_configuration" "legal_hold" {
  bucket = aws_s3_bucket.legal_hold.id

  rule {
    default_retention {
      mode = var.object_lock_mode
      days = var.default_retention_days
    }
  }

  object_lock_enabled = "Enabled"
}

# Server-side encryption with KMS
resource "aws_s3_bucket_server_side_encryption_configuration" "legal_hold" {
  bucket = aws_s3_bucket.legal_hold.id

  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = var.kms_key_arn
      sse_algorithm     = "aws:kms"
    }
    bucket_key_enabled = true
  }
}

# Block all public access (critical for legal evidence)
resource "aws_s3_bucket_public_access_block" "legal_hold" {
  bucket = aws_s3_bucket.legal_hold.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# Strict bucket policy for legal hold
resource "aws_s3_bucket_policy" "legal_hold" {
  bucket = aws_s3_bucket.legal_hold.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid       = "DenyInsecureConnections"
        Effect    = "Deny"
        Principal = "*"
        Action    = "s3:*"
        Resource = [
          aws_s3_bucket.legal_hold.arn,
          "${aws_s3_bucket.legal_hold.arn}/*"
        ]
        Condition = {
          Bool = {
            "aws:SecureTransport" = "false"
          }
        }
      },
      {
        Sid       = "RequireSSEKMS"
        Effect    = "Deny"
        Principal = "*"
        Action    = "s3:PutObject"
        Resource  = "${aws_s3_bucket.legal_hold.arn}/*"
        Condition = {
          StringNotEquals = {
            "s3:x-amz-server-side-encryption" = "aws:kms"
          }
        }
      },
      {
        Sid       = "DenyUnencryptedObjectUploads"
        Effect    = "Deny"
        Principal = "*"
        Action    = "s3:PutObject"
        Resource  = "${aws_s3_bucket.legal_hold.arn}/*"
        Condition = {
          StringNotEquals = {
            "s3:x-amz-server-side-encryption-aws-kms-key-id" = var.kms_key_arn
          }
        }
      },
      {
        Sid       = "DenyDeleteWithoutMFA"
        Effect    = "Deny"
        Principal = "*"
        Action = [
          "s3:DeleteObject",
          "s3:DeleteObjectVersion",
          "s3:PutLifecycleConfiguration"
        ]
        Resource = [
          aws_s3_bucket.legal_hold.arn,
          "${aws_s3_bucket.legal_hold.arn}/*"
        ]
        Condition = {
          BoolIfExists = {
            "aws:MultiFactorAuthPresent" = "false"
          }
        }
      }
    ]
  })
}

# CloudWatch log group for legal hold access (extended retention)
resource "aws_cloudwatch_log_group" "legal_hold_access" {
  name              = "/aws/s3/${var.name_prefix}-compliance-access"
  retention_in_days = 2557 # Closest valid value to 7 years for legal compliance
  kms_key_id        = var.kms_key_arn

  tags = var.tags
}

# S3 bucket notification for legal hold events (placeholder for future Lambda/SNS integration)
# Note: CloudWatch configurations are not supported in S3 bucket notifications
# This will be configured when Lambda log shippers and SNS topics are added

# IAM policy for legal hold access (very restricted)
resource "aws_iam_policy" "legal_hold_access" {
  name        = "${var.name_prefix}-compliance-access"
  description = "Restricted access policy for legal hold bucket"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:PutObject",
          "s3:PutObjectLegalHold",
          "s3:PutObjectRetention",
          "s3:GetObject",
          "s3:GetObjectLegalHold",
          "s3:GetObjectRetention",
          "s3:ListBucket"
        ]
        Resource = [
          aws_s3_bucket.legal_hold.arn,
          "${aws_s3_bucket.legal_hold.arn}/*"
        ]
        Condition = {
          StringEquals = {
            "s3:x-amz-server-side-encryption"                = "aws:kms"
            "s3:x-amz-server-side-encryption-aws-kms-key-id" = var.kms_key_arn
          }
        }
      },
      {
        Effect = "Allow"
        Action = [
          "kms:Encrypt",
          "kms:Decrypt",
          "kms:ReEncrypt*",
          "kms:GenerateDataKey*",
          "kms:DescribeKey"
        ]
        Resource = [var.kms_key_arn]
      }
    ]
  })

  tags = var.tags
}

# CloudWatch alarm for unauthorized access attempts
resource "aws_cloudwatch_metric_alarm" "legal_hold_unauthorized_access" {
  count = var.enable_security_monitoring ? 1 : 0

  alarm_name          = "${var.name_prefix}-compliance-unauthorized-access"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "4xxError"
  namespace           = "AWS/S3"
  period              = "300"
  statistic           = "Sum"
  threshold           = "5"
  alarm_description   = "Unauthorized access attempts to legal hold bucket"
  alarm_actions       = var.security_alarm_sns_topics

  dimensions = {
    BucketName = aws_s3_bucket.legal_hold.bucket
  }

  tags = var.tags
}
