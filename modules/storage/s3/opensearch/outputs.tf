output "bucket_name" {
  description = "Name of the OpenSearch S3 logs bucket"
  value       = aws_s3_bucket.opensearch.bucket
}

output "bucket_arn" {
  description = "ARN of the OpenSearch S3 logs bucket"
  value       = aws_s3_bucket.opensearch.arn
}

output "bucket_id" {
  description = "ID of the OpenSearch S3 logs bucket"
  value       = aws_s3_bucket.opensearch.id
}

output "bucket_domain_name" {
  description = "Domain name of the OpenSearch S3 bucket"
  value       = aws_s3_bucket.opensearch.bucket_domain_name
}

output "iam_policy_arn" {
  description = "ARN of the IAM policy for OpenSearch log shipping"
  value       = aws_iam_policy.opensearch_log_shipper.arn
}

output "iam_policy_name" {
  description = "Name of the IAM policy for OpenSearch log shipping"
  value       = aws_iam_policy.opensearch_log_shipper.name
}

output "cloudwatch_log_group_name" {
  description = "Name of the CloudWatch log group for S3 access"
  value       = aws_cloudwatch_log_group.opensearch_s3_access.name
}

output "cloudwatch_log_group_arn" {
  description = "ARN of the CloudWatch log group for S3 access"
  value       = aws_cloudwatch_log_group.opensearch_s3_access.arn
}

output "lifecycle_configuration" {
  description = "OpenSearch-specific lifecycle policy summary"
  value = {
    purpose                         = "opensearch-application-logs"
    transition_to_ia_days           = 30
    transition_to_glacier_days      = 90
    transition_to_deep_archive_days = 365
    automatic_deletion              = var.enable_automatic_deletion
    retention_days                  = var.retention_days
    intelligent_tiering             = var.enable_intelligent_tiering
  }
}

output "compliance_summary" {
  description = "OpenSearch compliance features summary"
  value = {
    data_classification   = var.data_classification
    compliance_frameworks = var.compliance_frameworks
    encryption_enabled    = true
    versioning_enabled    = true
    public_access_blocked = true
    automatic_deletion    = var.enable_automatic_deletion
    retention_days        = var.retention_days
    log_types             = var.log_types
    purpose               = "application-monitoring"
  }
}

output "cost_optimization" {
  description = "Cost optimization features for OpenSearch logs"
  value = {
    intelligent_tiering_enabled = var.enable_intelligent_tiering
    lifecycle_transitions       = true
    automatic_cleanup           = var.enable_automatic_deletion
    storage_classes             = ["STANDARD", "STANDARD_IA", "GLACIER", "DEEP_ARCHIVE"]
  }
}
