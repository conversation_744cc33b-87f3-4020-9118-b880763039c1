# OpenSearch-specific S3 bucket for application logs with cost optimization
resource "aws_s3_bucket" "opensearch" {
  bucket = "${var.name_prefix}-opensearch"

  tags = merge(
    var.tags,
    {
      Name       = "${var.name_prefix}-opensearch"
      Purpose    = "opensearch-application"
      LogSource  = "opensearch"
      DataClass  = "confidential"
      Compliance = "application-monitoring"
    }
  )
}



# Enable versioning for data protection
resource "aws_s3_bucket_versioning" "opensearch" {
  bucket = aws_s3_bucket.opensearch.id
  versioning_configuration {
    status = "Enabled"
  }
}

# Server-side encryption with KMS
resource "aws_s3_bucket_server_side_encryption_configuration" "opensearch" {
  bucket = aws_s3_bucket.opensearch.id

  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = var.kms_key_arn
      sse_algorithm     = "aws:kms"
    }
    bucket_key_enabled = true
  }
}

# Block all public access
resource "aws_s3_bucket_public_access_block" "opensearch" {
  bucket = aws_s3_bucket.opensearch.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# Lifecycle policy optimized for OpenSearch application logs (cost-focused)
resource "aws_s3_bucket_lifecycle_configuration" "opensearch" {
  bucket = aws_s3_bucket.opensearch.id

  rule {
    id     = "opensearch_application_log_retention"
    status = "Enabled"

    filter {
      prefix = "logs/"
    }

    # Transition to IA after 90 days (application logs accessed occasionally)
    transition {
      days          = 90
      storage_class = "STANDARD_IA"
    }

    # Transition to Glacier after 120 days (cost optimization)
    transition {
      days          = 120
      storage_class = "GLACIER"
    }

    # Transition to Deep Archive after 1 year (long-term storage)
    transition {
      days          = 365
      storage_class = "DEEP_ARCHIVE"
    }

    # Optional: Delete after retention period (configurable for application logs)
    dynamic "expiration" {
      for_each = var.enable_automatic_deletion ? [1] : []
      content {
        days = var.retention_days
      }
    }

    # Clean up incomplete multipart uploads
    abort_incomplete_multipart_upload {
      days_after_initiation = 7
    }

    # Version management
    noncurrent_version_transition {
      noncurrent_days = 90
      storage_class   = "STANDARD_IA"
    }

    noncurrent_version_transition {
      noncurrent_days = 120
      storage_class   = "GLACIER"
    }

    dynamic "noncurrent_version_expiration" {
      for_each = var.enable_automatic_deletion ? [1] : []
      content {
        noncurrent_days = var.retention_days
      }
    }
  }
}

# CloudWatch log group for OpenSearch S3 access logging
resource "aws_cloudwatch_log_group" "opensearch_s3_access" {
  name              = "/aws/s3/${var.name_prefix}-opensearch-access"
  retention_in_days = var.access_log_retention_days
  # Note: KMS encryption removed for CloudWatch logs to avoid permission complexity
  # The S3 bucket itself is still encrypted with KMS

  tags = var.tags
}

# S3 bucket notification to trigger Lambda when log files are created
resource "aws_s3_bucket_notification" "opensearch_lambda_trigger" {
  count  = var.lambda_function_arn != "" ? 1 : 0
  bucket = aws_s3_bucket.opensearch.id

  lambda_function {
    lambda_function_arn = var.lambda_function_arn
    events              = ["s3:ObjectCreated:*"]
    filter_prefix       = "cloudwatch-logs/"
    filter_suffix       = ".gz"
  }

  depends_on = [aws_s3_bucket.opensearch]
}

# S3 bucket policy to allow CloudWatch Logs service to write exports
resource "aws_s3_bucket_policy" "opensearch_cloudwatch_logs" {
  bucket = aws_s3_bucket.opensearch.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "AllowCloudWatchLogsGetBucketAcl"
        Effect = "Allow"
        Principal = {
          Service = "logs.amazonaws.com"
        }
        Action   = "s3:GetBucketAcl"
        Resource = aws_s3_bucket.opensearch.arn
      },
      {
        Sid    = "AllowCloudWatchLogsPutObject"
        Effect = "Allow"
        Principal = {
          Service = "logs.amazonaws.com"
        }
        Action   = "s3:PutObject"
        Resource = "${aws_s3_bucket.opensearch.arn}/*"
        Condition = {
          StringEquals = {
            "s3:x-amz-acl" = "bucket-owner-full-control"
          }
        }
      },
      {
        Sid    = "AllowCloudWatchLogsNonKMSPutObject"
        Effect = "Allow"
        Principal = {
          Service = "logs.amazonaws.com"
        }
        Action   = "s3:PutObject"
        Resource = "${aws_s3_bucket.opensearch.arn}/cloudwatch-logs/*"
      }
    ]
  })
}

# IAM policy for OpenSearch log shipping
resource "aws_iam_policy" "opensearch_log_shipper" {
  name        = "${var.name_prefix}-opensearch-s3-access"
  description = "Policy for OpenSearch log shipping to S3"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:PutObject",
          "s3:PutObjectAcl",
          "s3:GetObject",
          "s3:ListBucket",
          "s3:DeleteObject" # Allow deletion for application logs (unlike security logs)
        ]
        Resource = [
          aws_s3_bucket.opensearch.arn,
          "${aws_s3_bucket.opensearch.arn}/*"
        ]
        Condition = {
          StringEquals = {
            "s3:x-amz-server-side-encryption"                = "aws:kms"
            "s3:x-amz-server-side-encryption-aws-kms-key-id" = var.kms_key_arn
          }
        }
      },
      {
        Effect = "Allow"
        Action = [
          "kms:Encrypt",
          "kms:Decrypt",
          "kms:ReEncrypt*",
          "kms:GenerateDataKey*",
          "kms:DescribeKey"
        ]
        Resource = [var.kms_key_arn]
      }
    ]
  })

  tags = var.tags
}

# Intelligent Tiering for cost optimization (OpenSearch-specific)
resource "aws_s3_bucket_intelligent_tiering_configuration" "opensearch" {
  count  = var.enable_intelligent_tiering ? 1 : 0
  bucket = aws_s3_bucket.opensearch.id
  name   = "opensearch-intelligent-tiering"

  filter {
    prefix = "logs/opensearch/"
  }

  tiering {
    access_tier = "ARCHIVE_ACCESS"
    days        = 90
  }

  tiering {
    access_tier = "DEEP_ARCHIVE_ACCESS"
    days        = 180
  }
}
