variable "name_prefix" {
  type        = string
  description = "Prefix for resource names"
}

variable "kms_key_arn" {
  type        = string
  description = "ARN of KMS key for encrypting OpenSearch logs"
  validation {
    condition     = can(regex("^arn:aws:kms:", var.kms_key_arn))
    error_message = "KMS key ARN must be a valid KMS key ARN."
  }
}

variable "environment" {
  type        = string
  description = "Environment name (dev, prod, operations)"
  validation {
    condition     = contains(["dev", "prod", "operations"], var.environment)
    error_message = "Environment must be one of: dev, prod, operations."
  }
}

variable "retention_days" {
  type        = number
  description = "Number of days to retain OpenSearch logs before deletion"
  default     = 2555 # 7 years
  validation {
    condition     = var.retention_days >= 365
    error_message = "OpenSearch log retention must be at least 1 year (365 days)."
  }
}

variable "enable_automatic_deletion" {
  type        = bool
  description = "Enable automatic deletion after retention period for application logs"
  default     = false
}

variable "access_log_retention_days" {
  type        = number
  description = "Retention period for S3 access logs"
  default     = 365 # 1 year for application logs
}

variable "enable_intelligent_tiering" {
  type        = bool
  description = "Enable S3 Intelligent Tiering for cost optimization"
  default     = true
}

variable "enable_cross_region_replication" {
  type        = bool
  description = "Enable cross-region replication for disaster recovery"
  default     = false
}

variable "replication_destination_region" {
  type        = string
  description = "Destination region for cross-region replication"
  default     = "eu-west-1"
}

variable "opensearch_cluster_access_cidrs" {
  type        = list(string)
  description = "CIDR blocks allowed for OpenSearch cluster access"
  default     = []
}

variable "compliance_frameworks" {
  type        = list(string)
  description = "Compliance frameworks for OpenSearch logs"
  default     = ["POPIA", "GDPR", "SOX"]
}

variable "data_classification" {
  type        = string
  description = "Data classification level for OpenSearch logs"
  default     = "confidential"
  validation {
    condition     = contains(["public", "internal", "confidential", "restricted"], var.data_classification)
    error_message = "Data classification must be one of: public, internal, confidential, restricted."
  }
}

variable "log_types" {
  type        = list(string)
  description = "Types of logs stored in this bucket"
  default     = ["application", "search", "index", "cluster"]
}

variable "tags" {
  type        = map(string)
  description = "Tags to apply to all OpenSearch S3 resources"
  default     = {}
}

variable "lambda_function_arn" {
  type        = string
  description = "ARN of the Lambda function to trigger on S3 events"
  default     = ""
}
