data "aws_ami" "amazon_linux_2" {
  most_recent = true
  owners      = ["amazon"]

  filter {
    name   = "name"
    values = ["amzn2-ami-hvm-*-x86_64-gp2"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }
}

data "aws_ssm_parameter" "wazuh_admin_password" {
  name = "${var.ssm_parameter_path}/admin_password"
}

data "aws_ssm_parameter" "wazuh_api_key" {
  name = "${var.ssm_parameter_path}/api_key"
}

data "aws_kms_key" "ssm" {
  key_id = var.kms_key_id
}
