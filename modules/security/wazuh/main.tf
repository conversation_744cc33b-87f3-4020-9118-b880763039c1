# Data source to read SSH key name from SSM Parameter Store
data "aws_ssm_parameter" "ssh_key_name" {
  name = "/infodocs/${var.environment}/ec2/key_name"
}

resource "aws_instance" "wazuh_server" {
  count = var.instance_count

  ami                    = var.ami_id != "" ? var.ami_id : data.aws_ami.amazon_linux_2.id
  instance_type          = var.instance_type
  subnet_id              = var.subnet_id
  vpc_security_group_ids = [aws_security_group.wazuh.id]
  key_name               = data.aws_ssm_parameter.ssh_key_name.value

  root_block_device {
    volume_size = var.root_volume_size
    volume_type = "gp3"
    encrypted   = true
    kms_key_id  = var.kms_key_id
  }

  user_data = templatefile("${path.module}/templates/user_data.sh.tpl", {
    wazuh_version  = var.wazuh_version
    admin_password = data.aws_ssm_parameter.wazuh_admin_password.value
    api_key        = data.aws_ssm_parameter.wazuh_api_key.value
  })

  # Security: Require IMDSv2 for enhanced security
  metadata_options {
    http_endpoint               = "enabled"
    http_tokens                 = "required" # Require IMDSv2
    http_put_response_hop_limit = 1
  }

  tags = merge(
    var.tags,
    {
      Name = "${var.name_prefix}-wazuh-server-${count.index + 1}"
    }
  )
}

resource "aws_security_group" "wazuh" {
  name        = "${var.name_prefix}-wazuh-sg"
  description = "Security group for Wazuh server"
  vpc_id      = var.vpc_id

  # SSH access
  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = var.allowed_cidr_blocks
    description = "SSH access"
  }

  # Wazuh Web UI
  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = var.allowed_cidr_blocks
    description = "HTTPS for Wazuh Web UI"
  }

  # Wazuh API
  ingress {
    from_port   = 55000
    to_port     = 55000
    protocol    = "tcp"
    cidr_blocks = var.allowed_cidr_blocks
    description = "Wazuh API"
  }

  # Wazuh agent registration service
  ingress {
    from_port   = 1515
    to_port     = 1515
    protocol    = "tcp"
    cidr_blocks = var.allowed_cidr_blocks
    description = "Wazuh agent registration service"
  }

  # Wazuh agent connection service
  ingress {
    from_port   = 1514
    to_port     = 1514
    protocol    = "tcp"
    cidr_blocks = var.allowed_cidr_blocks
    description = "Wazuh agent connection service"
  }

  # SSH access
  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = var.allowed_cidr_blocks
    description = "SSH access"
  }

  # Allow all outbound traffic
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
  }

  tags = merge(
    var.tags,
    {
      Name = "${var.name_prefix}-wazuh-sg"
    }
  )
}

# CloudWatch Log Group for Wazuh
resource "aws_cloudwatch_log_group" "wazuh" {
  count = var.enable_cloudwatch_logs ? 1 : 0

  name              = "/aws/ec2/${var.name_prefix}-wazuh"
  retention_in_days = var.cloudwatch_log_retention
  # Note: KMS encryption removed for CloudWatch logs to avoid permission complexity
  # The EC2 instance and EBS volumes are still encrypted with KMS

  tags = var.tags
}
