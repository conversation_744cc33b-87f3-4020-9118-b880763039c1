variable "instance_type" {
  type        = string
  description = "Instance type for Wazuh manager"
  validation {
    condition     = contains(["t3.medium", "t3.large", "m5.large", "m5.xlarge"], var.instance_type)
    error_message = "Instance type must be one of: t3.medium, t3.large, m5.large, m5.xlarge."
  }
}

variable "instance_count" {
  type        = number
  description = "Number of Wazuh instances to deploy"
  default     = 1
  validation {
    condition     = var.instance_count > 0 && var.instance_count <= 3
    error_message = "Instance count must be between 1 and 3."
  }
}

variable "subnet_id" {
  type        = string
  description = "Subnet ID for Wazuh instance"
  validation {
    condition     = can(regex("^subnet-[a-z0-9]+$", var.subnet_id))
    error_message = "Subnet ID must be a valid subnet identifier."
  }
}

variable "vpc_id" {
  type        = string
  description = "VPC ID where Wazuh will be deployed"
}

variable "ami_id" {
  type        = string
  description = "AMI ID for Wazuh instance (leave empty to use latest Amazon Linux 2)"
  default     = ""
}

# key_name is now read from SSM Parameter Store via data source

variable "root_volume_size" {
  type        = number
  description = "Root volume size in GB"
  default     = 50
}

variable "wazuh_version" {
  type        = string
  description = "Wazuh version to install"
  default     = "4.5.1"
}

variable "environment" {
  type        = string
  description = "Environment name (dev, prod, operations)"
  validation {
    condition     = contains(["dev", "prod", "operations"], var.environment)
    error_message = "Environment must be one of: dev, prod, operations."
  }
}

variable "name_prefix" {
  type        = string
  description = "Prefix for resource names"
}

# KMS related variables
variable "kms_key_id" {
  type        = string
  description = "KMS key ID for EBS volume encryption"
  validation {
    condition     = can(regex("^(arn:aws:kms:|alias/|key/|[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})[a-zA-Z0-9:/_-]*$", var.kms_key_id))
    error_message = "KMS key ID must be a valid KMS key identifier, ARN, alias, or UUID."
  }
}

variable "ssm_parameter_path" {
  type        = string
  description = "SSM parameter path prefix for Wazuh secrets"
  default     = "/infodocs/wazuh"
  validation {
    condition     = can(regex("^/[a-zA-Z0-9/_-]+$", var.ssm_parameter_path))
    error_message = "SSM parameter path must start with / and contain only alphanumeric characters, hyphens, and underscores."
  }
}

variable "enable_cloudwatch_logs" {
  type        = bool
  description = "Enable CloudWatch logs for Wazuh"
  default     = true
}

variable "cloudwatch_log_retention" {
  type        = number
  description = "Retention period in days for CloudWatch logs"
  default     = 30
}

variable "allowed_cidr_blocks" {
  type        = list(string)
  description = "CIDR blocks allowed to access Wazuh UI - INTERNAL ONLY"
  default     = [] # No default public access - must be explicitly set
  validation {
    condition     = !contains(var.allowed_cidr_blocks, "0.0.0.0/0")
    error_message = "Public access (0.0.0.0/0) is not allowed for security reasons. Use specific internal CIDR blocks only."
  }
}

variable "tags" {
  type        = map(string)
  description = "Tags for Wazuh instance"
  default     = {}
}
