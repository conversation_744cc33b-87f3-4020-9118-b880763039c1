output "instance_ids" {
  description = "IDs of the Wazuh instances"
  value       = aws_instance.wazuh_server[*].id
}

output "instance_private_ips" {
  description = "Private IPs of the Wazuh instances"
  value       = aws_instance.wazuh_server[*].private_ip
}

output "security_group_id" {
  description = "ID of the Wazuh security group"
  value       = aws_security_group.wazuh.id
}

output "cloudwatch_log_group_name" {
  description = "Name of the CloudWatch log group for Wazuh"
  value       = var.enable_cloudwatch_logs ? aws_cloudwatch_log_group.wazuh[0].name : null
}
