#!/bin/bash
# Wazuh installation script

# Update system packages
yum update -y
yum install -y wget curl

# Install Wazuh ${wazuh_version}
curl -sO https://packages.wazuh.com/4.5/wazuh-install.sh
chmod +x wazuh-install.sh
./wazuh-install.sh -a

# Configure API key
echo "${api_key}" > /var/ossec/etc/api_key.txt
chown ossec:ossec /var/ossec/etc/api_key.txt
chmod 640 /var/ossec/etc/api_key.txt

# Restart Wazuh services
systemctl restart wazuh-manager
systemctl restart wazuh-api

echo "Wazuh installation completed"
