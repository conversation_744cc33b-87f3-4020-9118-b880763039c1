variable "name_prefix" {
  type        = string
  description = "Prefix for KMS key name"
}

variable "deletion_window_in_days" {
  type        = number
  description = "Duration in days after which the key is deleted after destruction of the resource"
  default     = 30
}

variable "ci_cd_role_arns" {
  type        = list(string)
  description = "List of IAM role ARNs that should have access to the KMS key for CI/CD purposes"
  default     = []
}

variable "key_administrators" {
  type        = list(string)
  description = "List of IAM ARNs for KMS key administrators (full management access). Should include all admin users who need to manage the key."
  default     = []

  validation {
    condition     = length(var.key_administrators) > 0
    error_message = "At least one key administrator must be specified for security and management purposes."
  }
}

variable "key_users" {
  type        = list(string)
  description = "List of IAM ARNs for KMS key users (encrypt/decrypt access). Should include users and services that need to use the key for encryption operations."
  default     = []
}

variable "tags" {
  type        = map(string)
  description = "Tags to apply to the KMS key"
  default     = {}
}

variable "environment" {
  description = "Environment name (dev, prod, operations)"
  type        = string
  validation {
    condition     = contains(["dev", "prod", "operations"], var.environment)
    error_message = "Environment must be one of: dev, prod, operations."
  }
}
