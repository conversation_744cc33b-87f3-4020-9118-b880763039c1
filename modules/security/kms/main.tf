resource "aws_kms_key" "this" {
  description             = "KMS key for ${var.name_prefix} encryption"
  deletion_window_in_days = var.deletion_window_in_days
  enable_key_rotation     = true
  policy                  = data.aws_iam_policy_document.kms_policy.json

  tags = merge(
    var.tags,
    {
      Name        = "${var.name_prefix}-key"
      Environment = var.environment
      Project     = "infodocs"
      ManagedBy   = "opentofu"
      CostCenter  = "infrastructure"
    }
  )
}

resource "aws_kms_alias" "this" {
  name          = "alias/${var.name_prefix}-key"
  target_key_id = aws_kms_key.this.key_id
}

data "aws_iam_policy_document" "kms_policy" {
  # KMS Key Policy Structure:
  # 1. Root account can update policies (emergency access)
  # 2. Key administrators have full management access
  # 3. Key users have encrypt/decrypt access
  # 4. CI/CD roles have automation access

  # Statement to allow policy updates (emergency access via root account)
  statement {
    sid    = "Enable Key Policy Updates"
    effect = "Allow"
    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"]
    }
    actions   = ["kms:UpdateKeyPolicy", "kms:PutKeyPolicy"]
    resources = ["*"]
  }

  statement {
    sid    = "Allow Key Administrators"
    effect = "Allow"
    principals {
      type        = "AWS"
      identifiers = var.key_administrators
    }
    actions = [
      "kms:Create*",
      "kms:Describe*",
      "kms:Enable*",
      "kms:List*",
      "kms:Put*",
      "kms:Update*",
      "kms:Revoke*",
      "kms:Disable*",
      "kms:Get*",
      "kms:Delete*",
      "kms:TagResource",
      "kms:UntagResource",
      "kms:ScheduleKeyDeletion",
      "kms:CancelKeyDeletion"
    ]
    resources = ["*"]
  }

  statement {
    sid    = "Allow Key Users"
    effect = "Allow"
    principals {
      type        = "AWS"
      identifiers = var.key_users
    }
    actions = [
      "kms:Encrypt",
      "kms:Decrypt",
      "kms:ReEncrypt*",
      "kms:GenerateDataKey*",
      "kms:DescribeKey"
    ]
    resources = ["*"]
  }

  statement {
    sid    = "Allow CI/CD Access"
    effect = "Allow"
    principals {
      type        = "AWS"
      identifiers = length(var.ci_cd_role_arns) > 0 ? var.ci_cd_role_arns : []
    }
    actions = [
      "kms:Encrypt",
      "kms:Decrypt",
      "kms:ReEncrypt*",
      "kms:GenerateDataKey*",
      "kms:DescribeKey"
    ]
    resources = ["*"]
  }

  # Statement to allow EC2 service for EBS encryption
  statement {
    sid    = "Allow EC2 Service"
    effect = "Allow"
    principals {
      type        = "Service"
      identifiers = ["ec2.amazonaws.com"]
    }
    actions = [
      "kms:Encrypt",
      "kms:Decrypt",
      "kms:ReEncrypt*",
      "kms:GenerateDataKey*",
      "kms:DescribeKey"
    ]
    resources = ["*"]
  }

  # Statement to allow CloudWatch Logs service for log exports
  statement {
    sid    = "Allow CloudWatch Logs Service"
    effect = "Allow"
    principals {
      type        = "Service"
      identifiers = ["logs.amazonaws.com"]
    }
    actions = [
      "kms:Encrypt",
      "kms:Decrypt",
      "kms:ReEncrypt*",
      "kms:GenerateDataKey*",
      "kms:DescribeKey"
    ]
    resources = ["*"]
  }

  # Statement to allow all IAM roles in account for log processing
  statement {
    sid    = "Allow Account Roles"
    effect = "Allow"
    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"]
    }
    actions = [
      "kms:Decrypt",
      "kms:DescribeKey"
    ]
    resources = ["*"]
    condition {
      test     = "StringEquals"
      variable = "kms:ViaService"
      values   = ["s3.af-south-1.amazonaws.com"]
    }
  }
}

data "aws_caller_identity" "current" {}
