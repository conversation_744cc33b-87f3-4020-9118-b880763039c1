# KMS Module

This module creates AWS KMS (Key Management Service) keys for encrypting sensitive data across the InfoDocs platform.

## Features

- **Admin-friendly access control** - All administrators can manage keys
- **Dedicated encryption keys** for each environment
- **Automatic key rotation** for enhanced security
- **Fine-grained access control** via IAM policies
- **Key aliases** for easy reference
- **CI/CD integration** with role-based access
- **Multi-user support** - Multiple admins can access the same key

## Access Control Model

### Key Administrators
- **Full management access** to the KMS key
- Can create, modify, delete, and manage key policies
- Should include all infrastructure administrators
- **Required**: At least one administrator must be specified

### Key Users
- **Encrypt/decrypt access** for day-to-day operations
- Can use the key for encryption and decryption operations
- Includes both human users and service accounts

### CI/CD Roles
- **Automation access** for pipeline operations
- Limited to encryption/decryption operations needed for automation

## Usage

### Admin-Friendly Configuration

```hcl
module "kms" {
  source = "../../modules/security/kms"

  name_prefix = "infodocs-dev"
  environment = "dev"

  # Include ALL administrators who need access
  key_administrators = [
    "arn:aws:iam::${data.aws_caller_identity.current.account_id}:user/juandre",
    "arn:aws:iam::${data.aws_caller_identity.current.account_id}:user/infrastructure-as-code-kms",
    "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
  ]

  # Include users who need to encrypt/decrypt data
  key_users = [
    "arn:aws:iam::${data.aws_caller_identity.current.account_id}:user/juandre",
    "arn:aws:iam::${data.aws_caller_identity.current.account_id}:user/infrastructure-as-code-kms"
  ]

  # Automation access for pipelines
  ci_cd_role_arns = [
    "arn:aws:iam::${data.aws_caller_identity.current.account_id}:user/infrastructure-as-code-kms"
  ]
}

  name_prefix            = "infodocs-dev"
  environment            = "dev"
  deletion_window_in_days = 7
  ci_cd_role_arns        = ["arn:aws:iam::************:role/infodocs-cicd-role"]

  tags = {
    Project     = "infodocs"
    CostCenter  = "infrastructure"
  }
}
```

### Production Usage

```hcl
module "kms" {
  source = "../../modules/kms"

  name_prefix            = "infodocs-prod"
  environment            = "prod"
  deletion_window_in_days = 30  # Longer recovery window for production
  ci_cd_role_arns        = ["arn:aws:iam::************:role/infodocs-cicd-role"]

  tags = {
    Project     = "infodocs"
    CostCenter  = "infrastructure"
    CriticalResource = "true"
  }
}
```

## Integration with Other Modules

### SSM Module
```hcl
module "ssm" {
  source = "../../modules/ssm"

  environment = "dev"
  kms_key_id  = module.kms.key_id

  # Parameters definition...
}
```

### Wazuh Module
```hcl
module "wazuh" {
  source = "../../modules/wazuh"

  # Other parameters...
  kms_key_id = module.kms.key_id
}
```

### OpenSearch Module
```hcl
module "opensearch" {
  source = "../../modules/opensearch"

  # Other parameters...
  kms_key_arn = module.kms.key_arn
}
```

## Security Considerations

- KMS keys cannot be recovered after deletion
- Set appropriate deletion window (7-30 days)
- Use key policies to restrict access
- Enable CloudTrail for auditing key usage

## Cost Considerations

- **Key Storage**: ~$1/month per key
- **API Requests**: $0.03 per 10,000 requests
- **Total Estimated Cost**: $1-5/month per environment

## Compliance

- **POPIA**: Data encrypted at rest within South African region
- **GDPR**: Encryption satisfies data protection requirements
- **Best Practices**: Follows AWS security best practices
<!-- BEGINNING OF PRE-COMMIT-TERRAFORM DOCS HOOK -->
## Requirements

No requirements.

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | 6.0.0 |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [aws_kms_alias.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/kms_alias) | resource |
| [aws_kms_key.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/kms_key) | resource |
| [aws_caller_identity.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/caller_identity) | data source |
| [aws_iam_policy_document.kms_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_ci_cd_role_arns"></a> [ci\_cd\_role\_arns](#input\_ci\_cd\_role\_arns) | List of IAM role ARNs that should have access to the KMS key for CI/CD purposes | `list(string)` | `[]` | no |
| <a name="input_deletion_window_in_days"></a> [deletion\_window\_in\_days](#input\_deletion\_window\_in\_days) | Duration in days after which the key is deleted after destruction of the resource | `number` | `30` | no |
| <a name="input_environment"></a> [environment](#input\_environment) | Environment name (dev, staging, prod) | `string` | n/a | yes |
| <a name="input_name_prefix"></a> [name\_prefix](#input\_name\_prefix) | Prefix for KMS key name | `string` | n/a | yes |
| <a name="input_tags"></a> [tags](#input\_tags) | Tags to apply to the KMS key | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_alias_arn"></a> [alias\_arn](#output\_alias\_arn) | The ARN of the KMS alias |
| <a name="output_alias_name"></a> [alias\_name](#output\_alias\_name) | The alias name of the KMS key |
| <a name="output_key_arn"></a> [key\_arn](#output\_key\_arn) | The ARN of the KMS key |
| <a name="output_key_id"></a> [key\_id](#output\_key\_id) | The ID of the KMS key |
<!-- END OF PRE-COMMIT-TERRAFORM DOCS HOOK -->
