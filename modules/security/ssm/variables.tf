
variable "environment" {
  type        = string
  description = "Environment name (dev, prod, operations)"
  validation {
    condition     = contains(["dev", "prod", "operations"], var.environment)
    error_message = "Environment must be one of: dev, prod, operations."
  }
}

variable "kms_key_id" {
  type        = string
  description = "KMS key ID for encrypting secure parameters"
}

variable "parameters" {
  type = map(object({
    description = string
    value       = string
    secure      = bool
  }))
  description = "Map of parameters to create in SSM Parameter Store"
}

variable "tags" {
  type        = map(string)
  description = "Tags to apply to SSM parameters"
  default     = {}
}
