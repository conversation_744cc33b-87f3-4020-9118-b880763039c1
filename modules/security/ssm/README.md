# SSM Parameter Store Module

This module manages AWS Systems Manager Parameter Store parameters for storing configuration and secrets securely.

## Features

- **Hierarchical parameter organization** by environment and service
- **KMS encryption** for secure parameters
- **Standardized naming convention** for easy discovery
- **Tagging** for resource management and cost allocation

## Usage

### Basic Usage

```hcl
module "ssm" {
  source = "../../modules/ssm"

  environment = "dev"
  kms_key_id  = module.kms.key_id

  parameters = {
    "wazuh/admin_credentials" = {
      description = "Wazuh admin credentials"
      value       = "use-ssm-parameter-store"  # Reference SSM parameter
      secure      = true
    },
    "opensearch/endpoint" = {
      description = "OpenSearch endpoint URL"
      value       = "https://opensearch.example.com"
      secure      = false
    }
  }

  tags = {
    Project     = "infodocs"
    CostCenter  = "infrastructure"
  }
}
```

## Integration with Other Modules

### Wazuh Module
```hcl
module "wazuh" {
  source = "../../modules/wazuh"

  # Other parameters...
  ssm_parameter_path = "/infodocs/${var.environment}/wazuh"
}
```

### OpenSearch Module
```hcl
module "opensearch" {
  source = "../../modules/opensearch"

  # Other parameters...
  ssm_parameter_path = "/infodocs/${var.environment}/opensearch"
}
```

## Security Considerations

- Use `secure = true` for all sensitive parameters
- Always use KMS encryption for secure parameters
- Avoid hardcoding sensitive values in Terraform code
- Use IAM policies to restrict parameter access

## Cost Considerations

- **Standard Parameters**: Free
- **Advanced Parameters**: $0.05 per parameter per month
- **API Requests**: Free for standard throughput
- **Total Estimated Cost**: $0-5/month depending on parameter count

## Best Practices

1. **Naming Convention**: Use consistent hierarchy (e.g., /infodocs/dev/service/parameter)
2. **Description**: Always provide meaningful descriptions
3. **Encryption**: Always encrypt sensitive parameters
4. **Access Control**: Use IAM policies to restrict access
5. **Rotation**: Regularly rotate sensitive parameters
<!-- BEGINNING OF PRE-COMMIT-TERRAFORM DOCS HOOK -->
## Requirements

No requirements.

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | 6.0.0 |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [aws_ssm_parameter.parameter](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ssm_parameter) | resource |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_environment"></a> [environment](#input\_environment) | Environment name (dev, staging, prod) | `string` | n/a | yes |
| <a name="input_kms_key_id"></a> [kms\_key\_id](#input\_kms\_key\_id) | KMS key ID for encrypting secure parameters | `string` | n/a | yes |
| <a name="input_parameters"></a> [parameters](#input\_parameters) | Map of parameters to create in SSM Parameter Store | <pre>map(object({<br/>    description = string<br/>    value       = string<br/>    secure      = bool<br/>  }))</pre> | n/a | yes |
| <a name="input_tags"></a> [tags](#input\_tags) | Tags to apply to SSM parameters | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_parameter_arns"></a> [parameter\_arns](#output\_parameter\_arns) | ARNs of the SSM parameters |
| <a name="output_parameter_names"></a> [parameter\_names](#output\_parameter\_names) | Names of the SSM parameters |
<!-- END OF PRE-COMMIT-TERRAFORM DOCS HOOK -->
