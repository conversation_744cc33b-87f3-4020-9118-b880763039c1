#!/bin/bash
# ParrotOS-style penetration testing setup script

# Set hostname
hostnamectl set-hostname ${hostname}

# Update system
apt-get update -y

# Install ParrotOS-style tools and privacy-focused utilities
apt-get install -y \
    docker.io \
    docker-compose \
    awscli \
    python3-pip \
    git \
    curl \
    wget \
    nmap \
    masscan \
    gobuster \
    nikto \
    sqlmap \
    burpsuite \
    metasploit-framework \
    john \
    hashcat \
    aircrack-ng \
    wireshark \
    tcpdump \
    netcat \
    socat \
    proxychains4 \
    tor \
    torsocks \
    openvpn \
    rdesktop \
    freerdp2-x11 \
    tigervnc-standalone-server \
    tigervnc-xorg-extension \
    mate-desktop-environment \
    mate-desktop-environment-extras \
    firefox-esr \
    thunderbird \
    keepassxc \
    veracrypt \
    bleachbit \
    macchanger \
    steghide \
    binwalk \
    foremost \
    volatility \
    autopsy \
    sleuthkit \
    chkrootkit \
    rkhunter \
    lynis

# Enable and start Docker
systemctl enable docker
systemctl start docker

# Create parrot user (ParrotOS default)
useradd -m -s /bin/bash parrot
echo "parrot:parrot123" | chpasswd
usermod -aG sudo,docker parrot

# Install additional Python tools for privacy and security
pip3 install \
    impacket \
    bloodhound \
    crackmapexec \
    responder \
    mitm6 \
    ldapdomaindump \
    pywerview \
    shodan \
    censys \
    theHarvester \
    recon-ng \
    sublist3r \
    dirsearch \
    wfuzz \
    paramiko \
    scapy \
    pycrypto \
    requests \
    beautifulsoup4

# Setup VNC server for GUI access
mkdir -p /home/<USER>/.vnc
echo "parrot123" | vncpasswd -f > /home/<USER>/.vnc/passwd
chmod 600 /home/<USER>/.vnc/passwd
chown -R parrot:parrot /home/<USER>/.vnc

# Create VNC startup script for MATE desktop
cat > /home/<USER>/.vnc/xstartup << 'EOF'
#!/bin/bash
xrdb $HOME/.Xresources
mate-session &
EOF

chmod +x /home/<USER>/.vnc/xstartup
chown parrot:parrot /home/<USER>/.vnc/xstartup

# Create systemd service for VNC
cat > /etc/systemd/system/vncserver@.service << 'EOF'
[Unit]
Description=Start TightVNC server at startup
After=syslog.target network.target

[Service]
Type=forking
User=parrot
Group=parrot
WorkingDirectory=/home/<USER>

PIDFile=/home/<USER>/.vnc/%H:%i.pid
ExecStartPre=-/usr/bin/vncserver -kill :%i > /dev/null 2>&1
ExecStart=/usr/bin/vncserver -depth 24 -geometry 1280x800 :%i
ExecStop=/usr/bin/vncserver -kill :%i

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload

# Configure Tor for privacy
cat >> /etc/tor/torrc << 'EOF'
# ParrotOS-style Tor configuration
SocksPort 9050
ControlPort 9051
CookieAuthentication 1
EOF

systemctl enable tor
systemctl start tor

# Install AWS SSM agent
snap install amazon-ssm-agent --classic
systemctl enable snap.amazon-ssm-agent.amazon-ssm-agent.service
systemctl start snap.amazon-ssm-agent.amazon-ssm-agent.service

# Create penetration testing workspace
mkdir -p /home/<USER>/pentest/{tools,reports,scripts,wordlists,privacy,forensics}
chown -R parrot:parrot /home/<USER>/pentest

# Download common wordlists and privacy tools
cd /home/<USER>/pentest/wordlists
wget -q https://github.com/danielmiessler/SecLists/archive/master.zip -O seclists.zip
unzip -q seclists.zip && rm seclists.zip

# Download privacy-focused tools
cd /home/<USER>/pentest/privacy
git clone https://github.com/Und3rf10w/kali-anonsurf.git
git clone https://github.com/epidemics-scepticism/writing.git
git clone https://github.com/s0md3v/Photon.git

chown -R parrot:parrot /home/<USER>/pentest

# Create useful aliases for ParrotOS-style workflow
cat >> /home/<USER>/.bashrc << 'EOF'

# ParrotOS-style aliases
alias ll='ls -alF'
alias la='ls -A'
alias l='ls -CF'
alias pentest='cd /home/<USER>/pentest'
alias tools='cd /home/<USER>/pentest/tools'
alias reports='cd /home/<USER>/pentest/reports'
alias scripts='cd /home/<USER>/pentest/scripts'
alias wordlists='cd /home/<USER>/pentest/wordlists'
alias privacy='cd /home/<USER>/pentest/privacy'
alias forensics='cd /home/<USER>/pentest/forensics'

# Network scanning aliases
alias nmap-quick='nmap -T4 -F'
alias nmap-full='nmap -T4 -A -v'
alias nmap-vuln='nmap --script vuln'
alias nmap-stealth='nmap -sS -T2'

# Web application testing aliases
alias gobuster-common='gobuster dir -w /home/<USER>/pentest/wordlists/SecLists-master/Discovery/Web-Content/common.txt'
alias nikto-scan='nikto -h'
alias sqlmap-basic='sqlmap --batch --random-agent'

# Privacy aliases
alias tor-start='systemctl start tor'
alias tor-stop='systemctl stop tor'
alias tor-status='systemctl status tor'
alias proxychains='proxychains4'
alias anon-surf='cd /home/<USER>/pentest/privacy/kali-anonsurf && sudo ./anonsurf.sh'

# Forensics aliases
alias volatility='python2 /usr/bin/volatility'
alias binwalk-extract='binwalk -e'
alias foremost-all='foremost -t all'

# VNC aliases
alias vnc-start='vncserver :1 -geometry 1280x800 -depth 24'
alias vnc-stop='vncserver -kill :1'

# MAC address randomization
alias mac-random='sudo macchanger -r'
alias mac-restore='sudo macchanger -p'
EOF

chown parrot:parrot /home/<USER>/.bashrc

# Create startup script for ParrotOS environment
cat > /home/<USER>/start-parrot.sh << 'EOF'
#!/bin/bash
echo "Starting ParrotOS Penetration Testing Environment..."
echo "=================================================="
echo "Privacy-focused penetration testing platform"
echo "VNC Server: Run 'vnc-start' to start GUI access"
echo "SSH Tunnel: ssh -L 5902:localhost:5901 parrot@this-ip"
echo "Workspace: /home/<USER>/pentest/"
echo "Privacy Tools: AnonSurf, Tor, ProxyChains, MAC changer"
echo "Forensics: Volatility, Binwalk, Foremost, Autopsy"
echo "Pentest Tools: Nmap, Metasploit, Burp Suite, SQLMap"
echo "=================================================="
echo "Tor Status: $(systemctl is-active tor)"
echo "Docker Status: $(systemctl is-active docker)"
EOF

chmod +x /home/<USER>/start-parrot.sh
chown parrot:parrot /home/<USER>/start-parrot.sh

# Configure automatic MAC address randomization on boot
cat > /etc/systemd/system/mac-randomize.service << 'EOF'
[Unit]
Description=Randomize MAC address
After=network.target

[Service]
Type=oneshot
ExecStart=/usr/bin/macchanger -r eth0
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
EOF

# Enable MAC randomization service (optional)
# systemctl enable mac-randomize.service

# Log completion
echo "ParrotOS-style setup completed at $(date)" >> /var/log/user-data.log
