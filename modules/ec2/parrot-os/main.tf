# ParrotOS penetration testing instance
data "aws_ami" "parrot_os" {
  most_recent = true
  owners      = ["679593333241"] # Using Kali AMI as base, will customize with ParrotOS tools

  filter {
    name   = "name"
    values = ["kali-linux-*"] # Will customize this to ParrotOS in user_data
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }

  filter {
    name   = "state"
    values = ["available"]
  }
}

# Security group for ParrotOS (private access only)
resource "aws_security_group" "parrot_os" {
  name_prefix = "${var.name_prefix}-parrot-os"
  vpc_id      = var.vpc_id
  description = "Security group for ParrotOS penetration testing"

  # SSH access from bastion only
  ingress {
    description     = "SSH from bastion"
    from_port       = 22
    to_port         = 22
    protocol        = "tcp"
    security_groups = [var.bastion_security_group_id]
  }

  # VNC access from bastion (for GUI)
  ingress {
    description     = "VNC from bastion"
    from_port       = 5900
    to_port         = 5910
    protocol        = "tcp"
    security_groups = [var.bastion_security_group_id]
  }

  # HTTP/HTTPS for tool updates and downloads
  egress {
    description = "HTTP outbound"
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    description = "HTTPS outbound"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # DNS resolution
  egress {
    description = "DNS outbound"
    from_port   = 53
    to_port     = 53
    protocol    = "udp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Internal network testing (VPC CIDR)
  egress {
    description = "Internal network testing"
    from_port   = 0
    to_port     = 65535
    protocol    = "tcp"
    cidr_blocks = [var.vpc_cidr]
  }

  egress {
    description = "Internal network testing UDP"
    from_port   = 0
    to_port     = 65535
    protocol    = "udp"
    cidr_blocks = [var.vpc_cidr]
  }

  tags = merge(
    var.tags,
    {
      Name    = "${var.name_prefix}-parrot-os-sg"
      Purpose = "penetration-testing"
      Tool    = "parrot-os"
    }
  )
}

# ParrotOS EC2 instance
resource "aws_instance" "parrot_os" {
  ami                    = data.aws_ami.parrot_os.id
  instance_type          = var.instance_type
  key_name               = var.key_name
  vpc_security_group_ids = [aws_security_group.parrot_os.id]
  subnet_id              = var.private_subnet_id

  # Start stopped by default to save costs
  instance_initiated_shutdown_behavior = "stop"

  # Enhanced monitoring
  monitoring = true

  # EBS optimization
  ebs_optimized = true

  # Root volume configuration
  root_block_device {
    volume_type           = "gp3"
    volume_size           = var.root_volume_size
    delete_on_termination = true
    encrypted             = true
    kms_key_id            = var.kms_key_arn

    tags = merge(
      var.tags,
      {
        Name = "${var.name_prefix}-parrot-os-root"
      }
    )
  }

  # User data for ParrotOS setup
  user_data = base64encode(templatefile("${path.module}/user_data.sh", {
    hostname = "${var.name_prefix}-parrot-os"
  }))

  tags = merge(
    var.tags,
    {
      Name            = "${var.name_prefix}-parrot-os"
      Purpose         = "penetration-testing"
      Tool            = "parrot-os"
      AutoStart       = "false"
      CostOptimized   = "true"
      SecurityTesting = "true"
      Environment     = var.environment
      Specialty       = "privacy-focused-pentest"
    }
  )

  lifecycle {
    ignore_changes = [
      # Ignore AMI changes to prevent unwanted updates
      ami,
      # Allow manual start/stop without Terraform interference
      instance_state
    ]
  }
}

# CloudWatch alarms for cost monitoring
resource "aws_cloudwatch_metric_alarm" "parrot_os_running_time" {
  count = var.enable_cost_monitoring ? 1 : 0

  alarm_name          = "${var.name_prefix}-parrot-os-running-time"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "StatusCheckFailed"
  namespace           = "AWS/EC2"
  period              = "3600" # 1 hour
  statistic           = "Maximum"
  threshold           = "0"
  alarm_description   = "ParrotOS instance running for extended period"
  alarm_actions       = var.cost_alarm_sns_topics

  dimensions = {
    InstanceId = aws_instance.parrot_os.id
  }

  tags = var.tags
}

# SSM association for patch management
resource "aws_ssm_association" "parrot_os_patch" {
  count = var.enable_patch_management ? 1 : 0

  name = "AWS-RunPatchBaseline"

  targets {
    key    = "InstanceIds"
    values = [aws_instance.parrot_os.id]
  }

  schedule_expression = "cron(0 2 ? * SUN *)" # Weekly on Sunday at 2 AM

  parameters = {
    Operation = "Install"
  }
}
