output "instance_id" {
  description = "ID of the ParrotOS instance"
  value       = aws_instance.parrot_os.id
}

output "instance_arn" {
  description = "ARN of the ParrotOS instance"
  value       = aws_instance.parrot_os.arn
}

output "private_ip" {
  description = "Private IP address of the ParrotOS instance"
  value       = aws_instance.parrot_os.private_ip
}

output "security_group_id" {
  description = "Security group ID for ParrotOS"
  value       = aws_security_group.parrot_os.id
}

output "ami_id" {
  description = "AMI ID used for ParrotOS instance"
  value       = data.aws_ami.parrot_os.id
}

output "instance_type" {
  description = "Instance type of ParrotOS"
  value       = aws_instance.parrot_os.instance_type
}

output "access_instructions" {
  description = "Instructions for accessing ParrotOS"
  value = {
    ssh_command   = "ssh -J ec2-user@BASTION_IP parrot@${aws_instance.parrot_os.private_ip}"
    vnc_tunnel    = "ssh -L 5902:${aws_instance.parrot_os.private_ip}:5901 ec2-user@BASTION_IP"
    start_command = "aws ec2 start-instances --instance-ids ${aws_instance.parrot_os.id}"
    stop_command  = "aws ec2 stop-instances --instance-ids ${aws_instance.parrot_os.id}"
  }
}

output "cost_optimization" {
  description = "Cost optimization features"
  value = {
    auto_start      = false
    cost_monitoring = var.enable_cost_monitoring
    instance_type   = var.instance_type
    ebs_optimized   = true
    stop_when_idle  = true
  }
}

output "security_features" {
  description = "Security features enabled"
  value = {
    private_subnet      = true
    bastion_access_only = true
    encrypted_storage   = true
    security_groups     = [aws_security_group.parrot_os.id]
    patch_management    = var.enable_patch_management
    privacy_focused     = var.privacy_focused_tools
  }
}

output "parrot_os_features" {
  description = "ParrotOS specific features"
  value = {
    privacy_focused   = true
    anonsurf_included = true
    tor_integration   = true
    crypto_tools      = true
    forensics_tools   = true
    development_tools = true
  }
}
