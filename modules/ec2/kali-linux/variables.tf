variable "name_prefix" {
  type        = string
  description = "Prefix for resource names"
}

variable "environment" {
  type        = string
  description = "Environment name"
}

variable "vpc_id" {
  type        = string
  description = "VPC ID where Kali Linux will be deployed"
}

variable "vpc_cidr" {
  type        = string
  description = "VPC CIDR block for internal network testing"
}

variable "private_subnet_id" {
  type        = string
  description = "Private subnet ID for Kali Linux instance"
}

variable "bastion_security_group_id" {
  type        = string
  description = "Security group ID of the bastion host for SSH access"
}

variable "key_name" {
  type        = string
  description = "EC2 Key Pair name for SSH access"
}

variable "kms_key_arn" {
  type        = string
  description = "KMS key ARN for EBS encryption"
}

variable "instance_type" {
  type        = string
  description = "EC2 instance type for Kali Linux"
  default     = "t3.large"
  validation {
    condition     = contains(["t3.medium", "t3.large", "t3.xlarge", "m5.large", "m5.xlarge"], var.instance_type)
    error_message = "Instance type must be suitable for penetration testing workloads."
  }
}

variable "root_volume_size" {
  type        = number
  description = "Root volume size in GB for Kali Linux"
  default     = 50
  validation {
    condition     = var.root_volume_size >= 20 && var.root_volume_size <= 200
    error_message = "Root volume size must be between 20 and 200 GB."
  }
}

variable "enable_cost_monitoring" {
  type        = bool
  description = "Enable CloudWatch alarms for cost monitoring"
  default     = true
}

variable "enable_patch_management" {
  type        = bool
  description = "Enable automated patch management"
  default     = true
}

variable "cost_alarm_sns_topics" {
  type        = list(string)
  description = "SNS topic ARNs for cost monitoring alerts"
  default     = []
}

variable "allowed_testing_cidrs" {
  type        = list(string)
  description = "CIDR blocks allowed for penetration testing"
  default     = []
}

variable "tags" {
  type        = map(string)
  description = "Tags to apply to all Kali Linux resources"
  default     = {}
}
