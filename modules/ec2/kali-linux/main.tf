# Kali Linux penetration testing instance
data "aws_ami" "kali_linux" {
  most_recent = true
  owners      = ["679593333241"] # Kali Linux official AMI owner

  filter {
    name   = "name"
    values = ["kali-linux-*"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }

  filter {
    name   = "state"
    values = ["available"]
  }
}

# Security group for Kali Linux (private access only)
resource "aws_security_group" "kali_linux" {
  name_prefix = "${var.name_prefix}-kali-linux"
  vpc_id      = var.vpc_id
  description = "Security group for Kali Linux penetration testing"

  # SSH access from bastion only
  ingress {
    description     = "SSH from bastion"
    from_port       = 22
    to_port         = 22
    protocol        = "tcp"
    security_groups = [var.bastion_security_group_id]
  }

  # VNC access from bastion (for GUI)
  ingress {
    description     = "VNC from bastion"
    from_port       = 5900
    to_port         = 5910
    protocol        = "tcp"
    security_groups = [var.bastion_security_group_id]
  }

  # HTTP/HTTPS for tool updates and downloads
  egress {
    description = "HTTP outbound"
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    description = "HTTPS outbound"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # DNS resolution
  egress {
    description = "DNS outbound"
    from_port   = 53
    to_port     = 53
    protocol    = "udp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Internal network testing (VPC CIDR)
  egress {
    description = "Internal network testing"
    from_port   = 0
    to_port     = 65535
    protocol    = "tcp"
    cidr_blocks = [var.vpc_cidr]
  }

  egress {
    description = "Internal network testing UDP"
    from_port   = 0
    to_port     = 65535
    protocol    = "udp"
    cidr_blocks = [var.vpc_cidr]
  }

  tags = merge(
    var.tags,
    {
      Name    = "${var.name_prefix}-kali-linux-sg"
      Purpose = "penetration-testing"
      Tool    = "kali-linux"
    }
  )
}

# Kali Linux EC2 instance
resource "aws_instance" "kali_linux" {
  ami                    = data.aws_ami.kali_linux.id
  instance_type          = var.instance_type
  key_name               = var.key_name
  vpc_security_group_ids = [aws_security_group.kali_linux.id]
  subnet_id              = var.private_subnet_id

  # Start stopped by default to save costs
  instance_initiated_shutdown_behavior = "stop"

  # Enhanced monitoring
  monitoring = true

  # EBS optimization
  ebs_optimized = true

  # Root volume configuration
  root_block_device {
    volume_type           = "gp3"
    volume_size           = var.root_volume_size
    delete_on_termination = true
    encrypted             = true
    kms_key_id            = var.kms_key_arn

    tags = merge(
      var.tags,
      {
        Name = "${var.name_prefix}-kali-linux-root"
      }
    )
  }

  # User data for initial setup
  user_data = base64encode(templatefile("${path.module}/user_data.sh", {
    hostname = "${var.name_prefix}-kali-linux"
  }))

  tags = merge(
    var.tags,
    {
      Name            = "${var.name_prefix}-kali-linux"
      Purpose         = "penetration-testing"
      Tool            = "kali-linux"
      AutoStart       = "false"
      CostOptimized   = "true"
      SecurityTesting = "true"
      Environment     = var.environment
    }
  )

  lifecycle {
    ignore_changes = [
      # Ignore AMI changes to prevent unwanted updates
      ami,
      # Allow manual start/stop without Terraform interference
      instance_state
    ]
  }
}

# CloudWatch alarms for cost monitoring
resource "aws_cloudwatch_metric_alarm" "kali_linux_running_time" {
  count = var.enable_cost_monitoring ? 1 : 0

  alarm_name          = "${var.name_prefix}-kali-linux-running-time"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "StatusCheckFailed"
  namespace           = "AWS/EC2"
  period              = "3600" # 1 hour
  statistic           = "Maximum"
  threshold           = "0"
  alarm_description   = "Kali Linux instance running for extended period"
  alarm_actions       = var.cost_alarm_sns_topics

  dimensions = {
    InstanceId = aws_instance.kali_linux.id
  }

  tags = var.tags
}

# SSM association for patch management
resource "aws_ssm_association" "kali_linux_patch" {
  count = var.enable_patch_management ? 1 : 0

  name = "AWS-RunPatchBaseline"

  targets {
    key    = "InstanceIds"
    values = [aws_instance.kali_linux.id]
  }

  schedule_expression = "cron(0 2 ? * SUN *)" # Weekly on Sunday at 2 AM

  parameters = {
    Operation = "Install"
  }
}
