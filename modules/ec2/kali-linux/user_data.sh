#!/bin/bash
# Kali Linux initial setup script

# Set hostname
hostnamectl set-hostname ${hostname}

# Update system
apt-get update -y

# Install additional penetration testing tools
apt-get install -y \
    docker.io \
    docker-compose \
    awscli \
    python3-pip \
    git \
    curl \
    wget \
    nmap \
    masscan \
    gobuster \
    nikto \
    sqlmap \
    burpsuite \
    metasploit-framework \
    john \
    hashcat \
    aircrack-ng \
    wireshark \
    tcpdump \
    netcat \
    socat \
    proxychains4 \
    tor \
    openvpn \
    rdesktop \
    freerdp2-x11 \
    tigervnc-standalone-server \
    tigervnc-xorg-extension \
    xfce4 \
    xfce4-goodies

# Enable and start Docker
systemctl enable docker
systemctl start docker
usermod -aG docker kali

# Install additional Python tools
pip3 install \
    impacket \
    bloodhound \
    crackmapexec \
    responder \
    mitm6 \
    ldapdomaindump \
    pywerview

# Setup VNC server for GUI access
mkdir -p /home/<USER>/.vnc
echo "kali123" | vncpasswd -f > /home/<USER>/.vnc/passwd
chmod 600 /home/<USER>/.vnc/passwd
chown -R kali:kali /home/<USER>/.vnc

# Create VNC startup script
cat > /home/<USER>/.vnc/xstartup << 'EOF'
#!/bin/bash
xrdb $HOME/.Xresources
startxfce4 &
EOF

chmod +x /home/<USER>/.vnc/xstartup
chown kali:kali /home/<USER>/.vnc/xstartup

# Create systemd service for VNC
cat > /etc/systemd/system/vncserver@.service << 'EOF'
[Unit]
Description=Start TightVNC server at startup
After=syslog.target network.target

[Service]
Type=forking
User=kali
Group=kali
WorkingDirectory=/home/<USER>

PIDFile=/home/<USER>/.vnc/%H:%i.pid
ExecStartPre=-/usr/bin/vncserver -kill :%i > /dev/null 2>&1
ExecStart=/usr/bin/vncserver -depth 24 -geometry 1280x800 :%i
ExecStop=/usr/bin/vncserver -kill :%i

[Install]
WantedBy=multi-user.target
EOF

# Enable VNC service (but don't start automatically to save resources)
systemctl daemon-reload

# Install AWS SSM agent for management
snap install amazon-ssm-agent --classic
systemctl enable snap.amazon-ssm-agent.amazon-ssm-agent.service
systemctl start snap.amazon-ssm-agent.amazon-ssm-agent.service

# Create penetration testing workspace
mkdir -p /home/<USER>/pentest/{tools,reports,scripts,wordlists}
chown -R kali:kali /home/<USER>/pentest

# Download common wordlists
cd /home/<USER>/pentest/wordlists
wget -q https://github.com/danielmiessler/SecLists/archive/master.zip -O seclists.zip
unzip -q seclists.zip && rm seclists.zip
chown -R kali:kali /home/<USER>/pentest/wordlists

# Create useful aliases
cat >> /home/<USER>/.bashrc << 'EOF'

# Penetration testing aliases
alias ll='ls -alF'
alias la='ls -A'
alias l='ls -CF'
alias pentest='cd /home/<USER>/pentest'
alias tools='cd /home/<USER>/pentest/tools'
alias reports='cd /home/<USER>/pentest/reports'
alias scripts='cd /home/<USER>/pentest/scripts'
alias wordlists='cd /home/<USER>/pentest/wordlists'

# Network scanning aliases
alias nmap-quick='nmap -T4 -F'
alias nmap-full='nmap -T4 -A -v'
alias nmap-vuln='nmap --script vuln'

# Web application testing aliases
alias gobuster-common='gobuster dir -w /home/<USER>/pentest/wordlists/SecLists-master/Discovery/Web-Content/common.txt'
alias nikto-scan='nikto -h'

# Start VNC server
alias vnc-start='vncserver :1 -geometry 1280x800 -depth 24'
alias vnc-stop='vncserver -kill :1'
EOF

# Set proper permissions
chown kali:kali /home/<USER>/.bashrc

# Create startup script for penetration testing environment
cat > /home/<USER>/start-pentest.sh << 'EOF'
#!/bin/bash
echo "Starting Kali Linux Penetration Testing Environment..."
echo "=========================================="
echo "VNC Server: Run 'vnc-start' to start GUI access"
echo "SSH Tunnel: ssh -L 5901:localhost:5901 kali@this-ip"
echo "Workspace: /home/<USER>/pentest/"
echo "Tools installed: nmap, masscan, gobuster, nikto, sqlmap, burpsuite, metasploit"
echo "=========================================="
EOF

chmod +x /home/<USER>/start-pentest.sh
chown kali:kali /home/<USER>/start-pentest.sh

# Log completion
echo "Kali Linux setup completed at $(date)" >> /var/log/user-data.log
