output "instance_id" {
  description = "ID of the Kali Linux instance"
  value       = aws_instance.kali_linux.id
}

output "instance_arn" {
  description = "ARN of the Kali Linux instance"
  value       = aws_instance.kali_linux.arn
}

output "private_ip" {
  description = "Private IP address of the Kali Linux instance"
  value       = aws_instance.kali_linux.private_ip
}

output "security_group_id" {
  description = "Security group ID for Kali Linux"
  value       = aws_security_group.kali_linux.id
}

output "ami_id" {
  description = "AMI ID used for Kali Linux instance"
  value       = data.aws_ami.kali_linux.id
}

output "instance_type" {
  description = "Instance type of Kali Linux"
  value       = aws_instance.kali_linux.instance_type
}

output "access_instructions" {
  description = "Instructions for accessing Kali Linux"
  value = {
    ssh_command   = "ssh -J ec2-user@${var.bastion_security_group_id} kali@${aws_instance.kali_linux.private_ip}"
    vnc_tunnel    = "ssh -L 5901:${aws_instance.kali_linux.private_ip}:5901 ec2-user@BASTION_IP"
    start_command = "aws ec2 start-instances --instance-ids ${aws_instance.kali_linux.id}"
    stop_command  = "aws ec2 stop-instances --instance-ids ${aws_instance.kali_linux.id}"
  }
}

output "cost_optimization" {
  description = "Cost optimization features"
  value = {
    auto_start      = false
    cost_monitoring = var.enable_cost_monitoring
    instance_type   = var.instance_type
    ebs_optimized   = true
    stop_when_idle  = true
  }
}

output "security_features" {
  description = "Security features enabled"
  value = {
    private_subnet      = true
    bastion_access_only = true
    encrypted_storage   = true
    security_groups     = [aws_security_group.kali_linux.id]
    patch_management    = var.enable_patch_management
  }
}
