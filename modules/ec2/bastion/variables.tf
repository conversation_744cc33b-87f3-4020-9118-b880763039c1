variable "environment" {
  type        = string
  description = "Environment name (dev, prod, operations)"
  validation {
    condition     = contains(["dev", "prod", "operations"], var.environment)
    error_message = "Environment must be one of: dev, prod, operations."
  }
}

variable "instance_type" {
  type        = string
  description = "Instance type for bastion host"
  default     = "t3.micro"
  validation {
    condition     = contains(["t3.micro", "t3.small", "t3.medium"], var.instance_type)
    error_message = "Instance type must be one of: t3.micro, t3.small, t3.medium."
  }
}

variable "public_subnet_id" {
  type        = string
  description = "Public subnet ID for bastion host"
  validation {
    condition     = can(regex("^subnet-[a-z0-9]+$", var.public_subnet_id))
    error_message = "Subnet ID must be a valid subnet identifier."
  }
}

variable "vpc_id" {
  type        = string
  description = "VPC ID where bastion will be deployed"
}

# key_name is now read from SSM Parameter Store via data source

variable "kms_key_id" {
  type        = string
  description = "KMS key ID for EBS volume encryption"
  default     = null # Make it optional
}

variable "name_prefix" {
  type        = string
  description = "Prefix for resource names"
}

variable "allowed_ssh_cidr_blocks" {
  type        = list(string)
  description = "CIDR blocks allowed to SSH to bastion host"
  # Validation removed for development flexibility
}

variable "admin_users" {
  type        = list(string)
  description = "List of admin usernames to create on bastion host"
  default     = ["ec2-user"]
}

variable "enable_cloudwatch_logs" {
  type        = bool
  description = "Enable CloudWatch logs for bastion host"
  default     = true
}

variable "allocate_eip" {
  type        = bool
  description = "Allocate Elastic IP for bastion host"
  default     = true
}

variable "cloudwatch_log_retention" {
  type        = number
  description = "Retention period in days for CloudWatch logs"
  default     = 30
}

variable "tags" {
  type        = map(string)
  description = "Tags for bastion host"
  default     = {}
}

variable "setup_cloudflare_tunnel" {
  type        = bool
  description = "Whether to install cloudflared for Cloudflare Tunnel support"
  default     = false
}
