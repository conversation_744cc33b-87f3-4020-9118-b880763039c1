#!/bin/bash
yum update -y

# Install essential tools
yum install -y htop curl wget jq

# Install AWS CLI v2
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
./aws/install

# Configure SSH for port forwarding
echo "GatewayPorts yes" >> /etc/ssh/sshd_config
echo "AllowTcpForwarding yes" >> /etc/ssh/sshd_config
systemctl restart sshd

# Create admin users
%{ for user in admin_users ~}
useradd -m -s /bin/bash ${user}
usermod -aG wheel ${user}
%{ endfor ~}

# Configure CloudWatch agent
yum install -y amazon-cloudwatch-agent
cat > /opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json << 'EOF'
{
    "logs": {
        "logs_collected": {
            "files": {
                "collect_list": [
                    {
                        "file_path": "/var/log/secure",
                        "log_group_name": "/aws/ec2/bastion",
                        "log_stream_name": "{instance_id}/secure"
                    },
                    {
                        "file_path": "/var/log/messages",
                        "log_group_name": "/aws/ec2/bastion",
                        "log_stream_name": "{instance_id}/messages"
                    }
                ]
            }
        }
    }
}
EOF

/opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl \
    -a fetch-config \
    -m ec2 \
    -c file:/opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json \
    -s

# Setup Cloudflare Tunnel (if enabled)
%{ if setup_cloudflare_tunnel ~}
echo "Setting up Cloudflare Tunnel..."

# Install cloudflared
curl -L --output cloudflared.rpm https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-x86_64.rpm
yum install -y cloudflared.rpm
rm cloudflared.rpm

# Create cloudflared directories
mkdir -p /etc/cloudflared
mkdir -p /var/log/cloudflared

# Create cloudflared user
useradd -r -s /bin/false cloudflared || true

# Note: Tunnel configuration will be set up after deployment via SSM parameters
echo "Cloudflared installed. Configuration will be applied via SSM parameters."
%{ endif ~}

# Create helpful aliases
cat >> /home/<USER>/.bashrc << 'EOF'
alias ll='ls -la'
alias wazuh-tunnel='ssh -L 8443:WAZUH_PRIVATE_IP:443 -N ec2-user@localhost'
alias opensearch-tunnel='ssh -L 8444:OPENSEARCH_ENDPOINT:443 -N ec2-user@localhost'
alias tunnel-status='sudo systemctl status cloudflared'
alias tunnel-logs='sudo journalctl -u cloudflared -f'
EOF
