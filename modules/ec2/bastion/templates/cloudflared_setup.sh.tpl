#!/bin/bash

# Cloudflared setup script for bastion host
# This script installs and configures cloudflared for Cloudflare Tunnel

set -e

echo "🔧 Setting up Cloudflare Tunnel on bastion host"
echo "==============================================="

# Install cloudflared
echo "Installing cloudflared..."
curl -L --output cloudflared.rpm https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-x86_64.rpm
sudo yum install -y cloudflared.rpm
rm cloudflared.rpm

# Create cloudflared directory
sudo mkdir -p /etc/cloudflared
sudo mkdir -p /var/log/cloudflared

# Get tunnel configuration from SSM
echo "Retrieving tunnel configuration from SSM..."
TUNNEL_TOKEN=$(aws ssm get-parameter --name "/infodocs/cloudflare/tunnel/token" --with-decryption --query 'Parameter.Value' --output text --region ${aws_region})
TUNNEL_CONFIG=$(aws ssm get-parameter --name "/infodocs/cloudflare/tunnel/config" --query 'Parameter.Value' --output text --region ${aws_region})

# Create credentials file
echo "Creating tunnel credentials..."
echo "$TUNNEL_TOKEN" | sudo tee /etc/cloudflared/credentials.json > /dev/null
sudo chmod 600 /etc/cloudflared/credentials.json

# Create configuration file
echo "Creating tunnel configuration..."
echo "$TUNNEL_CONFIG" | sudo tee /etc/cloudflared/config.yml > /dev/null
sudo chmod 644 /etc/cloudflared/config.yml

# Create systemd service
echo "Creating systemd service..."
sudo tee /etc/systemd/system/cloudflared.service > /dev/null <<EOF
[Unit]
Description=Cloudflare Tunnel
After=network.target

[Service]
Type=simple
User=cloudflared
Group=cloudflared
ExecStart=/usr/local/bin/cloudflared tunnel --config /etc/cloudflared/config.yml run
Restart=on-failure
RestartSec=5s

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=cloudflared

[Install]
WantedBy=multi-user.target
EOF

# Create cloudflared user
sudo useradd -r -s /bin/false cloudflared || true
sudo chown -R cloudflared:cloudflared /etc/cloudflared
sudo chown -R cloudflared:cloudflared /var/log/cloudflared

# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable cloudflared
sudo systemctl start cloudflared

# Check status
echo "Checking tunnel status..."
sudo systemctl status cloudflared --no-pager

echo "✅ Cloudflare Tunnel setup complete!"
echo "Tunnel should now be connecting OpenSearch to opensearch.infodocs.co.za"
