output "bastion_instance_id" {
  description = "ID of the bastion host instance"
  value       = aws_instance.bastion.id
}

output "bastion_public_ip" {
  description = "Public IP address of the bastion host"
  value       = length(aws_eip.bastion) > 0 ? aws_eip.bastion[0].public_ip : aws_instance.bastion.public_ip
}

output "bastion_private_ip" {
  description = "Private IP address of the bastion host"
  value       = aws_instance.bastion.private_ip
}

output "bastion_security_group_id" {
  description = "ID of the bastion host security group"
  value       = aws_security_group.bastion.id
}

output "ssh_command" {
  description = "SSH command to connect to bastion host"
  value       = "ssh -i ${data.aws_ssm_parameter.ssh_key_name.value}.pem ec2-user@${length(aws_eip.bastion) > 0 ? aws_eip.bastion[0].public_ip : aws_instance.bastion.public_ip}"
  sensitive   = true
}
