# Data source to read SSH key name from SSM Parameter Store
data "aws_ssm_parameter" "ssh_key_name" {
  name = "/infodocs/${var.environment}/ec2/key_name"
}

# Bastion Host for secure access to internal monitoring systems
resource "aws_instance" "bastion" {
  ami                    = data.aws_ami.amazon_linux_2.id
  instance_type          = var.instance_type
  subnet_id              = var.public_subnet_id
  vpc_security_group_ids = [aws_security_group.bastion.id]
  key_name               = data.aws_ssm_parameter.ssh_key_name.value

  root_block_device {
    volume_size = 20
    volume_type = "gp3"
    encrypted   = false  # Temporarily match Wazuh configuration (no encryption)
  }

  user_data_base64 = base64encode(templatefile("${path.module}/templates/user_data.sh.tpl", {
    admin_users             = var.admin_users
    setup_cloudflare_tunnel = var.setup_cloudflare_tunnel
  }))

  # Security: Require IMDSv2 for enhanced security
  metadata_options {
    http_endpoint               = "enabled"
    http_tokens                 = "required" # Require IMDSv2
    http_put_response_hop_limit = 1
  }

  tags = merge(
    var.tags,
    {
      Name = "${var.name_prefix}-bastion"
      Type = "bastion-host"
    }
  )
}

# Security group for bastion host
resource "aws_security_group" "bastion" {
  name        = "${var.name_prefix}-bastion-sg"
  description = "Security group for bastion host"
  vpc_id      = var.vpc_id

  # SSH access from specific IP addresses only
  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = var.allowed_ssh_cidr_blocks
    description = "SSH access from authorized IPs only"
  }

  # Allow all outbound traffic for updates and internal access
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
  }

  tags = merge(
    var.tags,
    {
      Name = "${var.name_prefix}-bastion-sg"
    }
  )
}

# Elastic IP for bastion host (optional)
resource "aws_eip" "bastion" {
  count = var.allocate_eip ? 1 : 0

  instance = aws_instance.bastion.id
  domain   = "vpc"

  tags = merge(
    var.tags,
    {
      Name = "${var.name_prefix}-bastion-eip"
    }
  )
}

# Data source for latest Amazon Linux 2 AMI
data "aws_ami" "amazon_linux_2" {
  most_recent = true
  owners      = ["amazon"]

  filter {
    name   = "name"
    values = ["amzn2-ami-hvm-*-x86_64-gp2"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }
}

# CloudWatch Log Group for bastion host
resource "aws_cloudwatch_log_group" "bastion" {
  count = var.enable_cloudwatch_logs ? 1 : 0

  name              = "/aws/ec2/${var.name_prefix}-bastion"
  retention_in_days = var.cloudwatch_log_retention
  # KMS encryption removed to avoid key issues

  tags = var.tags
}
