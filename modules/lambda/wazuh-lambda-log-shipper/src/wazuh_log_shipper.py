import json
import boto3
import gzip
import base64
import os
import logging
from datetime import datetime, timezone
import hashlib
from typing import Dict, List, Any, Optional

# Configure logging
log_level = os.environ.get('LOG_LEVEL', 'INFO')
logging.basicConfig(level=getattr(logging, log_level))
logger = logging.getLogger(__name__)

# AWS clients
s3_client = boto3.client('s3')
sqs_client = boto3.client('sqs')

def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Wazuh-specific log shipper for security events
    Handles Wazuh alerts, events, and security log processing
    """

    # Wazuh-specific configuration
    WAZUH_S3_BUCKET = os.environ['WAZUH_S3_BUCKET']
    LEGAL_HOLD_S3_BUCKET = os.environ['LEGAL_HOLD_S3_BUCKET']
    KMS_KEY_ID = os.environ['KMS_KEY_ID']
    WAZUH_ALERT_THRESHOLD = int(os.environ.get('WAZUH_ALERT_THRESHOLD', '10'))
    ENABLE_THREAT_INTEL = os.environ.get('ENABLE_THREAT_INTEL', 'true').lower() == 'true'

    try:
        logger.info("Processing Wazuh security event")

        # Process Wazuh-specific log event
        wazuh_data = process_wazuh_event(event)

        if not wazuh_data or not wazuh_data.get('alerts'):
            logger.warning("No Wazuh alerts found in event")
            return create_response(200, "No Wazuh alerts to process")

        # Enrich with threat intelligence if enabled
        if ENABLE_THREAT_INTEL:
            wazuh_data = enrich_with_threat_intel(wazuh_data)

        # Generate Wazuh-specific compliance metadata
        metadata = generate_wazuh_metadata(wazuh_data)

        # Compress logs for storage efficiency
        compressed_data = compress_logs(wazuh_data)

        # Generate S3 key with Wazuh-specific partitioning
        s3_key = generate_wazuh_s3_key(wazuh_data, metadata)

        # Upload to Wazuh S3 bucket
        upload_response = upload_to_s3(
            WAZUH_S3_BUCKET, s3_key, compressed_data, metadata, KMS_KEY_ID
        )

        # Check if legal hold is required for high-severity alerts
        if requires_legal_hold(wazuh_data, WAZUH_ALERT_THRESHOLD):
            legal_hold_key = f"wazuh-security-incident/{s3_key}"
            legal_metadata = {**metadata, 'legal-hold': 'true', 'hold-reason': 'high-severity-security-alert'}

            upload_to_s3(
                LEGAL_HOLD_S3_BUCKET, legal_hold_key, compressed_data,
                legal_metadata, KMS_KEY_ID
            )
            logger.warning(f"HIGH SEVERITY: Legal hold applied for Wazuh alert: {legal_hold_key}")

        # Log success metrics
        alert_count = len(wazuh_data.get('alerts', []))
        max_severity = max([alert.get('rule', {}).get('level', 0) for alert in wazuh_data.get('alerts', [])], default=0)

        logger.info(f"Successfully processed {alert_count} Wazuh alerts, max severity: {max_severity}")

        return create_response(200, {
            'message': 'Wazuh security logs successfully processed',
            's3_key': s3_key,
            'alerts_processed': alert_count,
            'max_severity_level': max_severity,
            'legal_hold_applied': requires_legal_hold(wazuh_data, WAZUH_ALERT_THRESHOLD),
            'threat_intel_enriched': ENABLE_THREAT_INTEL
        })

    except Exception as e:
        logger.error(f"Error processing Wazuh logs: {str(e)}", exc_info=True)

        # Send to DLQ for security team review
        send_to_dlq(event, str(e), "wazuh-security-processing-error")

        return create_response(500, {'error': str(e), 'source': 'wazuh-security'})

def process_wazuh_event(event: Dict[str, Any]) -> Dict[str, Any]:
    """Process Wazuh-specific event structure"""
    try:
        # Handle CloudWatch Logs from Wazuh
        if 'awslogs' in event:
            cw_data = event['awslogs']['data']
            compressed_payload = base64.b64decode(cw_data)
            uncompressed_payload = gzip.decompress(compressed_payload)
            log_data = json.loads(uncompressed_payload)

            # Parse Wazuh alerts from log events
            alerts = []
            for log_event in log_data.get('logEvents', []):
                try:
                    alert_data = json.loads(log_event['message'])
                    if 'rule' in alert_data:  # Wazuh alert structure
                        alerts.append(alert_data)
                except json.JSONDecodeError:
                    # Handle non-JSON Wazuh logs
                    alerts.append({
                        'timestamp': log_event['timestamp'],
                        'message': log_event['message'],
                        'rule': {'level': 1, 'description': 'Raw log entry'}
                    })

            return {
                'source': 'wazuh',
                'log_group': log_data['logGroup'],
                'log_stream': log_data['logStream'],
                'alerts': alerts,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'agent_count': len(set([alert.get('agent', {}).get('id') for alert in alerts if alert.get('agent')]))
            }

        # Handle direct Wazuh API events
        elif 'alerts' in event or 'rule' in event:
            alerts = event.get('alerts', [event] if 'rule' in event else [])
            return {
                'source': 'wazuh',
                'alerts': alerts,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'agent_count': len(set([alert.get('agent', {}).get('id') for alert in alerts if alert.get('agent')]))
            }

        else:
            logger.warning("Unknown Wazuh event format")
            return {
                'source': 'wazuh',
                'alerts': [event],
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'agent_count': 1
            }

    except Exception as e:
        logger.error(f"Error processing Wazuh event: {e}")
        return None

def enrich_with_threat_intel(wazuh_data: Dict[str, Any]) -> Dict[str, Any]:
    """Enrich Wazuh alerts with threat intelligence"""
    # Placeholder for threat intelligence enrichment
    # In production, this would integrate with threat intel feeds

    for alert in wazuh_data.get('alerts', []):
        # Add threat intel context
        alert['threat_intel'] = {
            'enriched': True,
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'sources': ['internal_feeds']  # Would be actual threat intel sources
        }

        # Check for known IOCs
        if 'data' in alert:
            alert['threat_intel']['ioc_match'] = check_iocs(alert['data'])

    return wazuh_data

def check_iocs(alert_data: str) -> bool:
    """Check alert data against known Indicators of Compromise"""
    # Placeholder for IOC checking logic
    # In production, this would check against threat intel databases

    suspicious_patterns = [
        'malware', 'trojan', 'backdoor', 'exploit', 'ransomware',
        'phishing', 'botnet', 'c2', 'command and control'
    ]

    alert_text = str(alert_data).lower()
    return any(pattern in alert_text for pattern in suspicious_patterns)

def generate_wazuh_metadata(wazuh_data: Dict[str, Any]) -> Dict[str, str]:
    """Generate Wazuh-specific compliance metadata"""
    timestamp = datetime.now(timezone.utc)
    alerts = wazuh_data.get('alerts', [])

    # Calculate severity metrics
    severity_levels = [alert.get('rule', {}).get('level', 0) for alert in alerts]
    max_severity = max(severity_levels) if severity_levels else 0
    avg_severity = sum(severity_levels) / len(severity_levels) if severity_levels else 0

    metadata = {
        'source-system': 'wazuh',
        'log-type': 'security-alerts',
        'ingestion-timestamp': timestamp.isoformat(),
        'data-classification': 'confidential',
        'security-classification': 'confidential',
        'compliance-frameworks': 'POPIA,GDPR,SOX,HIPAA,PCI-DSS',
        'alert-count': str(len(alerts)),
        'max-severity-level': str(max_severity),
        'avg-severity-level': f"{avg_severity:.2f}",
        'agent-count': str(wazuh_data.get('agent_count', 0)),
        'checksum': generate_checksum(wazuh_data),
        'processor-version': '2.0-wazuh',
        'encryption-status': 'encrypted',
        'threat-intel-enriched': str(os.environ.get('ENABLE_THREAT_INTEL', 'false')).lower()
    }

    # Add high-severity flag
    if max_severity >= int(os.environ.get('WAZUH_ALERT_THRESHOLD', '10')):
        metadata['high-severity-alert'] = 'true'
        metadata['requires-investigation'] = 'true'

    return metadata

def generate_wazuh_s3_key(wazuh_data: Dict[str, Any], metadata: Dict[str, str]) -> str:
    """Generate Wazuh-specific S3 key with security-focused partitioning"""
    timestamp = datetime.now(timezone.utc)
    checksum_short = metadata['checksum'][:8]
    max_severity = metadata.get('max-severity-level', '0')

    # Partition by severity for security analysis
    severity_tier = 'critical' if int(max_severity) >= 12 else 'high' if int(max_severity) >= 8 else 'medium' if int(max_severity) >= 4 else 'low'

    key = (f"wazuh-security-logs/"
           f"severity={severity_tier}/"
           f"year={timestamp.year}/"
           f"month={timestamp.month:02d}/"
           f"day={timestamp.day:02d}/"
           f"hour={timestamp.hour:02d}/"
           f"wazuh_{timestamp.strftime('%Y%m%d_%H%M%S')}_{checksum_short}.json.gz")

    return key

def requires_legal_hold(wazuh_data: Dict[str, Any], threshold: int) -> bool:
    """Determine if Wazuh alerts require legal hold"""
    alerts = wazuh_data.get('alerts', [])

    for alert in alerts:
        rule_level = alert.get('rule', {}).get('level', 0)

        # High severity alerts
        if rule_level >= threshold:
            return True

        # Specific security events that always require legal hold
        rule_description = alert.get('rule', {}).get('description', '').lower()
        critical_events = [
            'data breach', 'unauthorized access', 'privilege escalation',
            'malware detected', 'intrusion attempt', 'data exfiltration',
            'ransomware', 'backdoor', 'command and control'
        ]

        if any(event in rule_description for event in critical_events):
            return True

    return False

def generate_checksum(data: Dict[str, Any]) -> str:
    """Generate SHA-256 checksum for data integrity"""
    data_string = json.dumps(data, sort_keys=True, default=str)
    return hashlib.sha256(data_string.encode()).hexdigest()

def compress_logs(log_data: Dict[str, Any]) -> bytes:
    """Compress log data for efficient storage"""
    json_data = json.dumps(log_data, separators=(',', ':'), default=str)
    return gzip.compress(json_data.encode('utf-8'))

def upload_to_s3(bucket: str, key: str, data: bytes, metadata: Dict[str, str], kms_key_id: str) -> Dict[str, Any]:
    """Upload compressed data to S3 with encryption and metadata"""
    return s3_client.put_object(
        Bucket=bucket,
        Key=key,
        Body=data,
        ServerSideEncryption='aws:kms',
        SSEKMSKeyId=kms_key_id,
        Metadata=metadata,
        ContentType='application/gzip',
        ContentEncoding='gzip',
        StorageClass='STANDARD'
    )

def send_to_dlq(event: Dict[str, Any], error_message: str, error_type: str) -> None:
    """Send failed events to dead letter queue with security context"""
    try:
        dlq_url = os.environ.get('DLQ_URL')
        if dlq_url:
            sqs_client.send_message(
                QueueUrl=dlq_url,
                MessageBody=json.dumps({
                    'original_event': event,
                    'error': error_message,
                    'error_type': error_type,
                    'timestamp': datetime.now(timezone.utc).isoformat(),
                    'processor': 'wazuh-security-log-shipper',
                    'requires_security_review': True
                })
            )
    except Exception as e:
        logger.error(f"Failed to send to DLQ: {e}")

def create_response(status_code: int, body: Any) -> Dict[str, Any]:
    """Create standardized Lambda response"""
    return {
        'statusCode': status_code,
        'body': json.dumps(body, default=str) if not isinstance(body, str) else body,
        'headers': {
            'Content-Type': 'application/json'
        }
    }
