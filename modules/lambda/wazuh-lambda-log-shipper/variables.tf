variable "name_prefix" {
  type        = string
  description = "Prefix for resource names"
}

variable "wazuh_s3_bucket" {
  type        = string
  description = "S3 bucket name for Wazuh log storage"
}

variable "legal_hold_s3_bucket" {
  type        = string
  description = "S3 bucket name for legal hold storage"
}

variable "kms_key_id" {
  type        = string
  description = "KMS key ID for encryption"
}

variable "kms_key_arn" {
  type        = string
  description = "KMS key ARN for encryption"
}

variable "environment" {
  type        = string
  description = "Environment name"
}

variable "wazuh_server_endpoints" {
  type        = list(string)
  description = "List of Wazuh server endpoints for log collection"
  default     = []
}

variable "wazuh_alert_threshold" {
  type        = number
  description = "Wazuh rule level threshold for triggering legal hold"
  default     = 10
  validation {
    condition     = var.wazuh_alert_threshold >= 1 && var.wazuh_alert_threshold <= 15
    error_message = "Wazuh alert threshold must be between 1 and 15."
  }
}

variable "enable_threat_intelligence" {
  type        = bool
  description = "Enable threat intelligence enrichment for Wazuh logs"
  default     = true
}

variable "security_classification" {
  type        = string
  description = "Security classification for Wazuh logs"
  default     = "confidential"
  validation {
    condition     = contains(["internal", "confidential", "restricted", "top-secret"], var.security_classification)
    error_message = "Security classification must be one of: internal, confidential, restricted, top-secret."
  }
}

variable "compliance_frameworks" {
  type        = list(string)
  description = "Compliance frameworks for Wazuh logs"
  default     = ["POPIA", "GDPR", "SOX", "HIPAA", "PCI-DSS"]
}

variable "wazuh_log_types" {
  type        = list(string)
  description = "Types of Wazuh logs to process"
  default     = ["alerts", "events", "archives", "firewall", "authentication"]
}

variable "enable_real_time_processing" {
  type        = bool
  description = "Enable real-time processing for critical security events"
  default     = true
}

variable "security_alarm_sns_topics" {
  type        = list(string)
  description = "SNS topic ARNs for security alerts"
  default     = []
}

variable "lambda_log_retention_days" {
  type        = number
  description = "Retention period for Lambda logs in days"
  default     = 90
}

variable "enable_enhanced_monitoring" {
  type        = bool
  description = "Enable enhanced monitoring for Wazuh Lambda"
  default     = true
}

variable "tags" {
  type        = map(string)
  description = "Tags to apply to all Wazuh Lambda resources"
  default     = {}
}
