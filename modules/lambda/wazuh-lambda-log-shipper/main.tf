# Wazuh-specific Lambda function for security log processing and shipping
resource "aws_lambda_function" "wazuh_log_shipper" {
  filename      = data.archive_file.lambda_zip.output_path
  function_name = "${var.name_prefix}-wazuh-log-shipper"
  role          = aws_iam_role.wazuh_lambda_execution.arn
  handler       = "wazuh_log_shipper.lambda_handler"
  runtime       = "python3.9"
  timeout       = 300  # 5 minutes for security log processing
  memory_size   = 1024 # 1GB for processing large security logs

  source_code_hash = data.archive_file.lambda_zip.output_base64sha256

  environment {
    variables = {
      WAZUH_S3_BUCKET         = var.wazuh_s3_bucket
      LEGAL_HOLD_S3_BUCKET    = var.legal_hold_s3_bucket
      KMS_KEY_ID              = var.kms_key_id
      LOG_LEVEL               = "INFO"
      SECURITY_CLASSIFICATION = "confidential"
      COMPLIANCE_MODE         = "strict"
      WAZUH_ALERT_THRESHOLD   = "10" # Rule level threshold for legal hold
      ENABLE_THREAT_INTEL     = "true"
      RETENTION_POLICY        = "permanent"
    }
  }

  dead_letter_config {
    target_arn = aws_sqs_queue.wazuh_dlq.arn
  }

  tags = merge(
    var.tags,
    {
      Purpose      = "wazuh-security-log-processing"
      LogSource    = "wazuh"
      DataClass    = "confidential"
      SecurityTool = "true"
    }
  )
}

# Create Lambda deployment package
data "archive_file" "lambda_zip" {
  type        = "zip"
  source_dir  = "${path.module}/src"
  output_path = "${path.module}/wazuh_log_shipper.zip"
}

# IAM role for Wazuh Lambda execution
resource "aws_iam_role" "wazuh_lambda_execution" {
  name = "${var.name_prefix}-wazuh-lambda-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

# IAM policy for Wazuh Lambda execution (security-focused)
resource "aws_iam_role_policy" "wazuh_lambda_execution" {
  name = "${var.name_prefix}-wazuh-lambda-policy"
  role = aws_iam_role.wazuh_lambda_execution.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:*:*:*"
      },
      {
        Effect = "Allow"
        Action = [
          "s3:PutObject",
          "s3:PutObjectAcl",
          "s3:GetObject",
          "s3:ListBucket"
        ]
        Resource = [
          "arn:aws:s3:::${var.wazuh_s3_bucket}",
          "arn:aws:s3:::${var.wazuh_s3_bucket}/*"
        ]
        Condition = {
          StringEquals = {
            "s3:x-amz-server-side-encryption" = "aws:kms"
          }
        }
      },
      {
        Effect = "Allow"
        Action = [
          "s3:PutObject",
          "s3:PutObjectLegalHold",
          "s3:PutObjectRetention"
        ]
        Resource = [
          "arn:aws:s3:::${var.legal_hold_s3_bucket}",
          "arn:aws:s3:::${var.legal_hold_s3_bucket}/*"
        ]
        Condition = {
          StringEquals = {
            "s3:x-amz-server-side-encryption" = "aws:kms"
          }
        }
      },
      {
        Effect = "Allow"
        Action = [
          "kms:Encrypt",
          "kms:Decrypt",
          "kms:ReEncrypt*",
          "kms:GenerateDataKey*",
          "kms:DescribeKey"
        ]
        Resource = [var.kms_key_arn]
      },
      {
        Effect = "Allow"
        Action = [
          "sqs:SendMessage"
        ]
        Resource = [aws_sqs_queue.wazuh_dlq.arn]
      }
    ]
  })
}

# Attach basic execution role
resource "aws_iam_role_policy_attachment" "wazuh_lambda_basic" {
  role       = aws_iam_role.wazuh_lambda_execution.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

# Dead Letter Queue for failed Wazuh log processing
resource "aws_sqs_queue" "wazuh_dlq" {
  name = "${var.name_prefix}-wazuh-log-shipper-dlq"

  kms_master_key_id         = var.kms_key_id
  message_retention_seconds = 1209600 # 14 days

  tags = merge(
    var.tags,
    {
      Purpose = "wazuh-lambda-dead-letter-queue"
    }
  )
}

# CloudWatch Log Group for Wazuh Lambda
resource "aws_cloudwatch_log_group" "wazuh_lambda_logs" {
  name              = "/aws/lambda/${aws_lambda_function.wazuh_log_shipper.function_name}"
  retention_in_days = 90 # Extended retention for security logs
  kms_key_id        = var.kms_key_id

  tags = var.tags
}

# Lambda permission for CloudWatch Logs
resource "aws_lambda_permission" "wazuh_cloudwatch_logs" {
  statement_id  = "AllowExecutionFromCloudWatchLogs"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.wazuh_log_shipper.function_name
  principal     = "logs.amazonaws.com"
}

# CloudWatch alarms for Wazuh Lambda monitoring
resource "aws_cloudwatch_metric_alarm" "wazuh_lambda_errors" {
  alarm_name          = "${var.name_prefix}-wazuh-lambda-errors"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "Errors"
  namespace           = "AWS/Lambda"
  period              = "300"
  statistic           = "Sum"
  threshold           = "1" # Alert on any errors for security logs
  alarm_description   = "Wazuh Lambda function errors"
  alarm_actions       = var.security_alarm_sns_topics

  dimensions = {
    FunctionName = aws_lambda_function.wazuh_log_shipper.function_name
  }

  tags = var.tags
}

resource "aws_cloudwatch_metric_alarm" "wazuh_lambda_duration" {
  alarm_name          = "${var.name_prefix}-wazuh-lambda-duration"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "Duration"
  namespace           = "AWS/Lambda"
  period              = "300"
  statistic           = "Average"
  threshold           = "240000" # 4 minutes (80% of 5 min timeout)
  alarm_description   = "Wazuh Lambda function duration"
  alarm_actions       = var.security_alarm_sns_topics

  dimensions = {
    FunctionName = aws_lambda_function.wazuh_log_shipper.function_name
  }

  tags = var.tags
}
