output "function_arn" {
  description = "ARN of the Wazuh Lambda function"
  value       = aws_lambda_function.wazuh_log_shipper.arn
}

output "function_name" {
  description = "Name of the Wazuh Lambda function"
  value       = aws_lambda_function.wazuh_log_shipper.function_name
}

output "function_invoke_arn" {
  description = "Invoke ARN of the Wazuh Lambda function"
  value       = aws_lambda_function.wazuh_log_shipper.invoke_arn
}

output "role_arn" {
  description = "ARN of the Wazuh Lambda execution role"
  value       = aws_iam_role.wazuh_lambda_execution.arn
}

output "role_name" {
  description = "Name of the Wazuh Lambda execution role"
  value       = aws_iam_role.wazuh_lambda_execution.name
}

output "dead_letter_queue_arn" {
  description = "ARN of the Wazuh dead letter queue"
  value       = aws_sqs_queue.wazuh_dlq.arn
}

output "dead_letter_queue_url" {
  description = "URL of the Wazuh dead letter queue"
  value       = aws_sqs_queue.wazuh_dlq.url
}

output "cloudwatch_log_group_name" {
  description = "Name of the Wazuh Lambda CloudWatch log group"
  value       = aws_cloudwatch_log_group.wazuh_lambda_logs.name
}

output "cloudwatch_log_group_arn" {
  description = "ARN of the Wazuh Lambda CloudWatch log group"
  value       = aws_cloudwatch_log_group.wazuh_lambda_logs.arn
}

output "security_configuration" {
  description = "Security configuration summary for Wazuh Lambda"
  value = {
    function_name           = aws_lambda_function.wazuh_log_shipper.function_name
    security_classification = var.security_classification
    compliance_frameworks   = var.compliance_frameworks
    threat_intelligence     = var.enable_threat_intelligence
    alert_threshold         = var.wazuh_alert_threshold
    real_time_processing    = var.enable_real_time_processing
    log_types               = var.wazuh_log_types
  }
}

output "monitoring_configuration" {
  description = "Monitoring configuration for Wazuh Lambda"
  value = {
    enhanced_monitoring = var.enable_enhanced_monitoring
    error_alarm_name    = aws_cloudwatch_metric_alarm.wazuh_lambda_errors.alarm_name
    duration_alarm_name = aws_cloudwatch_metric_alarm.wazuh_lambda_duration.alarm_name
    log_retention_days  = var.lambda_log_retention_days
    security_alerts     = length(var.security_alarm_sns_topics) > 0
  }
}

output "integration_endpoints" {
  description = "Integration endpoints for Wazuh log shipping"
  value = {
    wazuh_s3_bucket      = var.wazuh_s3_bucket
    legal_hold_s3_bucket = var.legal_hold_s3_bucket
    lambda_function_arn  = aws_lambda_function.wazuh_log_shipper.arn
    dead_letter_queue    = aws_sqs_queue.wazuh_dlq.url
  }
}
