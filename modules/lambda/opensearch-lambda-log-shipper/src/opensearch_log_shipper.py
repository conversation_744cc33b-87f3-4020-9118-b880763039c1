import json
import boto3
import gzip
import base64
import os
import logging
from datetime import datetime, timezone
import hashlib
from typing import Dict, List, Any, Optional
import urllib.request
import urllib.parse
import ssl

# Configure logging
log_level = os.environ.get('LOG_LEVEL', 'INFO')
logging.basicConfig(level=getattr(logging, log_level))
logger = logging.getLogger(__name__)

# AWS clients
s3_client = boto3.client('s3')
sqs_client = boto3.client('sqs')

def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Comprehensive log collection system:
    1. Collects logs from ALL CloudWatch log groups
    2. Exports to S3 bucket
    3. Processes S3 files and sends to OpenSearch
    """

    # Configuration
    OPENSEARCH_S3_BUCKET = os.environ['OPENSEARCH_S3_BUCKET']
    OPENSEARCH_ENDPOINTS = os.environ.get('O<PERSON>EN<PERSON>AR<PERSON>_ENDPOINTS', '').split(',')
    K<PERSON>_KEY_ID = os.environ.get('KMS_KEY_ID')

    try:
        logger.info(f"Processing event: {json.dumps(event, default=str)}")

        # Check if this is an S3 event (step 3: S3 to OpenSearch)
        if 'Records' in event and event['Records'] and 's3' in event['Records'][0]:
            logger.info("Processing S3 event - sending to OpenSearch")
            return process_s3_event(event, OPENSEARCH_ENDPOINTS[0] if OPENSEARCH_ENDPOINTS and OPENSEARCH_ENDPOINTS[0] else None)

        # Check if this is a scheduled event (step 1: CloudWatch to S3)
        if 'source' in event and event['source'] == 'aws.events':
            logger.info("Processing scheduled event - collecting all CloudWatch logs")
            return collect_all_cloudwatch_logs(OPENSEARCH_S3_BUCKET, KMS_KEY_ID)

        # Default: process individual CloudWatch log event
        logger.info("Processing individual CloudWatch log event")
        return process_individual_log_event(event, OPENSEARCH_S3_BUCKET, KMS_KEY_ID)
        compressed_data = compress_logs(opensearch_data)

        # Generate S3 key with OpenSearch-specific partitioning
        s3_key = generate_opensearch_s3_key(opensearch_data, metadata)

        # Upload to OpenSearch S3 bucket
        upload_response = upload_to_s3(
            OPENSEARCH_S3_BUCKET, s3_key, compressed_data, metadata, KMS_KEY_ID
        )

        # Check if legal hold is required for critical application events
        if requires_legal_hold(opensearch_data):
            legal_hold_key = f"opensearch-critical-event/{s3_key}"
            legal_metadata = {**metadata, 'legal-hold': 'true', 'hold-reason': 'critical-application-event'}

            upload_to_s3(
                LEGAL_HOLD_S3_BUCKET, legal_hold_key, compressed_data,
                legal_metadata, KMS_KEY_ID
            )
            logger.info(f"Legal hold applied for critical OpenSearch event: {legal_hold_key}")

        # Log success metrics
        log_count = len(opensearch_data.get('logs', []))
        log_types_processed = set([log.get('type', 'unknown') for log in opensearch_data.get('logs', [])])

        logger.info(f"Successfully processed {log_count} OpenSearch logs, types: {log_types_processed}")

        return create_response(200, {
            'message': 'OpenSearch application logs successfully processed',
            's3_key': s3_key,
            'logs_processed': log_count,
            'log_types': list(log_types_processed),
            'legal_hold_applied': requires_legal_hold(opensearch_data),
            'cost_optimized': ENABLE_COST_OPTIMIZATION
        })

    except Exception as e:
        logger.error(f"Error processing OpenSearch logs: {str(e)}", exc_info=True)

        # Send to DLQ for platform team review
        send_to_dlq(event, str(e), "opensearch-application-processing-error")

        return create_response(500, {'error': str(e), 'source': 'opensearch-application'})

def process_opensearch_event(event: Dict[str, Any]) -> Dict[str, Any]:
    """Process OpenSearch-specific event structure"""
    try:
        # Handle CloudWatch Logs from OpenSearch
        if 'awslogs' in event:
            cw_data = event['awslogs']['data']
            compressed_payload = base64.b64decode(cw_data)
            uncompressed_payload = gzip.decompress(compressed_payload)
            log_data = json.loads(uncompressed_payload)

            # Parse OpenSearch logs from log events
            logs = []
            for log_event in log_data.get('logEvents', []):
                try:
                    # Try to parse as JSON (structured OpenSearch logs)
                    log_entry = json.loads(log_event['message'])
                    log_entry['timestamp'] = log_event['timestamp']
                    logs.append(log_entry)
                except json.JSONDecodeError:
                    # Handle plain text OpenSearch logs
                    logs.append({
                        'timestamp': log_event['timestamp'],
                        'message': log_event['message'],
                        'type': determine_log_type(log_event['message']),
                        'level': 'INFO'
                    })

            return {
                'source': 'opensearch',
                'log_group': log_data['logGroup'],
                'log_stream': log_data['logStream'],
                'logs': logs,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'cluster_info': extract_cluster_info(log_data.get('logGroup', ''))
            }

        # Handle direct OpenSearch API events
        elif 'logs' in event or 'index' in event:
            logs = event.get('logs', [event] if 'index' in event else [])
            return {
                'source': 'opensearch',
                'logs': logs,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'cluster_info': event.get('cluster', {})
            }

        else:
            logger.warning("Unknown OpenSearch event format")
            return {
                'source': 'opensearch',
                'logs': [event],
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'cluster_info': {}
            }

    except Exception as e:
        logger.error(f"Error processing OpenSearch event: {e}")
        return None

def determine_log_type(message: str) -> str:
    """Determine OpenSearch log type from message content"""
    message_lower = message.lower()

    if 'search' in message_lower or 'query' in message_lower:
        return 'search'
    elif 'index' in message_lower:
        return 'index'
    elif 'cluster' in message_lower or 'node' in message_lower:
        return 'cluster'
    elif 'error' in message_lower or 'exception' in message_lower:
        return 'error'
    else:
        return 'application'

def extract_cluster_info(log_group: str) -> Dict[str, str]:
    """Extract cluster information from log group name"""
    # Example: /aws/opensearch/domains/infodocs-central-opensearch
    parts = log_group.split('/')
    if len(parts) >= 4 and 'opensearch' in parts:
        return {
            'domain': parts[-1] if len(parts) > 0 else 'unknown',
            'service': 'opensearch'
        }
    return {'domain': 'unknown', 'service': 'opensearch'}

def apply_cost_optimization(opensearch_data: Dict[str, Any]) -> Dict[str, Any]:
    """Apply cost optimization by filtering and sampling logs"""
    logs = opensearch_data.get('logs', [])

    # Filter out verbose debug logs to reduce storage costs
    filtered_logs = []
    for log in logs:
        log_level = log.get('level', 'INFO').upper()
        log_type = log.get('type', 'application')

        # Keep all error and warning logs
        if log_level in ['ERROR', 'WARN', 'WARNING']:
            filtered_logs.append(log)
        # Sample INFO logs (keep every 10th for application logs)
        elif log_level == 'INFO' and log_type == 'application':
            if hash(str(log.get('timestamp', ''))) % 10 == 0:
                filtered_logs.append(log)
        # Keep all search and cluster logs (business critical)
        elif log_type in ['search', 'cluster', 'index']:
            filtered_logs.append(log)
        # Skip DEBUG logs entirely for cost optimization

    opensearch_data['logs'] = filtered_logs
    opensearch_data['cost_optimization'] = {
        'original_count': len(logs),
        'filtered_count': len(filtered_logs),
        'reduction_percentage': round((1 - len(filtered_logs) / len(logs)) * 100, 2) if logs else 0
    }

    return opensearch_data

def generate_opensearch_metadata(opensearch_data: Dict[str, Any]) -> Dict[str, str]:
    """Generate OpenSearch-specific compliance metadata"""
    timestamp = datetime.now(timezone.utc)
    logs = opensearch_data.get('logs', [])

    # Calculate log type distribution
    log_types = {}
    error_count = 0
    for log in logs:
        log_type = log.get('type', 'unknown')
        log_types[log_type] = log_types.get(log_type, 0) + 1
        if log.get('level', '').upper() in ['ERROR', 'WARN', 'WARNING']:
            error_count += 1

    metadata = {
        'source-system': 'opensearch',
        'log-type': 'application-logs',
        'ingestion-timestamp': timestamp.isoformat(),
        'data-classification': 'confidential',
        'compliance-frameworks': 'POPIA,GDPR,SOX',
        'log-count': str(len(logs)),
        'error-count': str(error_count),
        'log-types': ','.join(log_types.keys()),
        'cluster-domain': opensearch_data.get('cluster_info', {}).get('domain', 'unknown'),
        'checksum': generate_checksum(opensearch_data),
        'processor-version': '2.0-opensearch',
        'encryption-status': 'encrypted',
        'cost-optimized': str(os.environ.get('ENABLE_COST_OPTIMIZATION', 'false')).lower()
    }

    # Add cost optimization metrics if available
    if 'cost_optimization' in opensearch_data:
        cost_opt = opensearch_data['cost_optimization']
        metadata['original-log-count'] = str(cost_opt['original_count'])
        metadata['reduction-percentage'] = str(cost_opt['reduction_percentage'])

    return metadata

def generate_opensearch_s3_key(opensearch_data: Dict[str, Any], metadata: Dict[str, str]) -> str:
    """Generate OpenSearch-specific S3 key with application-focused partitioning"""
    timestamp = datetime.now(timezone.utc)
    checksum_short = metadata['checksum'][:8]
    cluster_domain = metadata.get('cluster-domain', 'unknown')

    # Partition by cluster and log type for application analysis
    primary_log_type = max(
        [log.get('type', 'application') for log in opensearch_data.get('logs', [])],
        key=lambda x: [log.get('type') for log in opensearch_data.get('logs', [])].count(x),
        default='application'
    )

    key = (f"opensearch-application-logs/"
           f"cluster={cluster_domain}/"
           f"log-type={primary_log_type}/"
           f"year={timestamp.year}/"
           f"month={timestamp.month:02d}/"
           f"day={timestamp.day:02d}/"
           f"hour={timestamp.hour:02d}/"
           f"opensearch_{timestamp.strftime('%Y%m%d_%H%M%S')}_{checksum_short}.json.gz")

    return key

def requires_legal_hold(opensearch_data: Dict[str, Any]) -> bool:
    """Determine if OpenSearch logs require legal hold"""
    logs = opensearch_data.get('logs', [])

    for log in logs:
        # Critical application errors
        if log.get('level', '').upper() == 'ERROR':
            message = log.get('message', '').lower()
            critical_events = [
                'data corruption', 'index corruption', 'cluster failure',
                'unauthorized access', 'security breach', 'data loss'
            ]

            if any(event in message for event in critical_events):
                return True

        # High-volume search anomalies (potential attacks)
        if log.get('type') == 'search':
            # Check for suspicious search patterns
            message = log.get('message', '').lower()
            if 'injection' in message or 'exploit' in message:
                return True

    return False

def generate_checksum(data: Dict[str, Any]) -> str:
    """Generate SHA-256 checksum for data integrity"""
    data_string = json.dumps(data, sort_keys=True, default=str)
    return hashlib.sha256(data_string.encode()).hexdigest()

def compress_logs(log_data: Dict[str, Any]) -> bytes:
    """Compress log data for efficient storage"""
    json_data = json.dumps(log_data, separators=(',', ':'), default=str)
    return gzip.compress(json_data.encode('utf-8'))

def upload_to_s3(bucket: str, key: str, data: bytes, metadata: Dict[str, str], kms_key_id: str) -> Dict[str, Any]:
    """Upload compressed data to S3 with encryption and metadata"""

    # Base parameters
    put_params = {
        'Bucket': bucket,
        'Key': key,
        'Body': data,
        'Metadata': metadata,
        'ContentType': 'application/gzip',
        'ContentEncoding': 'gzip',
        'StorageClass': 'STANDARD'
    }

    # Add KMS encryption only if key is provided
    if kms_key_id and kms_key_id.strip():
        put_params['ServerSideEncryption'] = 'aws:kms'
        put_params['SSEKMSKeyId'] = kms_key_id
    else:
        # Use default S3 encryption
        put_params['ServerSideEncryption'] = 'AES256'

    return s3_client.put_object(**put_params)

def send_to_dlq(event: Dict[str, Any], error_message: str, error_type: str) -> None:
    """Send failed events to dead letter queue"""
    try:
        dlq_url = os.environ.get('DLQ_URL')
        if dlq_url:
            sqs_client.send_message(
                QueueUrl=dlq_url,
                MessageBody=json.dumps({
                    'original_event': event,
                    'error': error_message,
                    'error_type': error_type,
                    'timestamp': datetime.now(timezone.utc).isoformat(),
                    'processor': 'opensearch-application-log-shipper'
                })
            )
    except Exception as e:
        logger.error(f"Failed to send to DLQ: {e}")

def process_s3_event(event: Dict[str, Any], opensearch_endpoint: str) -> Dict[str, Any]:
    """Process S3 event and send data to OpenSearch"""
    try:
        processed_files = 0

        for record in event['Records']:
            bucket_name = record['s3']['bucket']['name']
            object_key = record['s3']['object']['key']

            logger.info(f"Processing S3 object: s3://{bucket_name}/{object_key}")

            # Download and process the file
            response = s3_client.get_object(Bucket=bucket_name, Key=object_key)

            # Handle gzipped files
            if object_key.endswith('.gz'):
                content = gzip.decompress(response['Body'].read()).decode('utf-8')
            else:
                content = response['Body'].read().decode('utf-8')

            # Parse log entries
            log_entries = []
            for line in content.strip().split('\n'):
                if line.strip():
                    try:
                        # Try to parse as JSON
                        log_entry = json.loads(line)
                        log_entries.append(log_entry)
                    except json.JSONDecodeError:
                        # Handle plain text logs
                        log_entries.append({
                            'message': line,
                            'timestamp': datetime.now(timezone.utc).isoformat(),
                            'source': 'cloudwatch-export'
                        })

            # Send to OpenSearch
            if opensearch_endpoint and log_entries:
                send_to_opensearch(log_entries, opensearch_endpoint, object_key)
                processed_files += 1
                logger.info(f"Sent {len(log_entries)} log entries to OpenSearch")

        return create_response(200, f"Successfully processed {processed_files} files")

    except Exception as e:
        logger.error(f"Error processing S3 event: {e}")
        return create_response(500, f"Error: {str(e)}")

def send_to_opensearch(log_entries: List[Dict], endpoint: str, source_key: str):
    """Send log entries to OpenSearch"""
    try:
        # Create index name from source key
        index_name = f"cloudwatch-logs-{datetime.now().strftime('%Y.%m.%d')}"

        # Bulk insert to OpenSearch
        bulk_data = []
        for entry in log_entries:
            bulk_data.append(json.dumps({"index": {"_index": index_name}}))
            bulk_data.append(json.dumps(entry))

        bulk_body = '\n'.join(bulk_data) + '\n'

        # Send to OpenSearch using built-in urllib
        url = f"{endpoint}/_bulk"

        # Create request
        req = urllib.request.Request(url, data=bulk_body.encode('utf-8'))
        req.add_header('Content-Type', 'application/x-ndjson')

        # Disable SSL verification for VPC endpoint
        ctx = ssl.create_default_context()
        ctx.check_hostname = False
        ctx.verify_mode = ssl.CERT_NONE

        # Send request
        with urllib.request.urlopen(req, context=ctx) as response:
            if response.status == 200:
                logger.info(f"Successfully sent {len(log_entries)} entries to OpenSearch index {index_name}")
            else:
                logger.error(f"Failed to send to OpenSearch: {response.status} - {response.read().decode('utf-8')}")

    except Exception as e:
        logger.error(f"Error sending to OpenSearch: {e}")

def collect_all_cloudwatch_logs(s3_bucket: str, kms_key_id: str) -> Dict[str, Any]:
    """Collect logs from ALL CloudWatch log groups and export to S3 for retention"""
    try:
        logs_client = boto3.client('logs')
        exported_groups = []

        # Get all log groups in the account
        paginator = logs_client.get_paginator('describe_log_groups')

        for page in paginator.paginate():
            for log_group in page['logGroups']:
                log_group_name = log_group['logGroupName']

                try:
                    logger.info(f"Exporting log group to S3: {log_group_name}")

                    # Calculate time range - last 30 days for comprehensive collection
                    end_time = int(datetime.now().timestamp() * 1000)
                    start_time = end_time - (30 * 24 * 60 * 60 * 1000)  # 30 days ago

                    # Create S3 destination key with proper structure
                    date_str = datetime.now().strftime('%Y/%m/%d')
                    s3_prefix = f"cloudwatch-logs/{date_str}/{log_group_name.replace('/', '-')}"

                    # Always export - we want fresh data every time
                    logger.info(f"Exporting log group: {log_group_name}")

                    # Export log group to S3 (without KMS to avoid permission issues)
                    response = logs_client.create_export_task(
                        logGroupName=log_group_name,
                        fromTime=start_time,
                        to=end_time,
                        destination=s3_bucket,
                        destinationPrefix=s3_prefix
                    )

                    exported_groups.append({
                        'logGroup': log_group_name,
                        'taskId': response['taskId'],
                        's3Prefix': s3_prefix,
                        'status': 'RUNNING'
                    })

                    logger.info(f"Started S3 export for log group: {log_group_name} -> s3://{s3_bucket}/{s3_prefix}")

                except Exception as e:
                    logger.error(f"Failed to export log group {log_group_name}: {e}")
                    continue

        return create_response(200, {
            'message': f'Started S3 export for {len(exported_groups)} log groups',
            'exportedGroups': exported_groups,
            'note': 'S3 exports will trigger automatic processing to OpenSearch when complete'
        })

    except Exception as e:
        logger.error(f"Error collecting CloudWatch logs: {e}")
        return create_response(500, f"Error: {str(e)}")

def process_individual_log_event(event: Dict[str, Any], s3_bucket: str, kms_key_id: str) -> Dict[str, Any]:
    """Process individual CloudWatch log event and export to S3"""
    try:
        # This handles individual log events if needed
        logger.info("Processing individual log event")
        return create_response(200, "Individual log event processed")

    except Exception as e:
        logger.error(f"Error processing individual log event: {e}")
        return create_response(500, f"Error: {str(e)}")

def create_response(status_code: int, body: Any) -> Dict[str, Any]:
    """Create standardized Lambda response"""
    return {
        'statusCode': status_code,
        'body': json.dumps(body, default=str) if not isinstance(body, str) else body,
        'headers': {
            'Content-Type': 'application/json'
        }
    }
