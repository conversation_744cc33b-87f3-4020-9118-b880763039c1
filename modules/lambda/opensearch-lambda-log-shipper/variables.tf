variable "name_prefix" {
  type        = string
  description = "Prefix for resource names"
}

variable "opensearch_s3_bucket" {
  type        = string
  description = "S3 bucket name for OpenSearch log storage"
}

variable "legal_hold_s3_bucket" {
  type        = string
  description = "S3 bucket name for legal hold storage"
}

variable "kms_key_id" {
  type        = string
  description = "KMS key ID for encryption"
}

variable "kms_key_arn" {
  type        = string
  description = "KMS key ARN for encryption"
}

variable "environment" {
  type        = string
  description = "Environment name"
}

variable "opensearch_cluster_endpoints" {
  type        = list(string)
  description = "List of OpenSearch cluster endpoints"
  default     = []
}

variable "opensearch_log_types" {
  type        = list(string)
  description = "Types of OpenSearch logs to process"
  default     = ["application", "search", "index", "cluster"]
}

variable "data_classification" {
  type        = string
  description = "Data classification for OpenSearch logs"
  default     = "confidential"
  validation {
    condition     = contains(["public", "internal", "confidential", "restricted"], var.data_classification)
    error_message = "Data classification must be one of: public, internal, confidential, restricted."
  }
}

variable "compliance_frameworks" {
  type        = list(string)
  description = "Compliance frameworks for OpenSearch logs"
  default     = ["POPIA", "GDPR", "SOX"]
}

variable "enable_cost_optimization" {
  type        = bool
  description = "Enable cost optimization features for OpenSearch logs"
  default     = true
}

variable "enable_monitoring" {
  type        = bool
  description = "Enable CloudWatch monitoring and alarms"
  default     = true
}

variable "alarm_sns_topics" {
  type        = list(string)
  description = "SNS topic ARNs for alarm notifications"
  default     = []
}

variable "lambda_log_retention_days" {
  type        = number
  description = "Retention period for Lambda logs in days"
  default     = 30
}

variable "tags" {
  type        = map(string)
  description = "Tags to apply to all OpenSearch Lambda resources"
  default     = {}
}

variable "vpc_subnet_ids" {
  type        = list(string)
  description = "VPC subnet IDs for Lambda function"
  default     = []
}

variable "vpc_security_group_ids" {
  type        = list(string)
  description = "VPC security group IDs for Lambda function"
  default     = []
}
