output "function_arn" {
  description = "ARN of the OpenSearch Lambda function"
  value       = aws_lambda_function.opensearch_log_shipper.arn
}

output "function_name" {
  description = "Name of the OpenSearch Lambda function"
  value       = aws_lambda_function.opensearch_log_shipper.function_name
}

output "function_invoke_arn" {
  description = "Invoke ARN of the OpenSearch Lambda function"
  value       = aws_lambda_function.opensearch_log_shipper.invoke_arn
}

output "role_arn" {
  description = "ARN of the OpenSearch Lambda execution role"
  value       = aws_iam_role.opensearch_lambda_execution.arn
}

output "role_name" {
  description = "Name of the OpenSearch Lambda execution role"
  value       = aws_iam_role.opensearch_lambda_execution.name
}

output "dead_letter_queue_arn" {
  description = "ARN of the OpenSearch dead letter queue"
  value       = aws_sqs_queue.opensearch_dlq.arn
}

output "dead_letter_queue_url" {
  description = "URL of the OpenSearch dead letter queue"
  value       = aws_sqs_queue.opensearch_dlq.url
}

output "cloudwatch_log_group_name" {
  description = "Name of the OpenSearch Lambda CloudWatch log group"
  value       = aws_cloudwatch_log_group.opensearch_lambda_logs.name
}

output "cloudwatch_log_group_arn" {
  description = "ARN of the OpenSearch Lambda CloudWatch log group"
  value       = aws_cloudwatch_log_group.opensearch_lambda_logs.arn
}

output "configuration_summary" {
  description = "Configuration summary for OpenSearch Lambda"
  value = {
    function_name         = aws_lambda_function.opensearch_log_shipper.function_name
    data_classification   = var.data_classification
    compliance_frameworks = var.compliance_frameworks
    cost_optimization     = var.enable_cost_optimization
    log_types             = var.opensearch_log_types
    monitoring_enabled    = var.enable_monitoring
  }
}

output "integration_endpoints" {
  description = "Integration endpoints for OpenSearch log shipping"
  value = {
    opensearch_s3_bucket = var.opensearch_s3_bucket
    legal_hold_s3_bucket = var.legal_hold_s3_bucket
    lambda_function_arn  = aws_lambda_function.opensearch_log_shipper.arn
    dead_letter_queue    = aws_sqs_queue.opensearch_dlq.url
  }
}
