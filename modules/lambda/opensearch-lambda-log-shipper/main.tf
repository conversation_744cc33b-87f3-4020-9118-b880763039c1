# Data sources to look up existing resources (avoids circular dependencies)
data "aws_opensearch_domain" "opensearch" {
  count       = length(var.opensearch_cluster_endpoints) == 0 ? 1 : 0
  domain_name = "${var.name_prefix}-os"
}

data "aws_s3_bucket" "opensearch_bucket" {
  count  = var.opensearch_s3_bucket == "" ? 1 : 0
  bucket = "${var.name_prefix}-opensearch"
}

# OpenSearch-specific Lambda function for application log processing and shipping
resource "aws_lambda_function" "opensearch_log_shipper" {
  filename      = data.archive_file.lambda_zip.output_path
  function_name = "${var.name_prefix}-opensearch-log-shipper"
  role          = aws_iam_role.opensearch_lambda_execution.arn
  handler       = "opensearch_log_shipper.lambda_handler"
  runtime       = "python3.9"
  timeout       = 300 # 5 minutes for log processing
  memory_size   = 512 # 512MB for application logs

  source_code_hash = data.archive_file.lambda_zip.output_base64sha256

  environment {
    variables = {
      OPENSEARCH_S3_BUCKET     = var.opensearch_s3_bucket != "" ? var.opensearch_s3_bucket : try(data.aws_s3_bucket.opensearch_bucket[0].bucket, "${var.name_prefix}-opensearch")
      LEGAL_HOLD_S3_BUCKET     = var.legal_hold_s3_bucket != "" ? var.legal_hold_s3_bucket : try(data.aws_s3_bucket.opensearch_bucket[0].bucket, "${var.name_prefix}-opensearch")
      OPENSEARCH_ENDPOINTS     = length(var.opensearch_cluster_endpoints) > 0 ? join(",", var.opensearch_cluster_endpoints) : try("https://${data.aws_opensearch_domain.opensearch[0].endpoint}", "")
      KMS_KEY_ID               = var.kms_key_id
      LOG_LEVEL                = "INFO"
      DATA_CLASSIFICATION      = "confidential"
      RETENTION_POLICY         = "long-term"
      ENABLE_COST_OPTIMIZATION = "true"
      LOG_TYPES                = join(",", var.opensearch_log_types)
    }
  }

  dead_letter_config {
    target_arn = aws_sqs_queue.opensearch_dlq.arn
  }

  # VPC Configuration (conditional)
  dynamic "vpc_config" {
    for_each = length(var.vpc_subnet_ids) > 0 ? [1] : []
    content {
      subnet_ids         = var.vpc_subnet_ids
      security_group_ids = var.vpc_security_group_ids
    }
  }

  tags = merge(
    var.tags,
    {
      Purpose       = "opensearch-application-log-processing"
      LogSource     = "opensearch"
      DataClass     = "confidential"
      CostOptimized = "true"
    }
  )
}

# S3 bucket notification to trigger Lambda on new objects
resource "aws_s3_bucket_notification" "opensearch_lambda_trigger" {
  bucket = var.opensearch_s3_bucket

  lambda_function {
    lambda_function_arn = aws_lambda_function.opensearch_log_shipper.arn
    events              = ["s3:ObjectCreated:*"]
    filter_prefix       = "complete-export/"
  }

  depends_on = [aws_lambda_permission.allow_s3_invoke]
}

# Permission for S3 to invoke Lambda
resource "aws_lambda_permission" "allow_s3_invoke" {
  statement_id  = "AllowExecutionFromS3Bucket"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.opensearch_log_shipper.function_name
  principal     = "s3.amazonaws.com"
  source_arn    = "arn:aws:s3:::${var.opensearch_s3_bucket}"
}

# CloudWatch Events rule to trigger hourly log collection
resource "aws_cloudwatch_event_rule" "daily_log_collection" {
  name                = "${var.name_prefix}-hourly-log-collection"
  description         = "Trigger hourly collection of all CloudWatch logs"
  schedule_expression = "rate(1 hour)"
}

# CloudWatch Events target
resource "aws_cloudwatch_event_target" "lambda_target" {
  rule      = aws_cloudwatch_event_rule.daily_log_collection.name
  target_id = "TriggerLambdaFunction"
  arn       = aws_lambda_function.opensearch_log_shipper.arn
}

# Permission for CloudWatch Events to invoke Lambda
resource "aws_lambda_permission" "allow_cloudwatch_invoke" {
  statement_id  = "AllowExecutionFromCloudWatch"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.opensearch_log_shipper.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.daily_log_collection.arn
}

# Create Lambda deployment package
data "archive_file" "lambda_zip" {
  type        = "zip"
  source_dir  = "${path.module}/src"
  output_path = "${path.module}/opensearch_log_shipper.zip"
}

# IAM role for OpenSearch Lambda execution
resource "aws_iam_role" "opensearch_lambda_execution" {
  name = "${var.name_prefix}-opensearch-lambda-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

# IAM policy for OpenSearch Lambda execution
resource "aws_iam_role_policy" "opensearch_lambda_execution" {
  name = "${var.name_prefix}-opensearch-lambda-policy"
  role = aws_iam_role.opensearch_lambda_execution.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:*:*:*"
      },
      {
        Effect = "Allow"
        Action = [
          "logs:DescribeLogGroups",
          "logs:DescribeLogStreams",
          "logs:DescribeExportTasks",
          "logs:CreateExportTask",
          "logs:FilterLogEvents"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "s3:PutObject",
          "s3:PutObjectAcl",
          "s3:GetObject",
          "s3:ListBucket",
          "s3:DeleteObject" # Allow deletion for application logs
        ]
        Resource = [
          "arn:aws:s3:::${var.opensearch_s3_bucket}",
          "arn:aws:s3:::${var.opensearch_s3_bucket}/*"
        ]
        Condition = {
          StringEquals = {
            "s3:x-amz-server-side-encryption" = "aws:kms"
          }
        }
      },
      {
        Effect = "Allow"
        Action = [
          "s3:PutObject",
          "s3:PutObjectLegalHold"
        ]
        Resource = [
          "arn:aws:s3:::${var.legal_hold_s3_bucket}",
          "arn:aws:s3:::${var.legal_hold_s3_bucket}/*"
        ]
        Condition = {
          StringEquals = {
            "s3:x-amz-server-side-encryption" = "aws:kms"
          }
        }
      },
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject"
        ]
        Resource = [
          "arn:aws:s3:::${var.opensearch_s3_bucket}/cloudwatch-logs/*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "kms:Decrypt",
          "kms:DescribeKey"
        ]
        Resource = [
          var.kms_key_arn
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "ec2:CreateNetworkInterface",
          "ec2:DescribeNetworkInterfaces",
          "ec2:DeleteNetworkInterface"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "ec2:CreateNetworkInterface",
          "ec2:DescribeNetworkInterfaces",
          "ec2:DeleteNetworkInterface",
          "ec2:AttachNetworkInterface",
          "ec2:DetachNetworkInterface"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "kms:Encrypt",
          "kms:Decrypt",
          "kms:ReEncrypt*",
          "kms:GenerateDataKey*",
          "kms:DescribeKey"
        ]
        Resource = [var.kms_key_arn]
      },
      {
        Effect = "Allow"
        Action = [
          "sqs:SendMessage"
        ]
        Resource = [aws_sqs_queue.opensearch_dlq.arn]
      }
    ]
  })
}

# Attach basic execution role
resource "aws_iam_role_policy_attachment" "opensearch_lambda_basic" {
  role       = aws_iam_role.opensearch_lambda_execution.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

# Dead Letter Queue for failed OpenSearch log processing
resource "aws_sqs_queue" "opensearch_dlq" {
  name = "${var.name_prefix}-opensearch-log-shipper-dlq"

  kms_master_key_id         = var.kms_key_id
  message_retention_seconds = 1209600 # 14 days

  tags = merge(
    var.tags,
    {
      Purpose = "opensearch-lambda-dead-letter-queue"
    }
  )
}

# CloudWatch Log Group for OpenSearch Lambda
resource "aws_cloudwatch_log_group" "opensearch_lambda_logs" {
  name              = "/aws/lambda/${aws_lambda_function.opensearch_log_shipper.function_name}"
  retention_in_days = var.lambda_log_retention_days
  kms_key_id        = var.kms_key_id

  tags = var.tags
}

# Lambda permission for CloudWatch Logs
resource "aws_lambda_permission" "opensearch_cloudwatch_logs" {
  statement_id  = "AllowExecutionFromCloudWatchLogs"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.opensearch_log_shipper.function_name
  principal     = "logs.amazonaws.com"
}

# CloudWatch alarms for OpenSearch Lambda monitoring
resource "aws_cloudwatch_metric_alarm" "opensearch_lambda_errors" {
  count = var.enable_monitoring ? 1 : 0

  alarm_name          = "${var.name_prefix}-opensearch-lambda-errors"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "Errors"
  namespace           = "AWS/Lambda"
  period              = "300"
  statistic           = "Sum"
  threshold           = "5" # More tolerant for application logs
  alarm_description   = "OpenSearch Lambda function errors"
  alarm_actions       = var.alarm_sns_topics

  dimensions = {
    FunctionName = aws_lambda_function.opensearch_log_shipper.function_name
  }

  tags = var.tags
}
