output "cloudflare_zone_id" {
  description = "ID of the Cloudflare zone"
  value       = data.aws_ssm_parameter.cloudflare_zone_id.value
  sensitive   = true
}

output "cloudflare_api_token" {
  description = "Cloudflare API token from SSM"
  value       = data.aws_ssm_parameter.cloudflare_api_token.value
  sensitive   = true
}

output "dns_records" {
  description = "Map of DNS record IDs and hostnames"
  value = {
    for key, record in cloudflare_record.records : key => {
      id       = record.id
      hostname = record.hostname
      name     = record.name
      content  = record.content
      type     = record.type
    }
  }
}
