# Cloudflare Module

This module manages Cloudflare DNS records, WAF rules, and security settings for InfoDocs domains.

## Features

- **DNS Management**: Create and manage DNS records
- **WAF Protection**: Web Application Firewall rules
- **DDoS Protection**: Mitigation of distributed denial-of-service attacks
- **SSL/TLS**: Secure HTTPS connections
- **Caching**: Content delivery optimization

## Usage

### Basic Usage

```hcl
module "cloudflare" {
  source = "../../modules/cloudflare"

  zone_id     = "your-cloudflare-zone-id"
  domain_name = "infodocs.co.za"
  environment = "dev"

  dns_records = [
    {
      name  = "@"
      type  = "A"
      value = "*********"
      ttl   = 3600
      proxied = true
    },
    {
      name  = "www"
      type  = "CNAME"
      value = "infodocs.co.za"
      ttl   = 3600
      proxied = true
    }
  ]

  tags = {
    Project     = "infodocs"
    CostCenter  = "infrastructure"
  }
}
```

## Migration from Route53

To migrate DNS records from Route53 to Cloudflare:

1. Export Route53 records using the provided script:
   ```bash
   ./scripts/route53-to-cloudflare.sh
   ```

2. Review the generated Terraform file

3. Apply using Terraform or import manually to Cloudflare

4. Verify records in Cloudflare before changing nameservers

5. Update nameservers at your domain registrar

## Security Considerations

- Enable WAF for production environments
- Use proxied records for enhanced security
- Configure rate limiting for API endpoints
- Enable DNSSEC for DNS security

## Cost Considerations

- **Free Tier**: Basic DNS management
- **Pro Plan**: ~$20/month for WAF and additional security
- **Enterprise**: Custom pricing for advanced features
- **Total Estimated Cost**: $0-20/month depending on plan

## Best Practices

1. **Proxied Records**: Enable proxying for public-facing records
2. **TTL Settings**: Use appropriate TTL values (lower for frequently changing records)
3. **Security Rules**: Configure WAF rules for sensitive endpoints
4. **Page Rules**: Optimize caching and security with page rules
5. **API Tokens**: Use scoped API tokens for automation
<!-- BEGINNING OF PRE-COMMIT-TERRAFORM DOCS HOOK -->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_cloudflare"></a> [cloudflare](#requirement\_cloudflare) | ~> 4.0 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | 6.0.0 |
| <a name="provider_cloudflare"></a> [cloudflare](#provider\_cloudflare) | 4.52.0 |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [cloudflare_page_rule.cache_everything](https://registry.terraform.io/providers/cloudflare/cloudflare/latest/docs/resources/page_rule) | resource |
| [cloudflare_page_rule.https_always](https://registry.terraform.io/providers/cloudflare/cloudflare/latest/docs/resources/page_rule) | resource |
| [cloudflare_ruleset.waf_managed_rules](https://registry.terraform.io/providers/cloudflare/cloudflare/latest/docs/resources/ruleset) | resource |
| [cloudflare_zone_settings_override.settings](https://registry.terraform.io/providers/cloudflare/cloudflare/latest/docs/resources/zone_settings_override) | resource |
| [aws_ssm_parameter.cloudflare_token1](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/ssm_parameter) | data source |
| [aws_ssm_parameter.cloudflare_token2](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/ssm_parameter) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_domain"></a> [domain](#input\_domain) | The domain name to manage in Cloudflare | `string` | n/a | yes |
| <a name="input_environment"></a> [environment](#input\_environment) | Environment name (e.g., dev, staging, prod) | `string` | n/a | yes |
| <a name="input_tags"></a> [tags](#input\_tags) | Tags to apply to resources | `map(string)` | `{}` | no |
| <a name="input_zone_id"></a> [zone\_id](#input\_zone\_id) | The Cloudflare Zone ID | `string` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_cloudflare_zone_id"></a> [cloudflare\_zone\_id](#output\_cloudflare\_zone\_id) | ID of the Cloudflare zone |
| <a name="output_page_rules"></a> [page\_rules](#output\_page\_rules) | IDs of the Cloudflare page rules |
| <a name="output_waf_ruleset_id"></a> [waf\_ruleset\_id](#output\_waf\_ruleset\_id) | ID of the Cloudflare WAF ruleset |
| <a name="output_zone_settings_id"></a> [zone\_settings\_id](#output\_zone\_settings\_id) | ID of the Cloudflare zone settings |
<!-- END OF PRE-COMMIT-TERRAFORM DOCS HOOK -->
