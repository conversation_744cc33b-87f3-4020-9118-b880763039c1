# Basic Cloudflare configuration

# Temporarily disabled - requires additional API token permissions
# resource "cloudflare_zone_settings_override" "settings" {
#   zone_id = data.aws_ssm_parameter.cloudflare_zone_id.value
#
#   settings {
#     ssl                      = "strict"
#     always_use_https         = "on"
#     min_tls_version          = "1.2"
#     opportunistic_encryption = "on"
#     tls_1_3                  = "on"
#     automatic_https_rewrites = "on"
#     universal_ssl            = "on"
#     browser_check            = "on"
#     challenge_ttl            = 1800
#     security_level           = "medium"
#     brotli                   = "on"
#     minify {
#       css  = "on"
#       js   = "on"
#       html = "on"
#     }
#   }
# }

# DNS records from variable configuration
resource "cloudflare_record" "records" {
  for_each = var.dns_records

  zone_id  = data.aws_ssm_parameter.cloudflare_zone_id.value
  name     = each.value.name
  content  = each.value.content
  type     = each.value.type
  ttl      = each.value.ttl
  proxied  = lookup(each.value, "proxied", false)
  priority = lookup(each.value, "priority", null)

  tags = [
    "Environment:${var.environment}",
    "Project:infodocs",
    "ManagedBy:opentofu"
  ]
}
