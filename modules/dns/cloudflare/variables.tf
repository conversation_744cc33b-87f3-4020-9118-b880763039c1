variable "domain" {
  type        = string
  description = "The domain name to manage in Cloudflare"
}

variable "environment" {
  type        = string
  description = "Environment name (dev, prod, operations)"
  validation {
    condition     = contains(["dev", "prod", "operations"], var.environment)
    error_message = "Environment must be one of: dev, prod, operations."
  }
}

variable "tags" {
  type        = map(string)
  description = "Tags to apply to resources"
  default     = {}
}

variable "ip_address" {
  type        = string
  description = "IP address for DNS records"
  default     = ""
}

variable "dns_records" {
  type = map(object({
    name     = string
    content  = string
    type     = string
    ttl      = number
    proxied  = optional(bool, false)
    priority = optional(number, null)
  }))
  description = "Map of DNS records to create in Cloudflare"
  default     = {}
}
