# Get AWS credentials for CloudWatch data source
data "aws_ssm_parameter" "grafana_aws_access_key" {
  name            = "/infodocs/grafana/aws_access_key_id"
  with_decryption = true
}

data "aws_ssm_parameter" "grafana_aws_secret_key" {
  name            = "/infodocs/grafana/aws_secret_access_key"
  with_decryption = true
}

# CloudWatch Data Source for AWS Infrastructure Metrics
resource "grafana_data_source" "cloudwatch" {
  type = "cloudwatch"
  name = "CloudWatch-${var.environment}"
  url  = "https://monitoring.${data.aws_region.current.name}.amazonaws.com"

  json_data_encoded = jsonencode({
    defaultRegion = data.aws_region.current.name
    authType      = "keys"
    customMetricsNamespaces = join(",", [
      "AWS/EC2",
      "AWS/Lambda",
      "AWS/RDS",
      "AWS/ELB",
      "AWS/ApplicationELB",
      "AWS/NetworkELB",
      "AWS/S3",
      "AWS/OpenSearch",
      "CWAgent",
      "InfoDocs/Laravel" # Custom namespace for Laravel metrics
    ])
  })

  secure_json_data_encoded = jsonencode({
    accessKey = data.aws_ssm_parameter.grafana_aws_access_key.value
    secretKey = data.aws_ssm_parameter.grafana_aws_secret_key.value
  })

  lifecycle {
    ignore_changes = [
      secure_json_data_encoded
    ]
  }
}

# CloudWatch Logs Data Source
resource "grafana_data_source" "cloudwatch_logs" {
  type = "cloudwatch"
  name = "CloudWatch-Logs-${var.environment}"
  url  = "https://logs.${data.aws_region.current.name}.amazonaws.com"

  json_data_encoded = jsonencode({
    defaultRegion = data.aws_region.current.name
    authType      = "keys"
    logGroups     = var.cloudwatch_log_groups
  })

  secure_json_data_encoded = jsonencode({
    accessKey = data.aws_ssm_parameter.grafana_aws_access_key.value
    secretKey = data.aws_ssm_parameter.grafana_aws_secret_key.value
  })

  lifecycle {
    ignore_changes = [
      secure_json_data_encoded
    ]
  }
}

# OpenSearch Data Source for Log Analytics
resource "grafana_data_source" "opensearch_logs" {
  type = "grafana-opensearch-datasource"
  name = "OpenSearch-Logs-${var.environment}"
  url  = var.opensearch_endpoint

  json_data_encoded = jsonencode({
    timeField                  = "@timestamp"
    esVersion                  = "7.10.0"
    interval                   = "Daily"
    maxConcurrentShardRequests = 5
    includeFrozen              = false
    logMessageField            = "message"
    logLevelField              = "level"
    database                   = "logs-*"
  })
}

# OpenSearch Data Source for Security/Wazuh Data
resource "grafana_data_source" "opensearch_security" {
  type = "grafana-opensearch-datasource"
  name = "OpenSearch-Security-${var.environment}"
  url  = var.opensearch_endpoint

  json_data_encoded = jsonencode({
    timeField                  = "@timestamp"
    esVersion                  = "7.10.0"
    interval                   = "Daily"
    maxConcurrentShardRequests = 5
    includeFrozen              = false
    database                   = "wazuh-alerts-*"
    timeInterval               = "1m"
  })
}
