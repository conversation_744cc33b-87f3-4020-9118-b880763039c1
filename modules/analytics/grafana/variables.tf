variable "environment" {
  type        = string
  description = "Environment name (dev, prod, operations)"
  validation {
    condition     = contains(["dev", "prod", "operations"], var.environment)
    error_message = "Environment must be one of: dev, prod, operations."
  }
}

variable "opensearch_endpoint" {
  type        = string
  description = "OpenSearch endpoint URL"
}

variable "cloudwatch_log_groups" {
  type        = list(string)
  description = "CloudWatch log groups to monitor"
  default = [
    "/aws/lambda/infodocs-operations-opensearch-log-shipper",
    "/aws/lambda/infodocs-operations-wazuh-log-shipper",
    "/aws/ec2/bastion",
    "/aws/opensearch/infodocs-operations-os",
    "/infodocs/laravel/application",
    "/infodocs/laravel/audit"
  ]
}

variable "alert_evaluation_interval" {
  type        = string
  description = "Default interval for alert rule evaluation"
  default     = "1m"
}

variable "slack_webhooks" {
  type = object({
    critical       = string
    infrastructure = string
    application    = string
    security       = string
  })
  description = "Slack webhook URLs for different alert types"
}

variable "tags" {
  type        = map(string)
  description = "Tags to apply to all resources"
  default     = {}
}
