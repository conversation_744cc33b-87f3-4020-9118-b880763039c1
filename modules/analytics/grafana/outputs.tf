output "data_source_uids" {
  description = "UIDs of created data sources"
  value = {
    cloudwatch          = grafana_data_source.cloudwatch.uid
    cloudwatch_logs     = grafana_data_source.cloudwatch_logs.uid
    opensearch_logs     = grafana_data_source.opensearch_logs.uid
    opensearch_security = grafana_data_source.opensearch_security.uid
  }
}

output "dashboard_uids" {
  description = "UIDs of created dashboards"
  value = {
    aws_infrastructure  = grafana_dashboard.aws_infrastructure.uid
    laravel_application = grafana_dashboard.laravel_application.uid
    security_monitoring = grafana_dashboard.security_monitoring.uid
    lambda_functions    = grafana_dashboard.lambda_functions.uid
    cost_monitoring     = grafana_dashboard.cost_monitoring.uid
  }
}

output "folder_uids" {
  description = "UIDs of created folders"
  value = {
    aws_infrastructure    = grafana_folder.aws_infrastructure.uid
    laravel_applications  = grafana_folder.laravel_applications.uid
    security_monitoring   = grafana_folder.security_monitoring.uid
    lambda_functions      = grafana_folder.lambda_functions.uid
    infrastructure_alerts = grafana_folder.infrastructure.uid
    application_alerts    = grafana_folder.application.uid
    security_alerts       = grafana_folder.security.uid
  }
}

output "contact_point_names" {
  description = "Names of created contact points"
  value = {
    slack_critical       = grafana_contact_point.slack_critical.name
    slack_infrastructure = grafana_contact_point.slack_infrastructure.name
    slack_application    = grafana_contact_point.slack_application.name
    slack_security       = grafana_contact_point.slack_security.name
  }
}

output "notification_policy_id" {
  description = "ID of the notification policy"
  value       = grafana_notification_policy.default.id
}
