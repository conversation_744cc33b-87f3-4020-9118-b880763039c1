{{ range .Alerts }}
*📱 Application Alert - ${environment}*

*Alert:* {{ .Annotations.summary }}
*Description:* {{ .Annotations.description }}
*Severity:* {{ .Labels.severity }}
*Service:* {{ .Labels.service }}
*Environment:* {{ .Labels.environment }}
*Started:* {{ .StartsAt.Format "2006-01-02 15:04:05 MST" }}

*Status:* {{ .Status }}
{{ if .GeneratorURL }}*View in Grafana:* {{ .GeneratorURL }}{{ end }}

{{ if eq .Status "resolved" }}
✅ *RESOLVED* at {{ .EndsAt.Format "2006-01-02 15:04:05 MST" }}
{{ else }}
🔍 *Check application logs and performance metrics*
{{ end }}

---
{{ end }}
