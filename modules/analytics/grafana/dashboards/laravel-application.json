{"id": null, "title": "Laravel Application Monitoring - ${environment}", "tags": ["laravel", "application", "infodocs", "${environment}"], "timezone": "Africa/Johannesburg", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "Request Rate", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "targets": [{"datasource": {"uid": "${cloudwatch_datasource_uid}"}, "refId": "A", "namespace": "InfoDocs/Laravel", "metricName": "RequestCount", "statistic": "Sum", "dimensions": {"Environment": "${environment}"}}], "fieldConfig": {"defaults": {"unit": "reqps", "color": {"mode": "palette-classic"}}}}, {"id": 2, "title": "Response Time", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "targets": [{"datasource": {"uid": "${cloudwatch_datasource_uid}"}, "refId": "A", "namespace": "InfoDocs/Laravel", "metricName": "ResponseTime", "statistic": "Average", "dimensions": {"Environment": "${environment}"}}], "fieldConfig": {"defaults": {"unit": "ms", "color": {"mode": "palette-classic"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1000}, {"color": "red", "value": 2000}]}}}}, {"id": 3, "title": "Error Rate", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "targets": [{"datasource": {"uid": "${cloudwatch_datasource_uid}"}, "refId": "A", "namespace": "InfoDocs/Laravel", "metricName": "ErrorCount", "statistic": "Sum", "dimensions": {"Environment": "${environment}"}}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}}}}, {"id": 4, "title": "Database Query Time", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "targets": [{"datasource": {"uid": "${cloudwatch_datasource_uid}"}, "refId": "A", "namespace": "InfoDocs/Laravel", "metricName": "DatabaseQueryTime", "statistic": "Average", "dimensions": {"Environment": "${environment}"}}], "fieldConfig": {"defaults": {"unit": "ms", "color": {"mode": "palette-classic"}}}}, {"id": 5, "title": "Queue Jobs", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "targets": [{"datasource": {"uid": "${cloudwatch_datasource_uid}"}, "refId": "A", "namespace": "InfoDocs/Laravel", "metricName": "QueueJobsProcessed", "statistic": "Sum", "dimensions": {"Environment": "${environment}"}}, {"datasource": {"uid": "${cloudwatch_datasource_uid}"}, "refId": "B", "namespace": "InfoDocs/Laravel", "metricName": "QueueJobsFailed", "statistic": "Sum", "dimensions": {"Environment": "${environment}"}}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}}}}, {"id": 6, "title": "Memory Usage", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "targets": [{"datasource": {"uid": "${cloudwatch_datasource_uid}"}, "refId": "A", "namespace": "InfoDocs/Laravel", "metricName": "MemoryUsage", "statistic": "Average", "dimensions": {"Environment": "${environment}"}}], "fieldConfig": {"defaults": {"unit": "bytes", "color": {"mode": "palette-classic"}}}}, {"id": 7, "title": "Recent Application Logs", "type": "logs", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}, "targets": [{"datasource": {"uid": "${cloudwatch_logs_datasource_uid}"}, "refId": "A", "logGroups": ["/infodocs/laravel/application"], "region": "af-south-1"}], "options": {"showTime": true, "showLabels": false, "showCommonLabels": false, "wrapLogMessage": false, "prettifyLogMessage": false, "enableLogDetails": true, "dedupStrategy": "none", "sortOrder": "Descending"}}, {"id": 8, "title": "API Endpoints Performance", "type": "table", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 32}, "targets": [{"datasource": {"uid": "${opensearch_logs_datasource_uid}"}, "refId": "A", "query": "fields @timestamp, method, uri, response_time, status_code | filter @message like /api/ | stats avg(response_time) as avg_response_time, count() as request_count by uri | sort avg_response_time desc"}], "fieldConfig": {"defaults": {"custom": {"displayMode": "table"}}}}], "templating": {"list": [{"name": "environment", "type": "constant", "current": {"value": "${environment}", "text": "${environment}"}, "hide": 2}]}, "annotations": {"list": [{"name": "Deployments", "datasource": {"uid": "${cloudwatch_logs_datasource_uid}"}, "enable": true, "iconColor": "green", "query": "fields @timestamp, @message | filter @message like /deployment/ | sort @timestamp desc"}]}, "schemaVersion": 30, "version": 1}