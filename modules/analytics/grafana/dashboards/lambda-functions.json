{"id": null, "title": "Lambda Functions Monitoring - ${environment}", "tags": ["aws", "lambda", "serverless", "infodocs", "${environment}"], "timezone": "Africa/Johannesburg", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "Lambda Invocations", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "targets": [{"datasource": {"uid": "${cloudwatch_datasource_uid}"}, "refId": "A", "namespace": "AWS/Lambda", "metricName": "Invocations", "statistic": "Sum", "dimensions": {}}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}}}}, {"id": 2, "title": "Lambda Errors", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "targets": [{"datasource": {"uid": "${cloudwatch_datasource_uid}"}, "refId": "A", "namespace": "AWS/Lambda", "metricName": "Errors", "statistic": "Sum", "dimensions": {}}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}}}}, {"id": 3, "title": "Lambda Duration", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "targets": [{"datasource": {"uid": "${cloudwatch_datasource_uid}"}, "refId": "A", "namespace": "AWS/Lambda", "metricName": "Duration", "statistic": "Average", "dimensions": {}}], "fieldConfig": {"defaults": {"unit": "ms", "color": {"mode": "palette-classic"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10000}, {"color": "red", "value": 30000}]}}}}, {"id": 4, "title": "Lambda Throttles", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "targets": [{"datasource": {"uid": "${cloudwatch_datasource_uid}"}, "refId": "A", "namespace": "AWS/Lambda", "metricName": "<PERSON>hrottles", "statistic": "Sum", "dimensions": {}}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}}}}, {"id": 5, "title": "Lambda Function Performance by Function", "type": "table", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}, "targets": [{"datasource": {"uid": "${cloudwatch_datasource_uid}"}, "refId": "A", "namespace": "AWS/Lambda", "metricName": "Duration", "statistic": "Average", "dimensions": {"FunctionName": "*"}}], "fieldConfig": {"defaults": {"custom": {"displayMode": "table"}}}}, {"id": 6, "title": "Recent Lambda Logs", "type": "logs", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}, "targets": [{"datasource": {"uid": "${cloudwatch_logs_datasource_uid}"}, "refId": "A", "logGroups": ["/aws/lambda/infodocs-operations-opensearch-log-shipper", "/aws/lambda/infodocs-operations-wazuh-log-shipper"]}], "options": {"showTime": true, "showLabels": false, "showCommonLabels": false, "wrapLogMessage": false, "prettifyLogMessage": false, "enableLogDetails": true, "dedupStrategy": "none", "sortOrder": "Descending"}}], "templating": {"list": [{"name": "environment", "type": "constant", "current": {"value": "${environment}", "text": "${environment}"}, "hide": 2}]}, "schemaVersion": 30, "version": 1}