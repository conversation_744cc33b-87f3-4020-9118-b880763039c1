{"id": null, "title": "Security Monitoring - ${environment}", "tags": ["security", "wazuh", "compliance", "infodocs", "${environment}"], "timezone": "Africa/Johannesburg", "refresh": "1m", "time": {"from": "now-24h", "to": "now"}, "panels": [{"id": 1, "title": "Security Alerts by Severity", "type": "piechart", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "targets": [{"datasource": {"uid": "${opensearch_security_datasource_uid}"}, "refId": "A", "query": "rule.level:[1 TO 15]", "timeField": "@timestamp"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}}}}, {"id": 2, "title": "Failed Login Attempts", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "targets": [{"datasource": {"uid": "${opensearch_security_datasource_uid}"}, "refId": "A", "query": "rule.description:*failed*login*", "timeField": "@timestamp"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 5}, {"color": "red", "value": 10}]}}}}, {"id": 3, "title": "Top Security Rules Triggered", "type": "table", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "targets": [{"datasource": {"uid": "${opensearch_security_datasource_uid}"}, "refId": "A", "query": "*", "timeField": "@timestamp"}], "fieldConfig": {"defaults": {"custom": {"displayMode": "table"}}}}, {"id": 4, "title": "Security Events Timeline", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "targets": [{"datasource": {"uid": "${opensearch_security_datasource_uid}"}, "refId": "A", "query": "rule.level:[7 TO 15]", "timeField": "@timestamp"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}}}}, {"id": 5, "title": "Compliance Status", "type": "stat", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "targets": [{"datasource": {"uid": "${opensearch_security_datasource_uid}"}, "refId": "A", "query": "rule.pci_dss:* OR rule.gdpr:* OR rule.hipaa:*", "timeField": "@timestamp"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}}}}, {"id": 6, "title": "Recent Security Events", "type": "logs", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "targets": [{"datasource": {"uid": "${opensearch_security_datasource_uid}"}, "refId": "A", "query": "rule.level:[5 TO 15]", "timeField": "@timestamp"}], "options": {"showTime": true, "showLabels": false, "showCommonLabels": false, "wrapLogMessage": false, "prettifyLogMessage": false, "enableLogDetails": true, "dedupStrategy": "none", "sortOrder": "Descending"}}], "templating": {"list": [{"name": "environment", "type": "constant", "current": {"value": "${environment}", "text": "${environment}"}, "hide": 2}]}, "schemaVersion": 30, "version": 1}