{"id": null, "title": "AWS Cost Monitoring - ${environment}", "tags": ["aws", "cost", "billing", "infodocs", "${environment}"], "timezone": "Africa/Johannesburg", "refresh": "1h", "time": {"from": "now-30d", "to": "now"}, "panels": [{"id": 1, "title": "Daily AWS Costs", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "targets": [{"datasource": {"uid": "${cloudwatch_datasource_uid}"}, "refId": "A", "namespace": "AWS/Billing", "metricName": "EstimatedCharges", "statistic": "Maximum", "dimensions": {"Currency": "USD"}}], "fieldConfig": {"defaults": {"unit": "currencyUSD", "color": {"mode": "palette-classic"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 100}, {"color": "red", "value": 200}]}}}}, {"id": 2, "title": "Cost by Service", "type": "piechart", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "targets": [{"datasource": {"uid": "${cloudwatch_datasource_uid}"}, "refId": "A", "namespace": "AWS/Billing", "metricName": "EstimatedCharges", "statistic": "Maximum", "dimensions": {"ServiceName": "*", "Currency": "USD"}}], "fieldConfig": {"defaults": {"unit": "currencyUSD", "color": {"mode": "palette-classic"}}}}, {"id": 3, "title": "EC2 Instance Costs", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "targets": [{"datasource": {"uid": "${cloudwatch_datasource_uid}"}, "refId": "A", "namespace": "AWS/Billing", "metricName": "EstimatedCharges", "statistic": "Maximum", "dimensions": {"ServiceName": "AmazonEC2", "Currency": "USD"}}], "fieldConfig": {"defaults": {"unit": "currencyUSD", "color": {"mode": "palette-classic"}}}}, {"id": 4, "title": "Lambda Costs", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "targets": [{"datasource": {"uid": "${cloudwatch_datasource_uid}"}, "refId": "A", "namespace": "AWS/Billing", "metricName": "EstimatedCharges", "statistic": "Maximum", "dimensions": {"ServiceName": "AWSLambda", "Currency": "USD"}}], "fieldConfig": {"defaults": {"unit": "currencyUSD", "color": {"mode": "palette-classic"}}}}, {"id": 5, "title": "OpenSearch Costs", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "targets": [{"datasource": {"uid": "${cloudwatch_datasource_uid}"}, "refId": "A", "namespace": "AWS/Billing", "metricName": "EstimatedCharges", "statistic": "Maximum", "dimensions": {"ServiceName": "AmazonES", "Currency": "USD"}}], "fieldConfig": {"defaults": {"unit": "currencyUSD", "color": {"mode": "palette-classic"}}}}, {"id": 6, "title": "Monthly Cost Trend", "type": "stat", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "targets": [{"datasource": {"uid": "${cloudwatch_datasource_uid}"}, "refId": "A", "namespace": "AWS/Billing", "metricName": "EstimatedCharges", "statistic": "Maximum", "dimensions": {"Currency": "USD"}}], "fieldConfig": {"defaults": {"unit": "currencyUSD", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 100}, {"color": "red", "value": 200}]}}}}], "templating": {"list": [{"name": "environment", "type": "constant", "current": {"value": "${environment}", "text": "${environment}"}, "hide": 2}]}, "schemaVersion": 30, "version": 1}