{"id": null, "title": "AWS Infrastructure Overview - ${environment}", "tags": ["aws", "infrastructure", "infodocs", "${environment}"], "timezone": "Africa/Johannesburg", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "EC2 CPU Utilization", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "targets": [{"datasource": {"uid": "${cloudwatch_datasource_uid}"}, "refId": "A", "namespace": "AWS/EC2", "metricName": "CPUUtilization", "statistic": "Average", "dimensions": {}}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "max": 100, "color": {"mode": "palette-classic"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 70}, {"color": "red", "value": 85}]}}}}, {"id": 2, "title": "EC2 Memory Utilization", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "targets": [{"datasource": {"uid": "${cloudwatch_datasource_uid}"}, "refId": "A", "namespace": "CWAgent", "metricName": "mem_used_percent", "statistic": "Average", "dimensions": {}}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "max": 100, "color": {"mode": "palette-classic"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 75}, {"color": "red", "value": 90}]}}}}, {"id": 3, "title": "Disk Usage", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "targets": [{"datasource": {"uid": "${cloudwatch_datasource_uid}"}, "refId": "A", "namespace": "CWAgent", "metricName": "disk_used_percent", "statistic": "Average", "dimensions": {}}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "max": 100, "color": {"mode": "palette-classic"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 80}, {"color": "red", "value": 95}]}}}}, {"id": 4, "title": "Network Traffic", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "targets": [{"datasource": {"uid": "${cloudwatch_datasource_uid}"}, "refId": "A", "namespace": "AWS/EC2", "metricName": "NetworkIn", "statistic": "Average", "dimensions": {}}, {"datasource": {"uid": "${cloudwatch_datasource_uid}"}, "refId": "B", "namespace": "AWS/EC2", "metricName": "NetworkOut", "statistic": "Average", "dimensions": {}}], "fieldConfig": {"defaults": {"unit": "bytes", "color": {"mode": "palette-classic"}}}}, {"id": 5, "title": "OpenSearch Cluster Health", "type": "stat", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "targets": [{"datasource": {"uid": "${cloudwatch_datasource_uid}"}, "refId": "A", "namespace": "AWS/ES", "metricName": "ClusterStatus.green", "statistic": "Maximum", "dimensions": {}}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "green", "value": 1}]}}}}, {"id": 6, "title": "Lambda Function Invocations", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "targets": [{"datasource": {"uid": "${cloudwatch_datasource_uid}"}, "refId": "A", "namespace": "AWS/Lambda", "metricName": "Invocations", "statistic": "Sum", "dimensions": {}}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}}}}], "templating": {"list": [{"name": "environment", "type": "constant", "current": {"value": "${environment}", "text": "${environment}"}, "hide": 2}]}, "schemaVersion": 30, "version": 1}