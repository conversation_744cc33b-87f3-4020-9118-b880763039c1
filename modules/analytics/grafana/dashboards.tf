# Create folders for organizing dashboards
resource "grafana_folder" "aws_infrastructure" {
  title = "AWS Infrastructure"
}

resource "grafana_folder" "laravel_applications" {
  title = "Laravel Applications"
}

resource "grafana_folder" "security_monitoring" {
  title = "Security Monitoring"
}

resource "grafana_folder" "lambda_functions" {
  title = "Lambda Functions"
}

# AWS Infrastructure Overview Dashboard
resource "grafana_dashboard" "aws_infrastructure" {
  folder = grafana_folder.aws_infrastructure.id
  config_json = templatefile("${path.module}/dashboards/aws-infrastructure.json", {
    cloudwatch_datasource_uid = grafana_data_source.cloudwatch.uid
    environment               = var.environment
  })
}

# Laravel Application Dashboard
resource "grafana_dashboard" "laravel_application" {
  folder = grafana_folder.laravel_applications.id
  config_json = templatefile("${path.module}/dashboards/laravel-application.json", {
    cloudwatch_datasource_uid      = grafana_data_source.cloudwatch.uid
    cloudwatch_logs_datasource_uid = grafana_data_source.cloudwatch_logs.uid
    opensearch_logs_datasource_uid = grafana_data_source.opensearch_logs.uid
    environment                    = var.environment
  })
}

# Security Monitoring Dashboard
resource "grafana_dashboard" "security_monitoring" {
  folder = grafana_folder.security_monitoring.id
  config_json = templatefile("${path.module}/dashboards/security-monitoring.json", {
    opensearch_security_datasource_uid = grafana_data_source.opensearch_security.uid
    environment                        = var.environment
  })
}

# Lambda Functions Dashboard
resource "grafana_dashboard" "lambda_functions" {
  folder = grafana_folder.lambda_functions.id
  config_json = templatefile("${path.module}/dashboards/lambda-functions.json", {
    cloudwatch_datasource_uid      = grafana_data_source.cloudwatch.uid
    cloudwatch_logs_datasource_uid = grafana_data_source.cloudwatch_logs.uid
    environment                    = var.environment
  })
}

# Cost Monitoring Dashboard
resource "grafana_dashboard" "cost_monitoring" {
  folder = grafana_folder.aws_infrastructure.id
  config_json = templatefile("${path.module}/dashboards/cost-monitoring.json", {
    cloudwatch_datasource_uid = grafana_data_source.cloudwatch.uid
    environment               = var.environment
  })
}
