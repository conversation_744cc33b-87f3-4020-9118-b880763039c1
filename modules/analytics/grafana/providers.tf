terraform {
  required_providers {
    grafana = {
      source  = "grafana/grafana"
      version = "~> 3.0"
    }
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

# Get Grafana credentials from SSM
data "aws_ssm_parameter" "grafana_url" {
  name = "/infodocs/grafana/cloud_url"
}

data "aws_ssm_parameter" "grafana_auth" {
  name            = "/infodocs/grafana/service_account_token"
  with_decryption = true
}

# Configure Grafana provider
provider "grafana" {
  url  = data.aws_ssm_parameter.grafana_url.value
  auth = data.aws_ssm_parameter.grafana_auth.value

  # Increase timeout for large dashboard deployments
  http_headers = {
    "Content-Type" = "application/json"
  }
}

# Get current AWS region and account
data "aws_region" "current" {}
data "aws_caller_identity" "current" {}
