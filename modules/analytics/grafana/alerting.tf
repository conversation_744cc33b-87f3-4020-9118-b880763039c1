# Slack webhook URLs are now managed in variables for easy updates

# Contact Point: Critical Alerts
resource "grafana_contact_point" "slack_critical" {
  name = "slack-critical-${var.environment}"

  slack {
    url        = var.slack_webhooks.critical
    username   = "Grafana-Critical"
    icon_emoji = ":rotating_light:"
    title      = "🚨 CRITICAL: {{ .GroupLabels.alertname }}"
    text = templatefile("${path.module}/templates/slack-critical.tmpl", {
      environment = var.environment
    })
  }
}

# Contact Point: Infrastructure Alerts
resource "grafana_contact_point" "slack_infrastructure" {
  name = "slack-infrastructure-${var.environment}"

  slack {
    url        = var.slack_webhooks.infrastructure
    username   = "Grafana-Infrastructure"
    icon_emoji = ":warning:"
    title      = "⚠️ INFRASTRUCTURE: {{ .GroupLabels.alertname }}"
    text = templatefile("${path.module}/templates/slack-infrastructure.tmpl", {
      environment = var.environment
    })
  }
}

# Contact Point: Application Alerts
resource "grafana_contact_point" "slack_application" {
  name = "slack-application-${var.environment}"

  slack {
    url        = var.slack_webhooks.application
    username   = "Grafana-Application"
    icon_emoji = ":iphone:"
    title      = "📱 APPLICATION: {{ .GroupLabels.alertname }}"
    text = templatefile("${path.module}/templates/slack-application.tmpl", {
      environment = var.environment
    })
  }
}

# Contact Point: Security Alerts
resource "grafana_contact_point" "slack_security" {
  name = "slack-security-${var.environment}"

  slack {
    url        = var.slack_webhooks.security
    username   = "Grafana-Security"
    icon_emoji = ":lock:"
    title      = "🔒 SECURITY: {{ .GroupLabels.alertname }}"
    text = templatefile("${path.module}/templates/slack-security.tmpl", {
      environment = var.environment
    })
  }
}

# Notification Policy: Route alerts to appropriate channels
resource "grafana_notification_policy" "default" {
  group_by        = ["alertname", "cluster", "service"]
  group_wait      = "10s"
  group_interval  = "5m"
  repeat_interval = "12h"
  contact_point   = grafana_contact_point.slack_infrastructure.name

  policy {
    matcher {
      label = "severity"
      match = "="
      value = "critical"
    }
    contact_point   = grafana_contact_point.slack_critical.name
    group_wait      = "0s"
    group_interval  = "1m"
    repeat_interval = "5m"
  }

  policy {
    matcher {
      label = "alertname"
      match = "=~"
      value = ".*Security.*|.*Wazuh.*|.*Auth.*|.*Login.*"
    }
    contact_point   = grafana_contact_point.slack_security.name
    group_wait      = "5s"
    group_interval  = "2m"
    repeat_interval = "30m"
  }

  policy {
    matcher {
      label = "service"
      match = "=~"
      value = ".*laravel.*|.*app.*|.*api.*"
    }
    contact_point   = grafana_contact_point.slack_application.name
    group_wait      = "10s"
    group_interval  = "5m"
    repeat_interval = "1h"
  }

  policy {
    matcher {
      label = "alertname"
      match = "=~"
      value = ".*CPU.*|.*Memory.*|.*Disk.*|.*Network.*|.*Instance.*"
    }
    contact_point   = grafana_contact_point.slack_infrastructure.name
    group_wait      = "15s"
    group_interval  = "10m"
    repeat_interval = "2h"
  }
}

# Alert Rule: High CPU Usage
resource "grafana_rule_group" "infrastructure_alerts" {
  name             = "infrastructure-alerts-${var.environment}"
  folder_uid       = grafana_folder.infrastructure.uid
  interval_seconds = 60

  rule {
    name      = "HighCPUUsage"
    condition = "C"

    data {
      ref_id = "A"

      relative_time_range {
        from = 300
        to   = 0
      }

      datasource_uid = grafana_data_source.cloudwatch.uid
      model = jsonencode({
        namespace  = "AWS/EC2"
        metricName = "CPUUtilization"
        statistic  = "Average"
        dimensions = {}
      })
    }

    data {
      ref_id = "B"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model = jsonencode({
        type       = "math"
        expression = "A"
      })
    }

    data {
      ref_id = "C"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model = jsonencode({
        type       = "threshold"
        expression = "B > 80"
      })
    }

    no_data_state  = "NoData"
    exec_err_state = "Alerting"
    for            = "5m"

    annotations = {
      summary     = "High CPU usage detected on {{ $labels.instance }}"
      description = "CPU usage is above 80% for more than 5 minutes on instance {{ $labels.instance }}"
    }

    labels = {
      severity = "warning"
      service  = "infrastructure"
      team     = "devops"
    }
  }

  rule {
    name      = "HighMemoryUsage"
    condition = "C"

    data {
      ref_id = "A"

      relative_time_range {
        from = 300
        to   = 0
      }

      datasource_uid = grafana_data_source.cloudwatch.uid
      model = jsonencode({
        namespace  = "CWAgent"
        metricName = "mem_used_percent"
        statistic  = "Average"
        dimensions = {}
      })
    }

    data {
      ref_id = "B"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model = jsonencode({
        type       = "math"
        expression = "A"
      })
    }

    data {
      ref_id = "C"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model = jsonencode({
        type       = "threshold"
        expression = "B > 85"
      })
    }

    no_data_state  = "NoData"
    exec_err_state = "Alerting"
    for            = "5m"

    annotations = {
      summary     = "High memory usage detected on {{ $labels.instance }}"
      description = "Memory usage is above 85% for more than 5 minutes on instance {{ $labels.instance }}"
    }

    labels = {
      severity = "warning"
      service  = "infrastructure"
      team     = "devops"
    }
  }

  # Lambda Error Alert
  rule {
    name      = "LambdaErrors"
    condition = "C"

    data {
      ref_id = "A"

      relative_time_range {
        from = 300
        to   = 0
      }

      datasource_uid = grafana_data_source.cloudwatch.uid
      model = jsonencode({
        namespace  = "AWS/Lambda"
        metricName = "Errors"
        statistic  = "Sum"
        dimensions = {}
      })
    }

    data {
      ref_id = "B"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model = jsonencode({
        type       = "math"
        expression = "A"
      })
    }

    data {
      ref_id = "C"

      relative_time_range {
        from = 0
        to   = 0
      }

      datasource_uid = "__expr__"
      model = jsonencode({
        type       = "threshold"
        expression = "B > 5"
      })
    }

    no_data_state  = "NoData"
    exec_err_state = "Alerting"
    for            = "2m"

    annotations = {
      summary     = "Lambda function errors detected"
      description = "Lambda functions are experiencing more than 5 errors in the last 5 minutes"
    }

    labels = {
      severity = "critical"
      service  = "application"
      team     = "development"
    }
  }
}

# Create folders for organizing alerts
resource "grafana_folder" "infrastructure" {
  title = "Infrastructure Alerts"
}

resource "grafana_folder" "application" {
  title = "Application Alerts"
}

resource "grafana_folder" "security" {
  title = "Security Alerts"
}
