output "domain_id" {
  description = "ID of the OpenSearch domain"
  value       = aws_opensearch_domain.this.domain_id
}

output "domain_name" {
  description = "Name of the OpenSearch domain"
  value       = aws_opensearch_domain.this.domain_name
}

output "domain_endpoint" {
  description = "Domain-specific endpoint used to submit index, search, and data upload requests"
  value       = aws_opensearch_domain.this.endpoint
}

output "domain_arn" {
  description = "ARN of the OpenSearch domain"
  value       = aws_opensearch_domain.this.arn
}

output "security_group_id" {
  description = "The ID of the security group for OpenSearch"
  value       = aws_security_group.opensearch.id
}

output "opensearch_master_user" {
  description = "Master username for OpenSearch"
  value       = data.aws_ssm_parameter.opensearch_master_user.value
  sensitive   = true
}

output "opensearch_master_password" {
  description = "Master password for OpenSearch"
  value       = data.aws_ssm_parameter.opensearch_master_password.value
  sensitive   = true
}
