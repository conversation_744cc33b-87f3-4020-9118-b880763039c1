# OpenSearch Module

This module deploys an Amazon OpenSearch domain with secure configuration.

## Usage

```hcl
module "opensearch" {
  source = "../../../../modules//opensearch"

  domain_name    = "infodocs-logs"
  instance_type  = "t3.small.search"
  instance_count = 2

  ebs_volume_size = 20

  encrypt_at_rest = true

  master_user_ssm_param = "/infodocs/dev/opensearch/master_user"
  master_pass_ssm_param = "/infodocs/dev/opensearch/master_password"

  tags = {
    Environment = "dev"
    Project     = "infodocs"
    ManagedBy   = "opentofu"
  }
}
```
