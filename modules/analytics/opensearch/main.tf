# Data sources for access policy
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

data "aws_ssm_parameter" "opensearch_master_user" {
  name = "/infodocs/opensearch/master_user"
}

data "aws_ssm_parameter" "opensearch_master_password" {
  name = "/infodocs/opensearch/master_password"
}

resource "aws_opensearch_domain" "this" {
  domain_name    = var.domain_name
  engine_version = var.engine_version

  cluster_config {
    instance_type  = var.instance_type
    instance_count = var.instance_count
  }

  ebs_options {
    ebs_enabled = true
    volume_size = var.volume_size
    volume_type = "gp3"
  }

  encrypt_at_rest {
    enabled    = true
    kms_key_id = var.kms_key_arn
  }

  node_to_node_encryption {
    enabled = true
  }

  domain_endpoint_options {
    enforce_https       = true
    tls_security_policy = "Policy-Min-TLS-1-2-2019-07"
  }

  # Simple access policy for VPC access
  access_policies = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          AWS = "*"
        }
        Action   = "es:*"
        Resource = "arn:aws:es:${data.aws_region.current.id}:${data.aws_caller_identity.current.account_id}:domain/${var.domain_name}/*"
      }
    ]
  })

  advanced_security_options {
    enabled                        = false # Temporarily disabled for easier access
    internal_user_database_enabled = false
  }

  vpc_options {
    subnet_ids         = [var.subnet_ids[0]] # Single instance needs only one subnet
    security_group_ids = [aws_security_group.opensearch.id]
  }

  # Log publishing to CloudWatch for monitoring (only when enabled)
  dynamic "log_publishing_options" {
    for_each = var.enable_s3_log_publishing ? [1] : []
    content {
      log_type                 = "INDEX_SLOW_LOGS"
      enabled                  = true
      cloudwatch_log_group_arn = aws_cloudwatch_log_group.opensearch_s3_export[0].arn
    }
  }

  dynamic "log_publishing_options" {
    for_each = var.enable_s3_log_publishing ? [1] : []
    content {
      log_type                 = "SEARCH_SLOW_LOGS"
      enabled                  = true
      cloudwatch_log_group_arn = aws_cloudwatch_log_group.opensearch_s3_export[0].arn
    }
  }

  dynamic "log_publishing_options" {
    for_each = var.enable_s3_log_publishing ? [1] : []
    content {
      log_type                 = "ES_APPLICATION_LOGS"
      enabled                  = true
      cloudwatch_log_group_arn = aws_cloudwatch_log_group.opensearch_s3_export[0].arn
    }
  }

  tags = var.tags
}

# CloudWatch log group for S3 export
resource "aws_cloudwatch_log_group" "opensearch_s3_export" {
  count             = var.enable_s3_log_publishing ? 1 : 0
  name              = "/aws/opensearch/${var.name_prefix}-s3-export"
  retention_in_days = 90 # Short retention, logs go to S3
  # Note: KMS encryption removed for CloudWatch logs to avoid permission complexity
  # The OpenSearch domain itself is still encrypted with KMS

  tags = var.tags
}

# Note: Lambda function is now managed by the lambda-log-shipper module

# CloudWatch subscription filter to trigger log shipping
resource "aws_cloudwatch_log_subscription_filter" "opensearch_to_s3" {
  count           = var.enable_s3_log_publishing ? 1 : 0
  name            = "${var.name_prefix}-opensearch-s3-shipper"
  log_group_name  = aws_cloudwatch_log_group.opensearch_s3_export[0].name
  filter_pattern  = ""
  destination_arn = var.lambda_log_shipper_arn

  depends_on = [var.lambda_log_shipper_arn]
}

resource "aws_security_group" "opensearch" {
  name        = "${var.name_prefix}-opensearch-sg"
  description = "Security group for OpenSearch domain"
  vpc_id      = var.vpc_id

  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = var.allowed_cidr_blocks
    description = "HTTPS for OpenSearch API"
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
  }

  tags = merge(
    var.tags,
    {
      Name = "${var.name_prefix}-opensearch-sg"
    }
  )
}
