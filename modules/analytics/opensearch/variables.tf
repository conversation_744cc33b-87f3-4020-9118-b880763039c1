variable "domain_name" {
  type        = string
  description = "Name of the OpenSearch domain"
}

variable "engine_version" {
  type        = string
  description = "Version of OpenSearch to deploy"
  default     = "OpenSearch_2.5"
}

variable "instance_type" {
  type        = string
  description = "Instance type for OpenSearch nodes"
  default     = "t3.small.search"
}

variable "instance_count" {
  type        = number
  description = "Number of instances in the OpenSearch cluster"
  default     = 1
}

variable "volume_size" {
  type        = number
  description = "Size of EBS volume attached to each instance (in GB)"
  default     = 10
}

variable "kms_key_arn" {
  type        = string
  description = "ARN of KMS key for encrypting OpenSearch data"
}

variable "subnet_ids" {
  type        = list(string)
  description = "List of subnet IDs for the OpenSearch domain"
}

variable "vpc_id" {
  type        = string
  description = "ID of the VPC where OpenSearch will be deployed"
}

variable "allowed_cidr_blocks" {
  type        = list(string)
  description = "List of CIDR blocks allowed to access OpenSearch - INTERNAL ONLY"
  default     = [] # No default public access - must be explicitly set
  validation {
    condition     = !contains(var.allowed_cidr_blocks, "0.0.0.0/0")
    error_message = "Public access (0.0.0.0/0) is not allowed for security reasons. Use specific internal CIDR blocks only."
  }
}

variable "name_prefix" {
  type        = string
  description = "Prefix for resource names"
}

variable "environment" {
  type        = string
  description = "Environment name (dev, prod, operations)"
  validation {
    condition     = contains(["dev", "prod", "operations"], var.environment)
    error_message = "Environment must be one of: dev, prod, operations."
  }
}

variable "tags" {
  type        = map(string)
  description = "Tags to apply to OpenSearch resources"
  default     = {}
}

# S3 Log Archive Integration
variable "enable_s3_log_publishing" {
  type        = bool
  description = "Enable log publishing to S3 for long-term retention"
  default     = true
}

variable "s3_archive_bucket" {
  type        = string
  description = "S3 bucket name for log archival"
  default     = ""
}

variable "s3_legal_hold_bucket" {
  type        = string
  description = "S3 bucket name for legal hold logs"
  default     = ""
}

variable "lambda_log_shipper_arn" {
  type        = string
  description = "ARN of the centralized Lambda log shipper function"
  default     = ""
}

variable "log_retention_days" {
  type        = number
  description = "Days to retain logs in OpenSearch before archiving to S3"
  default     = 90
}
