# Kinesis Data Firehose for real-time log streaming to S3
resource "aws_kinesis_firehose_delivery_stream" "cloudwatch_logs" {
  name        = "${var.name_prefix}-cloudwatch-logs-firehose"
  destination = "extended_s3"

  extended_s3_configuration {
    role_arn            = aws_iam_role.firehose_delivery_role.arn
    bucket_arn          = "arn:aws:s3:::${var.s3_bucket_name}"
    prefix              = "cloudwatch-logs-firehose/year=!{timestamp:yyyy}/month=!{timestamp:MM}/day=!{timestamp:dd}/hour=!{timestamp:HH}/"
    error_output_prefix = "cloudwatch-logs-firehose-errors/"
    compression_format  = "GZIP"

    # CloudWatch Logs processing
    processing_configuration {
      enabled = true

      processors {
        type = "Lambda"

        parameters {
          parameter_name  = "LambdaArn"
          parameter_value = aws_lambda_function.firehose_processor.arn
        }
      }
    }

    cloudwatch_logging_options {
      enabled         = true
      log_group_name  = aws_cloudwatch_log_group.firehose_logs.name
      log_stream_name = "S3Delivery"
    }
  }

  tags = var.tags
}

# IAM role for Firehose
resource "aws_iam_role" "firehose_delivery_role" {
  name = "${var.name_prefix}-firehose-delivery-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "firehose.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

# IAM policy for Firehose
resource "aws_iam_role_policy" "firehose_delivery_policy" {
  name = "${var.name_prefix}-firehose-delivery-policy"
  role = aws_iam_role.firehose_delivery_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:AbortMultipartUpload",
          "s3:GetBucketLocation",
          "s3:GetObject",
          "s3:ListBucket",
          "s3:ListBucketMultipartUploads",
          "s3:PutObject"
        ]
        Resource = [
          "arn:aws:s3:::${var.s3_bucket_name}",
          "arn:aws:s3:::${var.s3_bucket_name}/*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "lambda:InvokeFunction",
          "lambda:GetFunctionConfiguration"
        ]
        Resource = aws_lambda_function.firehose_processor.arn
      },
      {
        Effect = "Allow"
        Action = [
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:*:*:*"
      },
      {
        Effect = "Allow"
        Action = [
          "kms:Decrypt",
          "kms:DescribeKey"
        ]
        Resource = var.kms_key_arn
        Condition = {
          StringEquals = {
            "kms:ViaService" = "s3.af-south-1.amazonaws.com"
          }
        }
      },
      {
        Effect = "Allow"
        Action = [
          "logs:PutLogEvents"
        ]
        Resource = "*"
      }
    ]
  })
}

# Data source for current region
data "aws_region" "current" {}

# CloudWatch Log Group for Firehose
resource "aws_cloudwatch_log_group" "firehose_logs" {
  name              = "/aws/kinesisfirehose/${var.name_prefix}-cloudwatch-logs"
  retention_in_days = var.log_retention_days
  kms_key_id        = var.kms_key_id

  tags = var.tags
}

# Lambda function for Firehose data processing
resource "aws_lambda_function" "firehose_processor" {
  filename      = data.archive_file.firehose_lambda_zip.output_path
  function_name = "${var.name_prefix}-firehose-processor"
  role          = aws_iam_role.firehose_lambda_role.arn
  handler       = "firehose_processor.lambda_handler"
  runtime       = "python3.9"
  timeout       = 60
  memory_size   = 256

  source_code_hash = data.archive_file.firehose_lambda_zip.output_base64sha256

  environment {
    variables = {
      LOG_LEVEL = var.log_level
    }
  }

  tags = var.tags
}

# Create Lambda deployment package for Firehose processor
data "archive_file" "firehose_lambda_zip" {
  type        = "zip"
  output_path = "${path.module}/firehose_processor.zip"
  source {
    content = file("${path.module}/src/firehose_processor.py")
    filename = "firehose_processor.py"
  }
}

# IAM role for Firehose Lambda processor
resource "aws_iam_role" "firehose_lambda_role" {
  name = "${var.name_prefix}-firehose-lambda-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

# IAM policy for Firehose Lambda processor
resource "aws_iam_role_policy" "firehose_lambda_policy" {
  name = "${var.name_prefix}-firehose-lambda-policy"
  role = aws_iam_role.firehose_lambda_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:*:*:*"
      }
    ]
  })
}

# Permission for Firehose to invoke Lambda
resource "aws_lambda_permission" "allow_firehose_invoke" {
  statement_id  = "AllowExecutionFromFirehose"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.firehose_processor.function_name
  principal     = "firehose.amazonaws.com"
  source_arn    = aws_kinesis_firehose_delivery_stream.cloudwatch_logs.arn
}

# CloudWatch Logs service role for writing to Firehose
resource "aws_iam_role" "cloudwatch_logs_role" {
  name = "${var.name_prefix}-cloudwatch-logs-firehose-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "logs.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

# Policy for CloudWatch Logs to write to Firehose
resource "aws_iam_role_policy" "cloudwatch_logs_firehose_policy" {
  name = "${var.name_prefix}-cloudwatch-logs-firehose-policy"
  role = aws_iam_role.cloudwatch_logs_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "firehose:PutRecord",
          "firehose:PutRecordBatch"
        ]
        Resource = aws_kinesis_firehose_delivery_stream.cloudwatch_logs.arn
      }
    ]
  })
}
