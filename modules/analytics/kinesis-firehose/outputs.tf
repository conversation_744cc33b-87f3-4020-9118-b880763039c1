output "firehose_delivery_stream_name" {
  description = "Name of the Kinesis Data Firehose delivery stream"
  value       = aws_kinesis_firehose_delivery_stream.cloudwatch_logs.name
}

output "firehose_delivery_stream_arn" {
  description = "ARN of the Kinesis Data Firehose delivery stream"
  value       = aws_kinesis_firehose_delivery_stream.cloudwatch_logs.arn
}

output "firehose_role_arn" {
  description = "ARN of the Firehose delivery role"
  value       = aws_iam_role.firehose_delivery_role.arn
}

output "firehose_processor_function_name" {
  description = "Name of the Firehose processor Lambda function"
  value       = aws_lambda_function.firehose_processor.function_name
}

output "firehose_processor_function_arn" {
  description = "ARN of the Firehose processor Lambda function"
  value       = aws_lambda_function.firehose_processor.arn
}

output "firehose_log_group_name" {
  description = "Name of the Firehose CloudWatch log group"
  value       = aws_cloudwatch_log_group.firehose_logs.name
}

output "cloudwatch_logs_role_arn" {
  description = "ARN of the CloudWatch Logs role for Firehose"
  value       = aws_iam_role.cloudwatch_logs_role.arn
}
