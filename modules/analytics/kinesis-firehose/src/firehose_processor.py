import json
import base64
import gzip
import logging
from typing import Dict, List, Any

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

def lambda_handler(event: Dict[str, Any], context) -> Dict[str, Any]:
    """
    Process CloudWatch Logs data from Kinesis Data Firehose
    Decompresses and formats log data for S3 storage
    """
    try:
        output = []
        
        for record in event['records']:
            # Decode the data
            compressed_payload = base64.b64decode(record['data'])
            
            # Decompress the gzipped data
            uncompressed_payload = gzip.decompress(compressed_payload)
            data = json.loads(uncompressed_payload)
            
            # Process CloudWatch Logs data
            if 'logEvents' in data:
                for log_event in data['logEvents']:
                    # Create structured log entry
                    processed_record = {
                        'timestamp': log_event['timestamp'],
                        'message': log_event['message'],
                        'logGroup': data.get('logGroup', ''),
                        'logStream': data.get('logStream', ''),
                        'source': 'cloudwatch-firehose',
                        'messageType': data.get('messageType', 'DATA_MESSAGE')
                    }
                    
                    # Convert back to JSON and encode
                    output_record = {
                        'recordId': record['recordId'],
                        'result': 'Ok',
                        'data': base64.b64encode(
                            (json.dumps(processed_record) + '\n').encode('utf-8')
                        ).decode('utf-8')
                    }
                    output.append(output_record)
            else:
                # If not CloudWatch Logs format, pass through as-is
                output_record = {
                    'recordId': record['recordId'],
                    'result': 'Ok',
                    'data': record['data']
                }
                output.append(output_record)
                
        logger.info(f"Successfully processed {len(output)} records")
        
        return {'records': output}
        
    except Exception as e:
        logger.error(f"Error processing Firehose records: {str(e)}")
        
        # Return failed records for retry
        output = []
        for record in event['records']:
            output_record = {
                'recordId': record['recordId'],
                'result': 'ProcessingFailed'
            }
            output.append(output_record)
            
        return {'records': output}
