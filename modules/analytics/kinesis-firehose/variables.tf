variable "name_prefix" {
  type        = string
  description = "Prefix for resource names"
}

variable "environment" {
  type        = string
  description = "Environment name"
}

variable "s3_bucket_name" {
  type        = string
  description = "S3 bucket name for log storage"
}

variable "kms_key_id" {
  type        = string
  description = "KMS key ID for encryption"
  default     = null
}

variable "kms_key_arn" {
  type        = string
  description = "KMS key ARN for encryption"
}

variable "buffer_size" {
  type        = number
  description = "Buffer size in MB for Firehose delivery"
  default     = 5
  validation {
    condition     = var.buffer_size >= 1 && var.buffer_size <= 128
    error_message = "Buffer size must be between 1 and 128 MB."
  }
}

variable "buffer_interval" {
  type        = number
  description = "Buffer interval in seconds for Firehose delivery"
  default     = 60
  validation {
    condition     = var.buffer_interval >= 60 && var.buffer_interval <= 900
    error_message = "Buffer interval must be between 60 and 900 seconds."
  }
}

variable "log_retention_days" {
  type        = number
  description = "Retention period for Firehose logs in days"
  default     = 30
}

variable "log_level" {
  type        = string
  description = "Log level for Lambda functions"
  default     = "INFO"
  validation {
    condition     = contains(["DEBUG", "INFO", "WARNING", "ERROR"], var.log_level)
    error_message = "Log level must be one of: DEBUG, INFO, WARNING, ERROR."
  }
}

variable "enable_monitoring" {
  type        = bool
  description = "Enable CloudWatch monitoring and alarms"
  default     = true
}

variable "alarm_sns_topics" {
  type        = list(string)
  description = "SNS topic ARNs for alarm notifications"
  default     = []
}

variable "tags" {
  type        = map(string)
  description = "Tags to apply to all Kinesis Firehose resources"
  default     = {}
}
