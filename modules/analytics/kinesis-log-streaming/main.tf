# Kinesis Data Firehose for Automatic Log Streaming
# This replaces manual exports with automatic daily streaming

terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

# Data sources
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

# Kinesis Data Firehose IAM Role
resource "aws_iam_role" "firehose_delivery_role" {
  name = "${var.name_prefix}-firehose-delivery-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "firehose.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

# Firehose delivery policy
resource "aws_iam_role_policy" "firehose_delivery_policy" {
  name = "${var.name_prefix}-firehose-delivery-policy"
  role = aws_iam_role.firehose_delivery_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:AbortMultipartUpload",
          "s3:GetBucketLocation",
          "s3:GetObject",
          "s3:ListBucket",
          "s3:ListBucketMultipartUploads",
          "s3:PutObject"
        ]
        Resource = [
          var.s3_bucket_arn,
          "${var.s3_bucket_arn}/*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "kms:Decrypt",
          "kms:GenerateDataKey"
        ]
        Resource = var.kms_key_arn
        Condition = {
          StringEquals = {
            "kms:ViaService" = "s3.${data.aws_region.current.name}.amazonaws.com"
          }
        }
      },
      {
        Effect = "Allow"
        Action = [
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:log-group:/aws/kinesisfirehose/${var.name_prefix}-*"
      }
    ]
  })
}

# CloudWatch Log Group for Firehose
resource "aws_cloudwatch_log_group" "firehose_log_group" {
  name              = "/aws/kinesisfirehose/${var.name_prefix}-log-streaming"
  retention_in_days = var.log_retention_days
  kms_key_id        = var.kms_key_arn

  tags = merge(var.tags, {
    Purpose = "firehose-log-streaming"
  })
}

# CloudWatch Log Stream for Firehose
resource "aws_cloudwatch_log_stream" "firehose_log_stream" {
  name           = "S3Delivery"
  log_group_name = aws_cloudwatch_log_group.firehose_log_group.name
}

# Kinesis Data Firehose Delivery Stream
resource "aws_kinesis_firehose_delivery_stream" "log_streaming" {
  name        = "${var.name_prefix}-log-streaming"
  destination = "s3"

  s3_configuration {
    role_arn           = aws_iam_role.firehose_delivery_role.arn
    bucket_arn         = var.s3_bucket_arn
    prefix             = "streaming-logs/year=!{timestamp:yyyy}/month=!{timestamp:MM}/day=!{timestamp:dd}/hour=!{timestamp:HH}/"
    error_output_prefix = "streaming-errors/"
    
    # Buffer settings for efficient batching
    buffer_size     = var.buffer_size_mb
    buffer_interval = var.buffer_interval_seconds
    
    # Compression for cost optimization
    compression_format = "GZIP"
    
    # KMS encryption
    kms_key_arn = var.kms_key_arn

    cloudwatch_logging_options {
      enabled         = true
      log_group_name  = aws_cloudwatch_log_group.firehose_log_group.name
      log_stream_name = aws_cloudwatch_log_stream.firehose_log_stream.name
    }
  }

  tags = merge(var.tags, {
    Purpose = "automated-log-streaming"
  })
}

# Lambda function to forward CloudWatch Logs to Firehose
resource "aws_lambda_function" "log_forwarder" {
  filename         = data.archive_file.log_forwarder_zip.output_path
  function_name    = "${var.name_prefix}-log-forwarder"
  role            = aws_iam_role.log_forwarder_role.arn
  handler         = "index.handler"
  source_code_hash = data.archive_file.log_forwarder_zip.output_base64sha256
  runtime         = "python3.9"
  timeout         = 300

  environment {
    variables = {
      FIREHOSE_DELIVERY_STREAM = aws_kinesis_firehose_delivery_stream.log_streaming.name
    }
  }

  tags = var.tags
}

# Lambda function code
data "archive_file" "log_forwarder_zip" {
  type        = "zip"
  output_path = "/tmp/log_forwarder.zip"
  source {
    content = templatefile("${path.module}/log_forwarder.py", {
      firehose_stream_name = "${var.name_prefix}-log-streaming"
    })
    filename = "index.py"
  }
}

# IAM role for Lambda log forwarder
resource "aws_iam_role" "log_forwarder_role" {
  name = "${var.name_prefix}-log-forwarder-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

# Lambda execution policy
resource "aws_iam_role_policy" "log_forwarder_policy" {
  name = "${var.name_prefix}-log-forwarder-policy"
  role = aws_iam_role.log_forwarder_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:*:*:*"
      },
      {
        Effect = "Allow"
        Action = [
          "firehose:PutRecord",
          "firehose:PutRecordBatch"
        ]
        Resource = aws_kinesis_firehose_delivery_stream.log_streaming.arn
      }
    ]
  })
}

# CloudWatch Log Group for Lambda
resource "aws_cloudwatch_log_group" "log_forwarder_logs" {
  name              = "/aws/lambda/${aws_lambda_function.log_forwarder.function_name}"
  retention_in_days = var.log_retention_days
  kms_key_id        = var.kms_key_arn

  tags = var.tags
}

# Lambda permission for CloudWatch Logs
resource "aws_lambda_permission" "allow_cloudwatch" {
  count         = length(var.source_log_groups)
  statement_id  = "AllowExecutionFromCloudWatchLogs-${count.index}"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.log_forwarder.function_name
  principal     = "logs.amazonaws.com"
  source_arn    = "arn:aws:logs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:log-group:${var.source_log_groups[count.index]}:*"
}

# Subscription filters for automatic log forwarding
resource "aws_cloudwatch_log_subscription_filter" "log_streaming" {
  count           = length(var.source_log_groups)
  name            = "${var.name_prefix}-streaming-${replace(var.source_log_groups[count.index], "/", "-")}"
  log_group_name  = var.source_log_groups[count.index]
  filter_pattern  = var.filter_pattern
  destination_arn = aws_lambda_function.log_forwarder.arn

  depends_on = [aws_lambda_permission.allow_cloudwatch]
}
