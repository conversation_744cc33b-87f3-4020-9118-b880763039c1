# selenium

<!-- BEGINNING OF PRE-COMMIT-TERRAFORM DOCS HOOK -->
## Requirements

No requirements.

## Providers

| Name | Version |
|------|---------|
| <a name="provider_helm"></a> [helm](#provider\_helm) | 3.0.1 |
| <a name="provider_kubernetes"></a> [kubernetes](#provider\_kubernetes) | 2.37.1 |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [helm_release.selenium_grid](https://registry.terraform.io/providers/hashicorp/helm/latest/docs/resources/release) | resource |
| [kubernetes_namespace.selenium](https://registry.terraform.io/providers/hashicorp/kubernetes/latest/docs/resources/namespace) | resource |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_chart_version"></a> [chart\_version](#input\_chart\_version) | Version of the Selenium Grid Helm chart | `string` | `"0.13.0"` | no |
| <a name="input_chrome_replicas"></a> [chrome\_replicas](#input\_chrome\_replicas) | Number of Chrome browser instances to run | `number` | `2` | no |
| <a name="input_firefox_enabled"></a> [firefox\_enabled](#input\_firefox\_enabled) | Whether to enable Firefox browser nodes | `bool` | `true` | no |
| <a name="input_firefox_replicas"></a> [firefox\_replicas](#input\_firefox\_replicas) | Number of Firefox browser instances to run | `number` | `1` | no |
| <a name="input_namespace"></a> [namespace](#input\_namespace) | Kubernetes namespace to deploy Selenium Grid | `string` | `"selenium"` | no |
| <a name="input_release_name"></a> [release\_name](#input\_release\_name) | Name of the Helm release | `string` | `"selenium-grid"` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_selenium_grid_endpoint"></a> [selenium\_grid\_endpoint](#output\_selenium\_grid\_endpoint) | The endpoint URL for the Selenium Grid |
<!-- END OF PRE-COMMIT-TERRAFORM DOCS HOOK -->
