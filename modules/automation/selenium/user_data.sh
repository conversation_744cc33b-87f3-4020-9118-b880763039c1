#!/bin/bash
# Cost-effective Selenium Grid setup using Docker on EC2
# No Kubernetes required - single instance with multiple containers

# Update system
yum update -y

# Install Docker
amazon-linux-extras install docker -y
systemctl start docker
systemctl enable docker
usermod -a -G docker ec2-user

# Install Docker Compose
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Create Selenium Grid Docker Compose configuration
cat > /home/<USER>/docker-compose.yml << 'EOF'
version: '3.8'
services:
  selenium-hub:
    image: selenium/hub:4.15.0
    container_name: selenium-hub
    ports:
      - "4444:4444"
    environment:
      - GRID_MAX_SESSION=16
      - GRID_BROWSER_TIMEOUT=300
      - GRID_TIMEOUT=300
    restart: unless-stopped

  chrome-node:
    image: selenium/node-chrome:4.15.0
    shm_size: 2gb
    depends_on:
      - selenium-hub
    environment:
      - HUB_HOST=selenium-hub
      - HUB_PORT=4444
      - NODE_MAX_INSTANCES=${chrome_replicas}
      - NODE_MAX_SESSION=${chrome_replicas}
    scale: ${chrome_replicas}
    restart: unless-stopped

  firefox-node:
    image: selenium/node-firefox:4.15.0
    shm_size: 2gb
    depends_on:
      - selenium-hub
    environment:
      - HUB_HOST=selenium-hub
      - HUB_PORT=4444
      - NODE_MAX_INSTANCES=${firefox_replicas}
      - NODE_MAX_SESSION=${firefox_replicas}
    scale: ${firefox_replicas}
    restart: unless-stopped
EOF

# Start Selenium Grid
cd /home/<USER>
docker-compose up -d

# Create systemd service for auto-start
cat > /etc/systemd/system/selenium-grid.service << 'EOF'
[Unit]
Description=Selenium Grid Docker Compose
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/home/<USER>
ExecStart=/usr/local/bin/docker-compose up -d
ExecStop=/usr/local/bin/docker-compose down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF

systemctl enable selenium-grid.service
systemctl start selenium-grid.service

# Install CloudWatch agent for monitoring
yum install -y amazon-cloudwatch-agent
