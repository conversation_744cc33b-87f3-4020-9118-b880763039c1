# Cost-effective EC2-based Selenium Grid using Docker
# No Kubernetes cluster required - runs on single EC2 instance

data "aws_ami" "amazon_linux" {
  most_recent = true
  owners      = ["amazon"]

  filter {
    name   = "name"
    values = ["amzn2-ami-hvm-*-x86_64-gp2"]
  }
}

resource "aws_instance" "selenium_hub" {
  ami                    = data.aws_ami.amazon_linux.id
  instance_type          = var.instance_type
  subnet_id              = var.subnet_id
  vpc_security_group_ids = [aws_security_group.selenium.id]
  key_name               = var.key_name

  user_data = base64encode(templatefile("${path.module}/user_data.sh", {
    chrome_replicas  = var.chrome_replicas
    firefox_replicas = var.firefox_replicas
  }))

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-selenium-hub"
    Type = "selenium-testing"
  })
}

resource "aws_security_group" "selenium" {
  name        = "${var.name_prefix}-selenium-sg"
  description = "Security group for Selenium Grid"
  vpc_id      = var.vpc_id

  # Selenium Hub port
  ingress {
    from_port   = 4444
    to_port     = 4444
    protocol    = "tcp"
    cidr_blocks = var.allowed_cidr_blocks
    description = "Selenium Hub"
  }

  # SSH access
  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = var.allowed_cidr_blocks
    description = "SSH access"
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
    description = "All outbound traffic"
  }

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-selenium-sg"
  })
}
