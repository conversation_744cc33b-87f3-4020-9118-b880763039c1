variable "name_prefix" {
  description = "Prefix for resource names"
  type        = string
}

variable "instance_type" {
  description = "EC2 instance type for Selenium hub"
  type        = string
  default     = "t3.medium"
}

variable "subnet_id" {
  description = "Subnet ID where Selenium instance will be deployed"
  type        = string
}

variable "vpc_id" {
  description = "VPC ID where Selenium will be deployed"
  type        = string
}

variable "key_name" {
  description = "EC2 Key Pair name for SSH access"
  type        = string
}

variable "allowed_cidr_blocks" {
  description = "CIDR blocks allowed to access Selenium Grid"
  type        = list(string)
  default     = []
}

variable "chrome_replicas" {
  description = "Number of Chrome node containers"
  type        = number
  default     = 2
}

variable "firefox_replicas" {
  description = "Number of Firefox node containers"
  type        = number
  default     = 1
}

variable "tags" {
  description = "Tags to apply to resources"
  type        = map(string)
  default     = {}
}
