# Data sources for Cloudflare and Google configuration
data "aws_ssm_parameter" "cloudflare_api_token" {
  name            = "/infodocs/cloudflare/api_token"
  with_decryption = true
}

data "aws_ssm_parameter" "cloudflare_account_id" {
  name = "/infodocs/cloudflare/account_id"
}

data "aws_ssm_parameter" "google_client_id" {
  name            = "/infodocs/google/oauth/client_id"
  with_decryption = true
}

data "aws_ssm_parameter" "google_client_secret" {
  name            = "/infodocs/google/oauth/client_secret"
  with_decryption = true
}

data "aws_ssm_parameter" "google_workspace_domain" {
  name = "/infodocs/google/workspace/domain"
}
