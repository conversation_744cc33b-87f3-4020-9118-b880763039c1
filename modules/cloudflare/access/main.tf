# Cloudflare Zero Trust Access Module
# Configures access policies and identity providers for secure application access

terraform {
  required_providers {
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "~> 4.0"
    }
  }
}

# Google Workspace Identity Provider
resource "cloudflare_access_identity_provider" "google_workspace" {
  account_id = var.cloudflare_account_id
  name       = "Google Workspace SSO"
  type       = "google-apps"
  
  config {
    client_id     = var.google_client_id
    client_secret = var.google_client_secret
    apps_domain   = var.google_workspace_domain
  }
}

# Access Application for OpenSearch
resource "cloudflare_access_application" "opensearch" {
  account_id                = var.cloudflare_account_id
  name                     = "OpenSearch Analytics Platform"
  domain                   = var.opensearch_hostname
  type                     = "self_hosted"
  session_duration         = "24h"
  auto_redirect_to_identity = true
  
  # CORS settings for OpenSearch
  cors_headers {
    allowed_methods = ["GET", "POST", "OPTIONS", "PUT", "DELETE"]
    allowed_origins = ["https://${var.opensearch_hostname}"]
    allowed_headers = ["*"]
    allow_credentials = true
    max_age = 86400
  }

  # Custom login page
  custom_deny_message = "Access denied. Please ensure you're connected to Cloudflare WARP and authenticated with your InfoDocs Google Workspace account."
  custom_deny_url     = "https://infodocs.co.za/access-denied"

  tags = [
    "Environment:${var.environment}",
    "Project:infodocs",
    "Service:opensearch",
    "ManagedBy:opentofu"
  ]
}

# Access Policy: Require WARP + Google Workspace
resource "cloudflare_access_policy" "opensearch_warp_policy" {
  account_id     = var.cloudflare_account_id
  application_id = cloudflare_access_application.opensearch.id
  name           = "OpenSearch WARP + Google Workspace Access"
  precedence     = 1
  decision       = "allow"

  # Require Google Workspace authentication
  include {
    login_method = [cloudflare_access_identity_provider.google_workspace.id]
  }

  # Require WARP connection
  require {
    device_posture = [cloudflare_access_device_posture_rule.warp_connected.id]
  }

  # Restrict to specific Google Workspace domain
  require {
    gsuite {
      identity_provider_id = cloudflare_access_identity_provider.google_workspace.id
      email_domain         = [var.google_workspace_domain]
    }
  }

  # Optional: Restrict to specific users/groups
  dynamic "require" {
    for_each = length(var.allowed_emails) > 0 ? [1] : []
    content {
      email = var.allowed_emails
    }
  }
}

# Device Posture Rule: WARP Connected
resource "cloudflare_access_device_posture_rule" "warp_connected" {
  account_id  = var.cloudflare_account_id
  name        = "WARP Connected"
  type        = "warp"
  description = "Require users to be connected to Cloudflare WARP"

  match {
    platform = "any"
  }

  input {
    connection_id = var.warp_connection_id
  }
}

# Access Policy: Block all other access
resource "cloudflare_access_policy" "opensearch_deny_all" {
  account_id     = var.cloudflare_account_id
  application_id = cloudflare_access_application.opensearch.id
  name           = "OpenSearch Deny All Others"
  precedence     = 2
  decision       = "deny"

  include {
    everyone = true
  }
}

# Store Access Application details in SSM
resource "aws_ssm_parameter" "access_app_aud" {
  name        = "/infodocs/cloudflare/access/opensearch/aud"
  description = "Cloudflare Access Application AUD for OpenSearch"
  type        = "String"
  value       = cloudflare_access_application.opensearch.aud

  tags = var.tags
}

resource "aws_ssm_parameter" "access_app_domain" {
  name        = "/infodocs/cloudflare/access/opensearch/domain"
  description = "Cloudflare Access Application domain for OpenSearch"
  type        = "String"
  value       = cloudflare_access_application.opensearch.domain

  tags = var.tags
}
