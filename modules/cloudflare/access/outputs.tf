output "access_application_id" {
  description = "Cloudflare Access Application ID"
  value       = cloudflare_access_application.opensearch.id
}

output "access_application_aud" {
  description = "Cloudflare Access Application AUD"
  value       = cloudflare_access_application.opensearch.aud
}

output "access_application_domain" {
  description = "Cloudflare Access Application domain"
  value       = cloudflare_access_application.opensearch.domain
}

output "identity_provider_id" {
  description = "Google Workspace Identity Provider ID"
  value       = cloudflare_access_identity_provider.google_workspace.id
}

output "warp_device_posture_rule_id" {
  description = "WARP device posture rule ID"
  value       = cloudflare_access_device_posture_rule.warp_connected.id
}

output "opensearch_access_url" {
  description = "Secure URL for OpenSearch access"
  value       = "https://${var.opensearch_hostname}"
}

output "access_instructions" {
  description = "Instructions for accessing OpenSearch"
  value = {
    step_1 = "Connect to Cloudflare WARP"
    step_2 = "Navigate to https://${var.opensearch_hostname}"
    step_3 = "Authenticate with your ${var.google_workspace_domain} Google Workspace account"
    step_4 = "Access OpenSearch Dashboards"
  }
}
