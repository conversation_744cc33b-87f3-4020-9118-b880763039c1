variable "cloudflare_account_id" {
  type        = string
  description = "Cloudflare Account ID"
}

variable "opensearch_hostname" {
  type        = string
  description = "Hostname for OpenSearch access (e.g., opensearch.infodocs.co.za)"
}

variable "google_client_id" {
  type        = string
  description = "Google OAuth Client ID for Workspace SSO"
  sensitive   = true
}

variable "google_client_secret" {
  type        = string
  description = "Google OAuth Client Secret for Workspace SSO"
  sensitive   = true
}

variable "google_workspace_domain" {
  type        = string
  description = "Google Workspace domain (e.g., infodocs.co.za)"
}

variable "warp_connection_id" {
  type        = string
  description = "Cloudflare WARP connection ID for device posture"
  default     = ""
}

variable "allowed_emails" {
  type        = list(string)
  description = "List of specific email addresses allowed access (optional)"
  default     = []
}

variable "environment" {
  type        = string
  description = "Environment name (dev, prod, operations)"
  validation {
    condition     = contains(["dev", "prod", "operations"], var.environment)
    error_message = "Environment must be one of: dev, prod, operations."
  }
}

variable "tags" {
  type        = map(string)
  description = "Tags to apply to AWS resources"
  default     = {}
}
