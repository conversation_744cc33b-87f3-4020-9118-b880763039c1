# Cloudflare Tunnel Module
# Creates a secure tunnel from your infrastructure to Cloudflare's edge

terraform {
  required_providers {
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "~> 4.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.1"
    }
  }
}

# Generate a random tunnel secret
resource "random_id" "tunnel_secret" {
  byte_length = 32
}

# Create the Cloudflare Tunnel
resource "cloudflare_tunnel" "opensearch_tunnel" {
  account_id = var.cloudflare_account_id
  name       = "${var.name_prefix}-opensearch-tunnel"
  secret     = random_id.tunnel_secret.b64_std
}

# Configure tunnel routing
resource "cloudflare_tunnel_config" "opensearch_config" {
  account_id = var.cloudflare_account_id
  tunnel_id  = cloudflare_tunnel.opensearch_tunnel.id

  config {
    # Ingress rules for OpenSearch
    ingress_rule {
      hostname = var.opensearch_hostname
      service  = "https://${var.opensearch_internal_endpoint}"
      
      # Origin request configuration for OpenSearch
      origin_request {
        connect_timeout          = "30s"
        tls_timeout             = "10s"
        tcp_keep_alive          = "30s"
        no_happy_eyeballs       = false
        keep_alive_connections  = 1024
        keep_alive_timeout      = "1m30s"
        http_host_header        = var.opensearch_hostname
        origin_server_name      = var.opensearch_hostname
        
        # Disable certificate verification for internal services
        no_tls_verify = true
        
        # Access configuration
        access {
          required  = true
          team_name = var.cloudflare_team_name
          aud_tag   = [var.access_application_aud]
        }
      }
    }

    # Catch-all rule (required)
    ingress_rule {
      service = "http_status:404"
    }
  }
}

# DNS record for the tunnel
resource "cloudflare_record" "opensearch_tunnel_dns" {
  zone_id = var.cloudflare_zone_id
  name    = var.opensearch_hostname
  content = cloudflare_tunnel.opensearch_tunnel.cname
  type    = "CNAME"
  ttl     = 1
  proxied = true

  comment = "Managed by OpenTofu - Cloudflare Tunnel for OpenSearch"
}

# Store tunnel credentials in SSM for bastion host
resource "aws_ssm_parameter" "tunnel_id" {
  name        = "/infodocs/cloudflare/tunnel/id"
  description = "Cloudflare Tunnel ID for OpenSearch access"
  type        = "String"
  value       = cloudflare_tunnel.opensearch_tunnel.id

  tags = var.tags
}

resource "aws_ssm_parameter" "tunnel_token" {
  name        = "/infodocs/cloudflare/tunnel/token"
  description = "Cloudflare Tunnel token for bastion host authentication"
  type        = "SecureString"
  value       = cloudflare_tunnel.opensearch_tunnel.tunnel_token

  tags = var.tags
}

resource "aws_ssm_parameter" "tunnel_secret" {
  name        = "/infodocs/cloudflare/tunnel/secret"
  description = "Cloudflare Tunnel secret"
  type        = "SecureString"
  value       = random_id.tunnel_secret.b64_std

  tags = var.tags
}

# Store tunnel configuration for bastion host
resource "aws_ssm_parameter" "tunnel_config" {
  name        = "/infodocs/cloudflare/tunnel/config"
  description = "Cloudflare Tunnel configuration for cloudflared"
  type        = "String"
  value = jsonencode({
    tunnel      = cloudflare_tunnel.opensearch_tunnel.id
    credentials-file = "/etc/cloudflared/credentials.json"
    
    ingress = [
      {
        hostname = var.opensearch_hostname
        service  = "https://${var.opensearch_internal_endpoint}"
        originRequest = {
          noTLSVerify = true
          connectTimeout = "30s"
          tlsTimeout = "10s"
          httpHostHeader = var.opensearch_hostname
        }
      },
      {
        service = "http_status:404"
      }
    ]
  })

  tags = var.tags
}
