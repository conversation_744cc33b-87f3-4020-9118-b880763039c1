variable "cloudflare_account_id" {
  type        = string
  description = "Cloudflare Account ID"
}

variable "cloudflare_zone_id" {
  type        = string
  description = "Cloudflare Zone ID for the domain"
}

variable "cloudflare_team_name" {
  type        = string
  description = "Cloudflare Zero Trust team name"
}

variable "name_prefix" {
  type        = string
  description = "Prefix for resource names"
}

variable "opensearch_hostname" {
  type        = string
  description = "Hostname for OpenSearch access (e.g., opensearch.infodocs.co.za)"
}

variable "opensearch_internal_endpoint" {
  type        = string
  description = "Internal OpenSearch endpoint (VPC endpoint)"
}

variable "access_application_aud" {
  type        = string
  description = "Cloudflare Access Application AUD tag"
}

variable "tags" {
  type        = map(string)
  description = "Tags to apply to AWS resources"
  default     = {}
}

variable "environment" {
  type        = string
  description = "Environment name (dev, prod, operations)"
  validation {
    condition     = contains(["dev", "prod", "operations"], var.environment)
    error_message = "Environment must be one of: dev, prod, operations."
  }
}
