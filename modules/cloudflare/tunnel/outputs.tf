output "tunnel_id" {
  description = "Cloudflare Tunnel ID"
  value       = cloudflare_tunnel.opensearch_tunnel.id
}

output "tunnel_cname" {
  description = "Cloudflare Tunnel CNAME"
  value       = cloudflare_tunnel.opensearch_tunnel.cname
}

output "tunnel_token" {
  description = "Cloudflare Tunnel token for authentication"
  value       = cloudflare_tunnel.opensearch_tunnel.tunnel_token
  sensitive   = true
}

output "dns_record_id" {
  description = "Cloudflare DNS record ID for the tunnel"
  value       = cloudflare_record.opensearch_tunnel_dns.id
}

output "opensearch_url" {
  description = "Public URL for OpenSearch access"
  value       = "https://${var.opensearch_hostname}"
}

output "tunnel_config_ssm_path" {
  description = "SSM parameter path for tunnel configuration"
  value       = aws_ssm_parameter.tunnel_config.name
}

output "tunnel_token_ssm_path" {
  description = "SSM parameter path for tunnel token"
  value       = aws_ssm_parameter.tunnel_token.name
}
