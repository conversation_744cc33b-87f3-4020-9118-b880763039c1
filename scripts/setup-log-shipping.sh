#!/bin/bash

# Setup CloudWatch Log Shipping to OpenSearch
# This script creates subscription filters to ship logs to OpenSearch

set -e

echo "🚀 Setting up CloudWatch Log Shipping to OpenSearch"
echo "=================================================="

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Get Lambda function ARN
LAMBDA_ARN=$(aws lambda get-function --function-name infodocs-operations-opensearch-log-shipper --query 'Configuration.FunctionArn' --output text)

if [[ -z "$LAMBDA_ARN" ]]; then
    echo "❌ Lambda function not found. Please deploy it first."
    exit 1
fi

echo -e "${GREEN}✓ Found Lambda function: $LAMBDA_ARN${NC}"

# High-value log groups to start with (based on your data)
LOG_GROUPS=(
    "/aws/lambda/BizPortalScraperV1"
    "/aws/lambda/Regbot2" 
    "/aws/lambda/ValidationLambda"
    "/aws/lambda/beneficialOwnershipV1"
    "/aws/lambda/annualReturnV1"
    "/aws/lambda/directorChangeV1"
    "/aws/lambda/Pdf_Generator"
    "/aws/ec2/infodocs-operations-bastion"
    "/aws/lambda-insights"
)

# Function to create subscription filter
create_subscription_filter() {
    local log_group="$1"
    local filter_name="opensearch-shipper-$(echo $log_group | sed 's/[^a-zA-Z0-9]/-/g')"
    
    echo -e "${BLUE}Setting up shipping for: $log_group${NC}"
    
    # Check if log group exists
    if ! aws logs describe-log-groups --log-group-name-prefix "$log_group" --query 'logGroups[0].logGroupName' --output text | grep -q "$log_group"; then
        echo -e "${YELLOW}⚠️  Log group $log_group not found, skipping${NC}"
        return
    fi
    
    # Create subscription filter
    if aws logs put-subscription-filter \
        --log-group-name "$log_group" \
        --filter-name "$filter_name" \
        --filter-pattern "" \
        --destination-arn "$LAMBDA_ARN" 2>/dev/null; then
        echo -e "${GREEN}✓ Created subscription filter for $log_group${NC}"
    else
        echo -e "${YELLOW}⚠️  Failed to create filter for $log_group (might already exist)${NC}"
    fi
}

# Grant CloudWatch Logs permission to invoke Lambda
echo -e "${BLUE}Granting CloudWatch Logs permission to invoke Lambda...${NC}"
aws lambda add-permission \
    --function-name "infodocs-operations-opensearch-log-shipper" \
    --statement-id "cloudwatch-logs-permission" \
    --action "lambda:InvokeFunction" \
    --principal "logs.af-south-1.amazonaws.com" 2>/dev/null || echo "Permission already exists"

# Create subscription filters for high-value log groups
echo -e "\n${BLUE}Creating subscription filters for high-value log groups...${NC}"
for log_group in "${LOG_GROUPS[@]}"; do
    create_subscription_filter "$log_group"
done

echo -e "\n${GREEN}🎉 Log shipping setup complete!${NC}"
echo "============================================"
echo -e "${YELLOW}What happens next:${NC}"
echo "1. New logs will be shipped to OpenSearch automatically"
echo "2. Check OpenSearch in 5-10 minutes for new indices"
echo "3. Look for indices like: logs-YYYY.MM.DD"
echo ""
echo -e "${BLUE}To check if it's working:${NC}"
echo "1. Go to OpenSearch: https://localhost:8443/_dashboards"
echo "2. Navigate to 'Stack Management' → 'Index Management'"
echo "3. Look for new indices appearing"
echo ""
echo -e "${BLUE}To see live logs being processed:${NC}"
echo "aws logs tail /aws/lambda/infodocs-operations-opensearch-log-shipper --follow"
