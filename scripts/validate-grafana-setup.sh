#!/bin/bash

# Grafana Setup Validation Script
# Validates that all components are properly configured before deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 Grafana Setup Validation${NC}"
echo "Checking all components before deployment..."
echo ""

# Track validation results
VALIDATION_PASSED=true

# Function to check validation
check_validation() {
    local test_name="$1"
    local test_result="$2"
    
    if [ "$test_result" = "true" ]; then
        echo -e "${GREEN}✅ $test_name${NC}"
    else
        echo -e "${RED}❌ $test_name${NC}"
        VALIDATION_PASSED=false
    fi
}

echo -e "${BLUE}📋 Checking Required SSM Parameters${NC}"

# Check Grafana Cloud URL
if aws ssm get-parameter --name "/infodocs/grafana/cloud_url" --query 'Parameter.Value' --output text &>/dev/null; then
    GRAFANA_URL=$(aws ssm get-parameter --name "/infodocs/grafana/cloud_url" --query 'Parameter.Value' --output text)
    if [[ "$GRAFANA_URL" == "https://infodocs.grafana.net" ]]; then
        check_validation "Grafana Cloud URL" "true"
    else
        echo -e "${YELLOW}⚠️  Grafana URL: $GRAFANA_URL (verify this is correct)${NC}"
        check_validation "Grafana Cloud URL" "true"
    fi
else
    check_validation "Grafana Cloud URL" "false"
fi

# Check Grafana Service Account Token
if aws ssm get-parameter --name "/infodocs/grafana/service_account_token" --with-decryption --query 'Parameter.Value' --output text &>/dev/null; then
    TOKEN=$(aws ssm get-parameter --name "/infodocs/grafana/service_account_token" --with-decryption --query 'Parameter.Value' --output text)
    if [[ "$TOKEN" == glsa_* ]]; then
        check_validation "Grafana Service Account Token" "true"
    else
        check_validation "Grafana Service Account Token (invalid format)" "false"
    fi
else
    check_validation "Grafana Service Account Token" "false"
fi

# Check AWS Access Key
if aws ssm get-parameter --name "/infodocs/grafana/aws_access_key_id" --with-decryption --query 'Parameter.Value' --output text &>/dev/null; then
    ACCESS_KEY=$(aws ssm get-parameter --name "/infodocs/grafana/aws_access_key_id" --with-decryption --query 'Parameter.Value' --output text)
    if [[ "$ACCESS_KEY" == AKIA* ]]; then
        check_validation "AWS Access Key ID" "true"
    else
        check_validation "AWS Access Key ID (invalid format)" "false"
    fi
else
    check_validation "AWS Access Key ID" "false"
fi

# Check AWS Secret Key
if aws ssm get-parameter --name "/infodocs/grafana/aws_secret_access_key" --with-decryption --query 'Parameter.Value' --output text &>/dev/null; then
    check_validation "AWS Secret Access Key" "true"
else
    check_validation "AWS Secret Access Key" "false"
fi

echo ""
echo -e "${BLUE}📁 Checking File Structure${NC}"

# Check module files
if [ -f "modules/analytics/grafana/providers.tf" ]; then
    check_validation "Grafana module providers.tf" "true"
else
    check_validation "Grafana module providers.tf" "false"
fi

if [ -f "modules/analytics/grafana/data-sources.tf" ]; then
    check_validation "Grafana module data-sources.tf" "true"
else
    check_validation "Grafana module data-sources.tf" "false"
fi

if [ -f "modules/analytics/grafana/dashboards.tf" ]; then
    check_validation "Grafana module dashboards.tf" "true"
else
    check_validation "Grafana module dashboards.tf" "false"
fi

if [ -f "modules/analytics/grafana/alerting.tf" ]; then
    check_validation "Grafana module alerting.tf" "true"
else
    check_validation "Grafana module alerting.tf" "false"
fi

# Check deployment configuration
if [ -f "environments/operations/af-south-1/analytics/grafana/terragrunt.hcl" ]; then
    check_validation "Deployment terragrunt.hcl" "true"
else
    check_validation "Deployment terragrunt.hcl" "false"
fi

# Check dashboard JSON files
DASHBOARD_FILES=(
    "modules/analytics/grafana/dashboards/aws-infrastructure.json"
    "modules/analytics/grafana/dashboards/laravel-application.json"
    "modules/analytics/grafana/dashboards/security-monitoring.json"
    "modules/analytics/grafana/dashboards/lambda-functions.json"
    "modules/analytics/grafana/dashboards/cost-monitoring.json"
)

for dashboard in "${DASHBOARD_FILES[@]}"; do
    if [ -f "$dashboard" ]; then
        check_validation "Dashboard $(basename $dashboard)" "true"
    else
        check_validation "Dashboard $(basename $dashboard)" "false"
    fi
done

echo ""
echo -e "${BLUE}🔧 Checking Slack Webhook Configuration${NC}"

# Check Slack webhooks in terragrunt.hcl
if grep -q "slack_webhooks" environments/operations/af-south-1/analytics/grafana/terragrunt.hcl; then
    check_validation "Slack webhooks configured" "true"
    
    # Check if webhooks are still placeholders
    if grep -q "T00000000/B00000000" environments/operations/af-south-1/analytics/grafana/terragrunt.hcl; then
        echo -e "${YELLOW}⚠️  Slack webhooks are still placeholders - update with real URLs${NC}"
    else
        echo -e "${GREEN}✅ Slack webhooks appear to be configured with real URLs${NC}"
    fi
else
    check_validation "Slack webhooks configured" "false"
fi

echo ""
echo -e "${BLUE}🌐 Checking AWS Connectivity${NC}"

# Test AWS CLI access
if aws sts get-caller-identity &>/dev/null; then
    check_validation "AWS CLI connectivity" "true"
    
    # Check if using infrastructure-as-code-kms user
    CURRENT_USER=$(aws sts get-caller-identity --query 'Arn' --output text)
    if [[ "$CURRENT_USER" == *"infrastructure-as-code-kms"* ]]; then
        check_validation "Using infrastructure-as-code-kms user" "true"
    else
        echo -e "${YELLOW}⚠️  Current user: $CURRENT_USER${NC}"
        echo -e "${YELLOW}⚠️  Consider using infrastructure-as-code-kms user for consistency${NC}"
    fi
else
    check_validation "AWS CLI connectivity" "false"
fi

# Check OpenSearch dependency
if [ -f "environments/operations/af-south-1/analytics/opensearch/terragrunt.hcl" ]; then
    check_validation "OpenSearch dependency exists" "true"
else
    check_validation "OpenSearch dependency exists" "false"
fi

echo ""
echo -e "${BLUE}📊 Validation Summary${NC}"

if [ "$VALIDATION_PASSED" = "true" ]; then
    echo -e "${GREEN}🎉 All validations passed! Ready to deploy.${NC}"
    echo ""
    echo -e "${BLUE}🚀 Next Steps:${NC}"
    echo "1. Update Slack webhook URLs in terragrunt.hcl (if not done)"
    echo "2. Run deployment:"
    echo "   cd environments/operations/af-south-1/analytics/grafana"
    echo "   terragrunt plan"
    echo "   terragrunt apply"
    echo ""
    echo -e "${GREEN}✅ Your Grafana Infrastructure as Code setup is ready!${NC}"
else
    echo -e "${RED}❌ Some validations failed. Please fix the issues above before deploying.${NC}"
    echo ""
    echo -e "${BLUE}🔧 Common Fixes:${NC}"
    echo "1. Run SSM parameter setup if missing parameters"
    echo "2. Check file paths and ensure all files exist"
    echo "3. Verify AWS credentials and permissions"
    echo "4. Update Slack webhook URLs"
    exit 1
fi
