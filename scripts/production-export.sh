#!/bin/bash

# Production Export - Export ALL existing log data with proper date ranges

set -e

echo "🚀 Production Log Export - All Historical Data"
echo "=============================================="

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

# Configuration
S3_BUCKET="infodocs-operations-opensearch"

# High-value log groups with their sizes
declare -A LOG_GROUPS=(
    ["/aws/lambda/BizPortalScraperV1"]="1.8GB"
    ["/aws/lambda/Regbot2"]="1.1GB"
    ["/aws/lambda/ValidationLambda"]="1.1GB"
    ["/aws/lambda/beneficialOwnershipV1"]="595MB"
    ["/aws/lambda/annualReturnV1"]="170MB"
    ["/aws/lambda/directorChangeV1"]="171MB"
    ["/aws/lambda/Pdf_Generator"]="37MB"
    ["/aws/lambda/addressChangeV1"]="36MB"
    ["/aws/lambda/checkListV1"]="33MB"
    ["/aws/lambda/documentRequestV1"]="28MB"
)

echo -e "${BLUE}Exporting ${#LOG_GROUPS[@]} log groups with ~4GB+ total data${NC}"
echo ""

# Function to get actual date range for a log group
get_log_date_range() {
    local log_group="$1"
    
    echo -e "${BLUE}Checking date range for: $log_group${NC}"
    
    # Get first and last event times
    local date_info=$(aws logs describe-log-streams \
        --log-group-name "$log_group" \
        --order-by LastEventTime \
        --descending \
        --max-items 1 \
        --query 'logStreams[0].{First:firstEventTime,Last:lastEventTime}' \
        --output json 2>/dev/null)
    
    if [[ -n "$date_info" && "$date_info" != "null" ]]; then
        local first_event=$(echo "$date_info" | jq -r '.First // empty')
        local last_event=$(echo "$date_info" | jq -r '.Last // empty')
        
        if [[ -n "$first_event" && "$first_event" != "null" && "$first_event" != "empty" ]]; then
            echo "  First event: $(date -r $((first_event / 1000)))"
            echo "  Last event: $(date -r $((last_event / 1000)))"
            echo "$first_event $last_event"
            return 0
        fi
    fi
    
    echo "  No date range found"
    return 1
}

# Function to create export task with proper date range
export_log_group() {
    local log_group="$1"
    local size="$2"
    local log_group_clean=$(echo "$log_group" | sed 's|/aws/lambda/||' | sed 's|/aws/||')
    local destination_prefix="production-export/$log_group_clean/"
    
    echo -e "\n${BLUE}Exporting: $log_group ($size)${NC}"
    
    # Check if log group exists
    if ! aws logs describe-log-groups --log-group-name-prefix "$log_group" --query 'logGroups[0].logGroupName' --output text 2>/dev/null | grep -q "$log_group"; then
        echo -e "${YELLOW}⚠️  Log group not found, skipping${NC}"
        return 1
    fi
    
    # Get actual date range
    local date_range=$(get_log_date_range "$log_group")
    if [[ $? -ne 0 ]]; then
        echo -e "${YELLOW}⚠️  No data found, skipping${NC}"
        return 1
    fi
    
    local start_time=$(echo "$date_range" | cut -d' ' -f1)
    local end_time=$(echo "$date_range" | cut -d' ' -f2)
    
    # Create export task
    echo "Creating export task..."
    local task_id=$(aws logs create-export-task \
        --log-group-name "$log_group" \
        --from "$start_time" \
        --to "$end_time" \
        --destination "$S3_BUCKET" \
        --destination-prefix "$destination_prefix" \
        --query 'taskId' \
        --output text 2>/dev/null)
    
    if [[ -n "$task_id" && "$task_id" != "None" ]]; then
        echo -e "${GREEN}✓ Export task created: $task_id${NC}"
        echo "  Destination: s3://$S3_BUCKET/$destination_prefix"
        echo "  Expected size: $size"
        return 0
    else
        echo -e "${RED}✗ Failed to create export task${NC}"
        return 1
    fi
}

# Function to monitor export progress
monitor_exports() {
    echo -e "\n${BLUE}Monitoring export progress...${NC}"
    
    while true; do
        local active_tasks=$(aws logs describe-export-tasks \
            --query 'exportTasks[?status==`RUNNING` || status==`PENDING`]' \
            --output json)
        
        local task_count=$(echo "$active_tasks" | jq length)
        
        if [[ "$task_count" -eq 0 ]]; then
            echo -e "${GREEN}All export tasks completed!${NC}"
            break
        fi
        
        echo "Active export tasks: $task_count"
        echo "$active_tasks" | jq -r '.[] | "  \(.logGroupName): \(.status)"'
        
        echo "Waiting 60 seconds before next check..."
        sleep 60
    done
}

echo -e "${YELLOW}⚠️  This will export ALL historical data (~4GB+)${NC}"
echo -e "${YELLOW}   Large exports may take 1-2 hours to complete${NC}"
echo ""
read -p "Do you want to proceed with production export? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Aborted."
    exit 1
fi

# Export each log group
echo -e "${BLUE}Starting production exports...${NC}"
successful_exports=0

for log_group in "${!LOG_GROUPS[@]}"; do
    if export_log_group "$log_group" "${LOG_GROUPS[$log_group]}"; then
        successful_exports=$((successful_exports + 1))
    fi
    sleep 5  # Rate limiting
done

echo -e "\n${GREEN}🎉 Export process initiated!${NC}"
echo "============================================"
echo "Successfully created $successful_exports export tasks"
echo ""

# Show current status
echo -e "${BLUE}Current export status:${NC}"
aws logs describe-export-tasks \
    --query 'exportTasks[].{TaskId:taskId,Status:status,LogGroup:logGroupName}' \
    --output table

echo ""
echo -e "${BLUE}Monitor progress:${NC}"
echo "1. Run: aws logs describe-export-tasks --query 'exportTasks[].{TaskId:taskId,Status:status,LogGroup:logGroupName}' --output table"
echo "2. Check S3: aws s3 ls s3://$S3_BUCKET/production-export/ --recursive --human-readable"
echo "3. Or run: ./scripts/production-export.sh monitor"
echo ""

# Optional monitoring
if [[ "$1" == "monitor" ]]; then
    monitor_exports
fi
