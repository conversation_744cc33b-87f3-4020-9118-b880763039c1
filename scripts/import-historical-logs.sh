#!/bin/bash

# Import Historical CloudWatch Logs to OpenSearch
# This script pulls existing logs and sends them to OpenSearch

set -e

echo "📊 Importing Historical CloudWatch Logs to OpenSearch"
echo "===================================================="

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

# Configuration
LAMBDA_FUNCTION="infodocs-operations-opensearch-log-shipper"
DAYS_BACK=30  # How many days of history to import

# High-value log groups to import
LOG_GROUPS=(
    "/aws/lambda/BizPortalScraperV1"
    "/aws/lambda/Regbot2"
    "/aws/lambda/ValidationLambda"
    "/aws/lambda/beneficialOwnershipV1"
    "/aws/lambda/annualReturnV1"
    "/aws/lambda/directorChangeV1"
    "/aws/lambda/Pdf_Generator"
)

# Calculate start time (30 days ago) - macOS compatible
START_TIME=$(date -v-${DAYS_BACK}d +%s)000
END_TIME=$(date +%s)000

echo -e "${BLUE}Importing logs from $(date -v-${DAYS_BACK}d) to $(date)${NC}"
echo -e "${YELLOW}This will import approximately $(echo "${#LOG_GROUPS[@]} * $DAYS_BACK" | bc) days worth of data${NC}"
echo ""

# Function to import logs from a log group
import_log_group() {
    local log_group="$1"
    local batch_size=10000
    local next_token=""
    local event_count=0
    
    echo -e "${BLUE}Processing: $log_group${NC}"
    
    # Check if log group exists
    if ! aws logs describe-log-groups --log-group-name-prefix "$log_group" --query 'logGroups[0].logGroupName' --output text | grep -q "$log_group"; then
        echo -e "${YELLOW}⚠️  Log group $log_group not found, skipping${NC}"
        return
    fi
    
    # Get log streams for this group
    local streams=$(aws logs describe-log-streams \
        --log-group-name "$log_group" \
        --order-by LastEventTime \
        --descending \
        --max-items 50 \
        --query 'logStreams[].logStreamName' \
        --output text)
    
    if [[ -z "$streams" ]]; then
        echo -e "${YELLOW}⚠️  No log streams found in $log_group${NC}"
        return
    fi
    
    # Process each stream
    for stream in $streams; do
        echo -e "  Processing stream: $stream"
        
        # Get events from this stream
        local events=$(aws logs get-log-events \
            --log-group-name "$log_group" \
            --log-stream-name "$stream" \
            --start-time "$START_TIME" \
            --end-time "$END_TIME" \
            --query 'events[].{timestamp:timestamp,message:message}' \
            --output json)
        
        if [[ "$events" != "[]" ]]; then
            # Create a CloudWatch Logs event format for the lambda
            local log_data=$(echo "$events" | jq -c --arg group "$log_group" --arg stream "$stream" '{
                messageType: "DATA_MESSAGE",
                owner: "513036964009",
                logGroup: $group,
                logStream: $stream,
                subscriptionFilters: ["historical-import"],
                logEvents: .
            }')
            
            # Compress and base64 encode (like CloudWatch does)
            local compressed_data=$(echo "$log_data" | gzip | base64 -w 0)
            
            # Create the lambda payload
            local payload=$(jq -n --arg data "$compressed_data" '{
                awslogs: {
                    data: $data
                }
            }')
            
            # Invoke the lambda function
            if aws lambda invoke \
                --function-name "$LAMBDA_FUNCTION" \
                --payload "$payload" \
                --cli-binary-format raw-in-base64-out \
                /tmp/lambda-response.json >/dev/null 2>&1; then
                
                local batch_events=$(echo "$events" | jq length)
                event_count=$((event_count + batch_events))
                echo -e "    ${GREEN}✓ Processed $batch_events events${NC}"
            else
                echo -e "    ${RED}✗ Failed to process batch${NC}"
            fi
        fi
        
        # Rate limiting to avoid overwhelming the system
        sleep 1
    done
    
    echo -e "${GREEN}✓ Completed $log_group: $event_count total events${NC}"
    echo ""
}

# Confirm before proceeding
echo -e "${YELLOW}⚠️  This will import historical data for ${#LOG_GROUPS[@]} log groups${NC}"
echo -e "${YELLOW}   This may take 10-30 minutes and will invoke your Lambda function many times${NC}"
echo ""
read -p "Do you want to proceed? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Aborted."
    exit 1
fi

# Import each log group
total_start_time=$(date +%s)
for log_group in "${LOG_GROUPS[@]}"; do
    import_log_group "$log_group"
done

total_end_time=$(date +%s)
total_duration=$((total_end_time - total_start_time))

echo -e "\n${GREEN}🎉 Historical import complete!${NC}"
echo "============================================"
echo "Total time: ${total_duration} seconds"
echo ""
echo -e "${BLUE}Next steps:${NC}"
echo "1. Check OpenSearch for new indices: https://localhost:8443/_dashboards"
echo "2. Go to Stack Management → Index Management"
echo "3. Look for indices with today's date"
echo "4. Create index patterns to explore your data"
echo ""
echo -e "${YELLOW}Note: It may take 5-10 minutes for all data to appear in OpenSearch${NC}"
