#!/bin/bash
# Security policy validation script for InfoDocs Infrastructure

set -e

echo "🔒 Checking security policies..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

ERRORS=0

# Check for public S3 buckets
echo "Checking S3 bucket security..."
if grep -r "block_public_acls.*false" modules/s3/ 2>/dev/null; then
    echo -e "${RED}❌ ERROR: S3 bucket allows public ACLs${NC}"
    ERRORS=$((ERRORS + 1))
fi

if grep -r "block_public_policy.*false" modules/s3/ 2>/dev/null; then
    echo -e "${RED}❌ ERROR: S3 bucket allows public policies${NC}"
    ERRORS=$((ERRORS + 1))
fi

# Check for unencrypted resources
echo "Checking encryption requirements..."
if grep -r "server_side_encryption_configuration" modules/s3/ | grep -v "aws:kms"; then
    echo -e "${YELLOW}⚠️  WARNING: Non-KMS encryption found${NC}"
fi

# Check for hardcoded secrets
echo "Checking for hardcoded secrets..."
if grep -r -i "password.*=" modules/ | grep -v "variable\|output\|data" | grep -v ".tf:.*#"; then
    echo -e "${RED}❌ ERROR: Potential hardcoded password found${NC}"
    ERRORS=$((ERRORS + 1))
fi

if grep -r -i "secret.*=" modules/ | grep -v "variable\|output\|data" | grep -v ".tf:.*#"; then
    echo -e "${RED}❌ ERROR: Potential hardcoded secret found${NC}"
    ERRORS=$((ERRORS + 1))
fi

# Check for public access in security groups
echo "Checking security group rules..."
if grep -r "0.0.0.0/0" modules/ | grep -v "egress" | grep -v ".tf:.*#"; then
    echo -e "${YELLOW}⚠️  WARNING: Public access (0.0.0.0/0) found in ingress rules${NC}"
fi

# Check for required tags
echo "Checking required tags..."
REQUIRED_TAGS=("Environment" "Project" "ManagedBy")
for tag in "${REQUIRED_TAGS[@]}"; do
    if ! grep -r "\"$tag\"" modules/*/main.tf >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  WARNING: Required tag '$tag' not found in some modules${NC}"
    fi
done

# Check for KMS key usage
echo "Checking KMS encryption..."
if ! grep -r "kms_key" modules/s3/ >/dev/null 2>&1; then
    echo -e "${RED}❌ ERROR: S3 modules missing KMS encryption${NC}"
    ERRORS=$((ERRORS + 1))
fi

# Check for versioning on critical buckets
echo "Checking S3 versioning..."
if ! grep -r "versioning_configuration" modules/s3/ >/dev/null 2>&1; then
    echo -e "${RED}❌ ERROR: S3 versioning not enabled${NC}"
    ERRORS=$((ERRORS + 1))
fi

# Check for legal hold bucket object lock
echo "Checking legal hold security..."
if [ -d "modules/s3/legal-hold-s3-bucket" ]; then
    if ! grep -r "object_lock_configuration" modules/s3/legal-hold-s3-bucket/ >/dev/null 2>&1; then
        echo -e "${RED}❌ ERROR: Legal hold bucket missing object lock${NC}"
        ERRORS=$((ERRORS + 1))
    fi
fi

# Summary
if [ $ERRORS -eq 0 ]; then
    echo -e "${GREEN}✅ All security policy checks passed!${NC}"
    exit 0
else
    echo -e "${RED}❌ $ERRORS security policy violations found${NC}"
    exit 1
fi
