#!/bin/bash

echo "🔒 Setting up SSM Parameter Store Secrets"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if AWS CLI is configured
if ! aws sts get-caller-identity >/dev/null 2>&1; then
    echo -e "${RED}❌ AWS CLI not configured. Please run 'aws configure' first.${NC}"
    exit 1
fi

echo -e "${BLUE}📋 Creating Global SSM Parameters (shared across environments)${NC}"

# 1. Cloudflare Configuration
echo "Creating Cloudflare parameters..."
aws ssm put-parameter \
  --name "/infodocs/cloudflare/api_token" \
  --value "YOUR_CLOUDFLARE_API_TOKEN" \
  --type "SecureString" \
  --description "Cloudflare API token for DNS management" \
  --overwrite 2>/dev/null || echo "Parameter already exists"

aws ssm put-parameter \
  --name "/infodocs/cloudflare/zone_id" \
  --value "ab5ea0dd71ae5911fbd90599388a0d4b" \
  --type "String" \
  --description "Cloudflare zone ID for infodocs.co.za" \
  --overwrite 2>/dev/null || echo "Parameter already exists"

aws ssm put-parameter \
  --name "/infodocs/cloudflare/account_id" \
  --value "04fc3a26db877be07b4a6b840cb62d35" \
  --type "String" \
  --description "Cloudflare account ID" \
  --overwrite 2>/dev/null || echo "Parameter already exists"

# Server Configuration
echo "Creating server configuration parameters..."
aws ssm put-parameter \
  --name "/infodocs/server/main_ip_address" \
  --value "***********" \
  --type "String" \
  --description "Main server IP address for infodocs.co.za" \
  --overwrite 2>/dev/null || echo "Parameter already exists"

# AWS Account Configuration
echo "Creating AWS account configuration parameters..."
aws ssm put-parameter \
  --name "/infodocs/aws/account_id" \
  --value "************" \
  --type "String" \
  --description "AWS Account ID for InfoDocs infrastructure" \
  --overwrite 2>/dev/null || echo "Parameter already exists"

# IAM Configuration
echo "Creating IAM configuration parameters..."
aws ssm put-parameter \
  --name "/infodocs/iam/infrastructure_user_arn" \
  --value "arn:aws:iam::************:user/infrastructure-as-code-kms" \
  --type "String" \
  --description "IAM user ARN for infrastructure automation" \
  --overwrite 2>/dev/null || echo "Parameter already exists"

# 2. Wazuh Security Platform (centralized)
echo "Creating Wazuh parameters..."
aws ssm put-parameter \
  --name "/infodocs/wazuh/admin_password" \
  --value "Pw2pbLkJgcR74pzFu8RYEbj2gfxCWGALsZkscPjEssv-pwcHzo" \
  --type "SecureString" \
  --description "Wazuh admin password for centralized security monitoring" \
  --overwrite 2>/dev/null || echo "Parameter already exists"

aws ssm put-parameter \
  --name "/infodocs/wazuh/api_key" \
  --value "$(openssl rand -hex 32)" \
  --type "SecureString" \
  --description "Wazuh API key for REST API access" \
  --overwrite 2>/dev/null || echo "Parameter already exists"

aws ssm put-parameter \
  --name "/infodocs/wazuh/registration_password" \
  --value "$(openssl rand -base64 16)" \
  --type "SecureString" \
  --description "Wazuh agent registration password" \
  --overwrite 2>/dev/null || echo "Parameter already exists"

# 3. OpenSearch Platform (centralized)
echo "Creating OpenSearch parameters..."
aws ssm put-parameter \
  --name "/infodocs/opensearch/master_user" \
  --value "admin" \
  --type "String" \
  --description "OpenSearch master username" \
  --overwrite 2>/dev/null || echo "Parameter already exists"

aws ssm put-parameter \
  --name "/infodocs/opensearch/master_password" \
  --value "$(openssl rand -base64 32)" \
  --type "SecureString" \
  --description "OpenSearch master password for centralized log platform" \
  --overwrite 2>/dev/null || echo "Parameter already exists"

aws ssm put-parameter \
  --name "/infodocs/opensearch/api_key" \
  --value "$(openssl rand -hex 32)" \
  --type "SecureString" \
  --description "OpenSearch API key for authentication" \
  --overwrite 2>/dev/null || echo "Parameter already exists"

# 4. SSH Keys (if available)
echo "Creating SSH key parameters..."
if [[ -f ~/.ssh/infodocs-dev-infrastructure.pub ]]; then
    aws ssm put-parameter \
      --name "/infodocs/ssh/infrastructure_public_key" \
      --value "$(cat ~/.ssh/infodocs-dev-infrastructure.pub)" \
      --type "String" \
      --description "SSH public key for infrastructure access" \
      --overwrite 2>/dev/null || echo "Parameter already exists"
else
    echo -e "${YELLOW}⚠️  SSH key not found at ~/.ssh/infodocs-dev-infrastructure.pub${NC}"
    echo "Generate with: ssh-keygen -t rsa -b 4096 -f ~/.ssh/infodocs-dev-infrastructure"
fi

# 5. Infrastructure Database Configuration (separate from existing RDS)
echo "Creating infrastructure database parameters..."
aws ssm put-parameter \
  --name "/infodocs/infrastructure/database/master_password" \
  --value "$(openssl rand -base64 32)" \
  --type "SecureString" \
  --description "Infrastructure database master password (separate from main RDS)" \
  --overwrite 2>/dev/null || echo "Parameter already exists"

aws ssm put-parameter \
  --name "/infodocs/infrastructure/database/app_password" \
  --value "$(openssl rand -base64 32)" \
  --type "SecureString" \
  --description "Infrastructure database application user password (separate from main RDS)" \
  --overwrite 2>/dev/null || echo "Parameter already exists"

# 6. Grafana Monitoring Platform
echo "Creating Grafana parameters..."
aws ssm put-parameter \
  --name "/infodocs/grafana/admin_password" \
  --value "$(openssl rand -base64 32)" \
  --type "SecureString" \
  --description "Grafana admin password for monitoring dashboard" \
  --overwrite 2>/dev/null || echo "Parameter already exists"

aws ssm put-parameter \
  --name "/infodocs/grafana/admin_user" \
  --value "admin" \
  --type "String" \
  --description "Grafana admin username" \
  --overwrite 2>/dev/null || echo "Parameter already exists"

aws ssm put-parameter \
  --name "/infodocs/grafana/session_secret" \
  --value "$(openssl rand -hex 32)" \
  --type "SecureString" \
  --description "Grafana session secret key" \
  --overwrite 2>/dev/null || echo "Parameter already exists"

# Slack webhook placeholders (user needs to update with real URLs)
aws ssm put-parameter \
  --name "/infodocs/grafana/slack_webhook_critical" \
  --value "https://hooks.slack.com/services/YOUR/CRITICAL/WEBHOOK" \
  --type "SecureString" \
  --description "Slack webhook URL for critical alerts" \
  --overwrite 2>/dev/null || echo "Parameter already exists"

aws ssm put-parameter \
  --name "/infodocs/grafana/slack_webhook_infrastructure" \
  --value "https://hooks.slack.com/services/YOUR/INFRASTRUCTURE/WEBHOOK" \
  --type "SecureString" \
  --description "Slack webhook URL for infrastructure alerts" \
  --overwrite 2>/dev/null || echo "Parameter already exists"

aws ssm put-parameter \
  --name "/infodocs/grafana/slack_webhook_application" \
  --value "https://hooks.slack.com/services/YOUR/APPLICATION/WEBHOOK" \
  --type "SecureString" \
  --description "Slack webhook URL for application alerts" \
  --overwrite 2>/dev/null || echo "Parameter already exists"

aws ssm put-parameter \
  --name "/infodocs/grafana/slack_webhook_security" \
  --value "https://hooks.slack.com/services/YOUR/SECURITY/WEBHOOK" \
  --type "SecureString" \
  --description "Slack webhook URL for security alerts" \
  --overwrite 2>/dev/null || echo "Parameter already exists"

echo -e "\n${GREEN}✅ SSM Parameters created successfully!${NC}"

echo -e "\n${BLUE}📋 Verification:${NC}"
aws ssm get-parameters-by-path \
  --path "/infodocs" \
  --recursive \
  --query 'Parameters[].Name' \
  --output table

echo -e "\n${YELLOW}🔒 Security Note:${NC}"
echo "All sensitive parameters are encrypted with AWS managed KMS key."
echo "Use --with-decryption flag when retrieving sensitive values."
echo "Rotate passwords regularly (quarterly recommended)."
