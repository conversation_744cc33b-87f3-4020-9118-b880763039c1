#!/bin/bash

# Quick Zero Trust Setup for OpenSearch
# Gets you working access ASAP

set -e

echo "🚀 Quick Zero Trust Setup for OpenSearch"
echo "========================================"

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}We need a few details to set this up:${NC}"

# Get Cloudflare Account ID
echo -e "\n${YELLOW}1. Cloudflare Account ID${NC}"
echo "Go to: https://dash.cloudflare.com → Right sidebar → Account ID"
read -p "Enter your Cloudflare Account ID: " ACCOUNT_ID

# Get Cloudflare Zone ID  
echo -e "\n${YELLOW}2. Cloudflare Zone ID${NC}"
echo "Go to: https://dash.cloudflare.com → infodocs.co.za → Right sidebar → Zone ID"
read -p "Enter your Zone ID: " ZONE_ID

# Get API Token
echo -e "\n${YELLOW}3. Cloudflare API Token${NC}"
echo "Go to: https://dash.cloudflare.com/profile/api-tokens → Create Token → Custom Token"
echo "Permissions needed: Zone:Zone:Read, Zone:DNS:Edit, Account:Cloudflare Tunnel:Edit, Account:Access:Edit"
read -s -p "Enter your API Token: " API_TOKEN
echo

# Store in SSM
echo -e "\n${BLUE}Storing configuration...${NC}"

aws ssm put-parameter \
  --name "/infodocs/cloudflare/account_id" \
  --value "$ACCOUNT_ID" \
  --type "String" \
  --description "Cloudflare Account ID" \
  --overwrite

aws ssm put-parameter \
  --name "/infodocs/cloudflare/zone_id" \
  --value "$ZONE_ID" \
  --type "String" \
  --description "Cloudflare Zone ID" \
  --overwrite

aws ssm put-parameter \
  --name "/infodocs/cloudflare/api_token" \
  --value "$API_TOKEN" \
  --type "SecureString" \
  --description "Cloudflare API Token" \
  --overwrite

# Google Workspace setup
echo -e "\n${YELLOW}4. Google Workspace Domain${NC}"
read -p "Enter your Google Workspace domain (e.g., infodocs.co.za): " WORKSPACE_DOMAIN

aws ssm put-parameter \
  --name "/infodocs/google/workspace/domain" \
  --value "$WORKSPACE_DOMAIN" \
  --type "String" \
  --description "Google Workspace domain" \
  --overwrite

echo -e "\n${GREEN}✅ Basic configuration stored!${NC}"
echo -e "\n${YELLOW}Next: We'll create a simple Cloudflare Access rule${NC}"
echo "This will give you immediate access to OpenSearch via opensearch.infodocs.co.za"
