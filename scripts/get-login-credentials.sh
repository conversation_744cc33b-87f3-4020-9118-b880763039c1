#!/bin/bash

echo "🔐 InfoDocs Infrastructure Login Credentials"
echo "============================================="

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if AWS CLI is configured
if ! aws sts get-caller-identity >/dev/null 2>&1; then
    echo -e "${RED}❌ AWS CLI not configured. Please run 'aws configure' first.${NC}"
    exit 1
fi

echo -e "${BLUE}🔒 Security Platform Credentials${NC}"
echo "================================="

echo -e "${GREEN}Wazuh Security Platform:${NC}"
echo "URL: https://wazuh.infodocs.co.za"
echo "Username: admin"
WAZUH_PASSWORD=$(aws ssm get-parameter --name "/infodocs/wazuh/admin_password" --with-decryption --query 'Parameter.Value' --output text 2>/dev/null)
if [[ $? -eq 0 ]]; then
    echo "Password: $WAZUH_PASSWORD"
else
    echo "Password: ❌ Unable to retrieve (check permissions)"
fi

echo -e "\n${GREEN}OpenSearch Analytics Platform:${NC}"
echo "URL: https://opensearch.infodocs.co.za"
echo "Username: admin"
OPENSEARCH_PASSWORD=$(aws ssm get-parameter --name "/infodocs/opensearch/master_password" --with-decryption --query 'Parameter.Value' --output text 2>/dev/null)
if [[ $? -eq 0 ]]; then
    echo "Password: $OPENSEARCH_PASSWORD"
else
    echo "Password: ❌ Unable to retrieve (check permissions)"
fi

echo -e "\n${BLUE}🌐 DNS & CDN Configuration${NC}"
echo "=========================="

echo -e "${GREEN}Cloudflare Configuration:${NC}"
CLOUDFLARE_ZONE_ID=$(aws ssm get-parameter --name "/infodocs/cloudflare/zone_id" --query 'Parameter.Value' --output text 2>/dev/null)
CLOUDFLARE_ACCOUNT_ID=$(aws ssm get-parameter --name "/infodocs/cloudflare/account_id" --query 'Parameter.Value' --output text 2>/dev/null)

if [[ $? -eq 0 ]]; then
    echo "Zone ID: $CLOUDFLARE_ZONE_ID"
    echo "Account ID: $CLOUDFLARE_ACCOUNT_ID"
else
    echo "❌ Unable to retrieve Cloudflare configuration"
fi

echo -e "\n${BLUE}🗄️ Infrastructure Database (Separate from Main RDS)${NC}"
echo "=================================================="

echo -e "${GREEN}Infrastructure Database Credentials:${NC}"
INFRA_DB_MASTER=$(aws ssm get-parameter --name "/infodocs/infrastructure/database/master_password" --with-decryption --query 'Parameter.Value' --output text 2>/dev/null)
INFRA_DB_APP=$(aws ssm get-parameter --name "/infodocs/infrastructure/database/app_password" --with-decryption --query 'Parameter.Value' --output text 2>/dev/null)

if [[ $? -eq 0 ]]; then
    echo "Master Password: $INFRA_DB_MASTER"
    echo "App Password: $INFRA_DB_APP"
    echo -e "${YELLOW}Note: These are for infrastructure databases, separate from your existing RDS${NC}"
else
    echo "❌ Unable to retrieve infrastructure database credentials"
fi

echo -e "\n${BLUE}🔑 API Keys${NC}"
echo "==========="

echo -e "${GREEN}Wazuh API Key:${NC}"
WAZUH_API_KEY=$(aws ssm get-parameter --name "/infodocs/wazuh/api_key" --with-decryption --query 'Parameter.Value' --output text 2>/dev/null)
if [[ $? -eq 0 ]]; then
    echo "$WAZUH_API_KEY"
else
    echo "❌ Unable to retrieve Wazuh API key"
fi

echo -e "\n${GREEN}OpenSearch API Key:${NC}"
OPENSEARCH_API_KEY=$(aws ssm get-parameter --name "/infodocs/opensearch/api_key" --with-decryption --query 'Parameter.Value' --output text 2>/dev/null)
if [[ $? -eq 0 ]]; then
    echo "$OPENSEARCH_API_KEY"
else
    echo "❌ Unable to retrieve OpenSearch API key"
fi

echo -e "\n${YELLOW}💡 Tips:${NC}"
echo "- Use these credentials to log into the respective platforms"
echo "- API keys can be used for programmatic access"
echo "- Rotate passwords every 3-6 months using: ./scripts/rotate-passwords.sh"
echo "- All credentials are encrypted in AWS SSM Parameter Store"

echo -e "\n${BLUE}🔄 Password Rotation Schedule:${NC}"
echo "- Security platforms (Wazuh, OpenSearch): Every 3 months"
echo "- API keys: Every 6 months"
echo "- Infrastructure databases: Every 6 months"
