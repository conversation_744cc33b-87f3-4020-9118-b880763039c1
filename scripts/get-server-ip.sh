#!/bin/bash

echo "🌐 Finding Your Server IP Address for DNS Configuration"
echo "======================================================"

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 Checking current infodocs.co.za DNS resolution:${NC}"

# Check current DNS resolution
CURRENT_IP=$(dig +short infodocs.co.za @******* 2>/dev/null | head -1)
if [[ -n "$CURRENT_IP" && "$CURRENT_IP" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
    echo -e "${GREEN}Current IP: $CURRENT_IP${NC}"
else
    echo -e "${YELLOW}⚠️  Could not resolve current IP${NC}"
fi

echo -e "\n${BLUE}🔍 Checking for AWS Load Balancers:${NC}"

# Check for AWS Application Load Balancers
ALB_IPS=$(aws elbv2 describe-load-balancers \
    --query 'LoadBalancers[?contains(LoadBalancerName, `infodocs`) || contains(DNSName, `infodocs`)].{Name:LoadBalancerName,DNS:DNSName,Type:Type}' \
    --output table 2>/dev/null)

if [[ $? -eq 0 && -n "$ALB_IPS" ]]; then
    echo "$ALB_IPS"
    echo -e "${YELLOW}💡 If using ALB, use the DNS name in CNAME record instead of A record${NC}"
else
    echo "No AWS Load Balancers found with 'infodocs' in the name"
fi

echo -e "\n${BLUE}🔍 Checking for AWS EC2 instances:${NC}"

# Check for EC2 instances that might be web servers
EC2_IPS=$(aws ec2 describe-instances \
    --filters "Name=instance-state-name,Values=running" \
    --query 'Reservations[].Instances[?Tags[?Key==`Name` && contains(Value, `web`) || contains(Value, `app`) || contains(Value, `infodocs`)]].{Name:Tags[?Key==`Name`].Value|[0],PublicIP:PublicIpAddress,PrivateIP:PrivateIpAddress,InstanceId:InstanceId}' \
    --output table 2>/dev/null)

if [[ $? -eq 0 && -n "$EC2_IPS" ]]; then
    echo "$EC2_IPS"
else
    echo "No EC2 instances found with web/app/infodocs in the name"
fi

echo -e "\n${BLUE}🔍 Checking for Elastic IPs:${NC}"

# Check for Elastic IPs
EIP_LIST=$(aws ec2 describe-addresses \
    --query 'Addresses[].{PublicIP:PublicIp,PrivateIP:PrivateIpAddress,InstanceId:InstanceId,AllocationId:AllocationId}' \
    --output table 2>/dev/null)

if [[ $? -eq 0 && -n "$EIP_LIST" ]]; then
    echo "$EIP_LIST"
else
    echo "No Elastic IPs found"
fi

echo -e "\n${BLUE}🔍 Checking CloudFront distributions:${NC}"

# Check for CloudFront distributions
CF_DIST=$(aws cloudfront list-distributions \
    --query 'DistributionList.Items[?contains(Comment, `infodocs`) || contains(Origins.Items[0].DomainName, `infodocs`)].{Id:Id,DomainName:DomainName,Comment:Comment,Status:Status}' \
    --output table 2>/dev/null)

if [[ $? -eq 0 && -n "$CF_DIST" ]]; then
    echo "$CF_DIST"
    echo -e "${YELLOW}💡 If using CloudFront, use the distribution domain in CNAME record${NC}"
else
    echo "No CloudFront distributions found with 'infodocs' reference"
fi

echo -e "\n${YELLOW}📋 Next Steps:${NC}"
echo "=============="
echo "1. Identify which IP/service hosts your infodocs.co.za website"
echo "2. Update the Cloudflare configuration with the correct IP:"
echo ""
echo -e "${GREEN}   # Edit this file:${NC}"
echo "   environments/dev/af-south-1/dns/cloudflare/terragrunt.hcl"
echo ""
echo -e "${GREEN}   # Replace this line:${NC}"
echo "   server_ip_address = \"YOUR_ACTUAL_SERVER_IP_HERE\""
echo ""
echo -e "${GREEN}   # With your actual IP:${NC}"
echo "   server_ip_address = \"YOUR_ACTUAL_IP\""
echo ""
echo -e "${BLUE}💡 Common scenarios:${NC}"
echo "   - Web server on EC2: Use the Public IP from EC2 list above"
echo "   - Load Balancer: Use ALB DNS name (requires CNAME record type)"
echo "   - CloudFront: Use distribution domain (requires CNAME record type)"
echo "   - External hosting: Contact your hosting provider for the IP"

echo -e "\n${RED}⚠️  Important:${NC}"
echo "   The current dummy IP (*********) is a test address and won't work!"
echo "   You must replace it with your actual server IP before deployment."
