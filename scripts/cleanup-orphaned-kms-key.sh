#!/bin/bash

# Cleanup script for orphaned KMS key
# This script helps clean up the KMS key that was created without proper admin access

set -e

echo "🔍 KMS Key Cleanup Script"
echo "========================="

# The orphaned key ID from your error
ORPHANED_KEY_ID="9b8626a9-cee9-41e3-ac43-ac8167e4a049"
AWS_REGION="af-south-1"

echo "🔑 Orphaned Key ID: $ORPHANED_KEY_ID"
echo "🌍 Region: $AWS_REGION"
echo ""

# Check if we can access the key
echo "🔍 Checking key access..."
if aws kms describe-key --key-id "$ORPHANED_KEY_ID" --region "$AWS_REGION" >/dev/null 2>&1; then
    echo "✅ Key is accessible - you can manage it"

    # Show key details
    echo ""
    echo "📋 Key Details:"
    aws kms describe-key --key-id "$ORPHANED_KEY_ID" --region "$AWS_REGION" --query 'KeyMetadata.{KeyId:KeyId,State:KeyState,Description:Description}' --output table

    # Check for aliases
    echo ""
    echo "🏷️  Checking for aliases..."
    ALIASES=$(aws kms list-aliases --region "$AWS_REGION" --query "Aliases[?TargetKeyId=='$ORPHANED_KEY_ID'].AliasName" --output text)

    if [ -z "$ALIASES" ]; then
        echo "❌ No aliases found for this key"
        echo ""
        echo "🛠️  Options:"
        echo "1. Delete the orphaned key (recommended):"
        echo "   aws kms schedule-key-deletion --key-id $ORPHANED_KEY_ID --pending-window-in-days 7 --region $AWS_REGION"
        echo ""
        echo "2. Or add an alias manually:"
        echo "   aws kms create-alias --alias-name alias/infodocs-dev-key --target-key-id $ORPHANED_KEY_ID --region $AWS_REGION"
    else
        echo "✅ Found aliases: $ALIASES"
    fi

else
    echo "❌ Cannot access the key - insufficient permissions"
    echo ""
    echo "🛠️  To fix this, you need to:"
    echo "1. Add KMS permissions to your user account, OR"
    echo "2. Use the root account to delete the orphaned key, OR"
    echo "3. Use the infrastructure-as-code-kms user to manage it"
    echo ""
    echo "💡 After fixing permissions, you can delete the orphaned key with:"
    echo "   aws kms schedule-key-deletion --key-id $ORPHANED_KEY_ID --pending-window-in-days 7 --region $AWS_REGION"
fi

echo ""
echo "🎯 Recommended Action:"
echo "1. Delete the orphaned key (it has no alias and limited access)"
echo "2. Deploy the new admin-friendly KMS configuration"
echo "3. The new key will have proper admin access for all users"
echo ""
echo "🚀 After cleanup, run:"
echo "   cd environments/dev/af-south-1/security/kms"
echo "   terragrunt apply"
