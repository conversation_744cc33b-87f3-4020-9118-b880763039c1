#!/bin/bash

# Export CloudWatch Logs to S3 for OpenSearch Integration
# This creates export tasks to move logs from CloudWatch to S3

set -e

echo "📦 Exporting CloudWatch Logs to S3"
echo "=================================="

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

# Configuration
S3_BUCKET="infodocs-operations-opensearch"
DAYS_BACK=90  # Extended to capture more historical data

# High-value log groups to export
LOG_GROUPS=(
    "/aws/lambda/BizPortalScraperV1"
    "/aws/lambda/Regbot2"
    "/aws/lambda/ValidationLambda"
    "/aws/lambda/beneficialOwnershipV1"
    "/aws/lambda/annualReturnV1"
    "/aws/lambda/directorChangeV1"
    "/aws/lambda/Pdf_Generator"
    "/aws/lambda-insights"
)

# Export ALL data - no time restrictions
END_TIME=$(date +%s)
START_TIME=0  # Start from epoch (1970) to get everything

# Convert to milliseconds for CloudWatch
START_TIME_MS=0  # Export everything from the beginning
END_TIME_MS=$((END_TIME * 1000))

echo -e "${BLUE}Export Configuration:${NC}"
echo "S3 Bucket: $S3_BUCKET"
echo "Time Range: ALL HISTORICAL DATA (complete export)"
echo "Log Groups: ${#LOG_GROUPS[@]} groups"
echo ""

# Function to create export task
create_export_task() {
    local log_group="$1"
    local log_group_clean=$(echo "$log_group" | sed 's/[^a-zA-Z0-9]/-/g' | sed 's/^-//' | sed 's/-$//')

    # Create structured path: logs/service-type/service-name/year/month/day/
    local service_name=$(echo "$log_group" | sed 's|/aws/lambda/||' | sed 's|/aws/||')
    local current_date=$(date +%Y/%m/%d)
    local destination_prefix="logs/lambda/$service_name/$current_date/"
    
    echo -e "${BLUE}Exporting: $log_group${NC}"
    
    # Check if log group exists and has data
    if ! aws logs describe-log-groups --log-group-name-prefix "$log_group" --query 'logGroups[0].logGroupName' --output text 2>/dev/null | grep -q "$log_group"; then
        echo -e "${YELLOW}⚠️  Log group not found, skipping${NC}"
        return 1
    fi
    
    # Check for any data (no time restrictions)
    local stream_count=$(aws logs describe-log-streams \
        --log-group-name "$log_group" \
        --query "length(logStreams)" \
        --output text 2>/dev/null || echo "0")
    
    if [[ "$stream_count" == "0" ]]; then
        echo -e "${YELLOW}⚠️  No data in specified time range, skipping${NC}"
        return 1
    fi
    
    echo "Found $stream_count log streams with data"
    
    # Create export task
    local task_id=$(aws logs create-export-task \
        --log-group-name "$log_group" \
        --from "$START_TIME_MS" \
        --to "$END_TIME_MS" \
        --destination "$S3_BUCKET" \
        --destination-prefix "$destination_prefix" \
        --query 'taskId' \
        --output text 2>/dev/null)
    
    if [[ -n "$task_id" && "$task_id" != "None" ]]; then
        echo -e "${GREEN}✓ Export task created: $task_id${NC}"
        echo "  Destination: s3://$S3_BUCKET$destination_prefix"
        return 0
    else
        echo -e "${RED}✗ Failed to create export task${NC}"
        return 1
    fi
}

# Function to check export task status
check_export_status() {
    echo -e "\n${BLUE}Checking export task status...${NC}"
    
    local tasks=$(aws logs describe-export-tasks \
        --query 'exportTasks[?status==`RUNNING` || status==`PENDING`].{TaskId:taskId,Status:status,LogGroup:logGroupName}' \
        --output table 2>/dev/null)
    
    if [[ -n "$tasks" ]]; then
        echo "$tasks"
        echo -e "${YELLOW}Export tasks are running. This may take 10-30 minutes.${NC}"
    else
        echo -e "${GREEN}No active export tasks found.${NC}"
    fi
}

echo -e "${YELLOW}⚠️  This will export ALL HISTORICAL DATA to S3${NC}"
echo -e "${YELLOW}   This includes ~4GB+ of data and may take 30-60 minutes${NC}"
echo -e "${YELLOW}   Large exports (like BizPortalScraperV1 with 1.8GB) will take longer${NC}"
echo ""
read -p "Do you want to proceed? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Aborted."
    exit 1
fi

# Create export tasks
echo -e "${BLUE}Creating export tasks...${NC}"
echo ""

successful_exports=0
for log_group in "${LOG_GROUPS[@]}"; do
    if create_export_task "$log_group"; then
        successful_exports=$((successful_exports + 1))
    fi
    echo ""
    sleep 2  # Rate limiting
done

echo -e "\n${GREEN}🎉 Export process initiated!${NC}"
echo "============================================"
echo "Successfully created $successful_exports export tasks"
echo ""

# Check current status
check_export_status

echo ""
echo -e "${BLUE}Next steps:${NC}"
echo "1. Wait 10-30 minutes for exports to complete"
echo "2. Check S3 bucket: aws s3 ls s3://$S3_BUCKET/ --recursive"
echo "3. Configure OpenSearch to read from S3"
echo "4. Set up ongoing log streaming (Kinesis Data Firehose)"
echo ""
echo -e "${YELLOW}Monitor export progress:${NC}"
echo "aws logs describe-export-tasks --query 'exportTasks[].{TaskId:taskId,Status:status,LogGroup:logGroupName}' --output table"
