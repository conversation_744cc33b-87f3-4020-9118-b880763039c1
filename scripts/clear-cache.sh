#!/bin/bash

# Clear Cache Script for InfoDocs Infrastructure
# This script clears all Terragrunt and OpenTofu cache files

set -e

echo "🧹 Clearing InfoDocs Infrastructure Cache"
echo "========================================"

# Function to safely remove directories
safe_remove() {
    local pattern="$1"
    local description="$2"

    echo "🔍 Looking for $description..."

    if find . -type d -name "$pattern" 2>/dev/null | grep -q .; then
        echo "📁 Found $description directories:"
        find . -type d -name "$pattern" -print
        echo "🗑️  Removing $description..."
        find . -type d -name "$pattern" -exec rm -rf {} + 2>/dev/null || true
        echo "✅ $description cleared"
    else
        echo "✅ No $description found"
    fi
    echo ""
}

# Function to safely remove files
safe_remove_files() {
    local pattern="$1"
    local description="$2"

    echo "🔍 Looking for $description..."

    if find . -name "$pattern" 2>/dev/null | grep -q .; then
        echo "📄 Found $description files:"
        find . -name "$pattern" -print
        echo "🗑️  Removing $description..."
        find . -name "$pattern" -delete 2>/dev/null || true
        echo "✅ $description cleared"
    else
        echo "✅ No $description found"
    fi
    echo ""
}

# Clear Terragrunt cache
safe_remove ".terragrunt-cache" "Terragrunt cache"

# Clear OpenTofu/Terraform cache
safe_remove ".terraform" "OpenTofu/Terraform cache"

# Clear lock files
safe_remove_files ".terraform.lock.hcl" "OpenTofu/Terraform lock"

# Clear plan files
safe_remove_files "tfplan-*" "Terraform plan"
safe_remove_files "*.tfplan" "Terraform plan"

# Clear generated files (but keep them as they're needed)
echo "ℹ️  Note: Generated provider and backend files are kept as they're managed by Terragrunt"

# Clear plugin cache if it exists
if [ -d "$HOME/.terraform.d/plugin-cache" ]; then
    echo "🔍 Clearing global plugin cache..."
    rm -rf "$HOME/.terraform.d/plugin-cache"/*
    echo "✅ Global plugin cache cleared"
else
    echo "✅ No global plugin cache found"
fi

echo ""
echo "🎉 Cache clearing completed!"
echo ""
echo "📋 Next steps:"
echo "1. Navigate to your desired module: cd environments/dev/af-south-1/kms"
echo "2. Initialize: terragrunt init"
echo "3. Plan: terragrunt plan"
echo "4. Apply: terragrunt apply"
echo ""
echo "⚠️  Remember to deploy modules in the correct order:"
echo "   KMS → VPC → SSM → Cloudflare → OpenSearch → Wazuh → Selenium"
