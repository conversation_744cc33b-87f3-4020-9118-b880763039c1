#!/bin/bash

# Cloudflare Import Script
# Safely imports existing Cloudflare resources into Terraform state

set -e

echo "📥 Importing existing Cloudflare resources into Terraform"
echo "========================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if discovery was run
if [[ ! -f /tmp/cloudflare_existing.env ]]; then
    echo -e "${RED}❌ Please run ./scripts/cloudflare-discovery.sh first${NC}"
    exit 1
fi

# Load existing resource IDs
source /tmp/cloudflare_existing.env

echo -e "${BLUE}Found existing resources to import:${NC}"
cat /tmp/cloudflare_existing.env

echo -e "\n${YELLOW}⚠️  IMPORTANT: This will import existing resources into Terraform state.${NC}"
echo -e "${YELLOW}   Make sure you understand what this means:${NC}"
echo "   - Existing resources will be managed by Terraform"
echo "   - Future changes must be made via IaC"
echo "   - Manual changes in Cloudflare will cause drift"
echo ""
read -p "Do you want to proceed? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Aborted."
    exit 1
fi

# Function to import resource safely
import_resource() {
    local module_path="$1"
    local resource_name="$2"
    local resource_id="$3"
    local description="$4"
    
    echo -e "\n${BLUE}Importing: $description${NC}"
    echo "Module: $module_path"
    echo "Resource: $resource_name"
    echo "ID: $resource_id"
    
    cd "$module_path"
    
    # Check if resource already exists in state
    if terragrunt state list | grep -q "$resource_name"; then
        echo -e "${YELLOW}⚠️  Resource already in state, skipping${NC}"
        cd - > /dev/null
        return
    fi
    
    # Try to import
    if terragrunt import "$resource_name" "$resource_id"; then
        echo -e "${GREEN}✅ Successfully imported $description${NC}"
    else
        echo -e "${RED}❌ Failed to import $description${NC}"
        echo "You may need to adjust the resource configuration to match existing settings"
    fi
    
    cd - > /dev/null
}

# Import DNS record if exists
if [[ -n "${OPENSEARCH_DNS_RECORD_ID:-}" ]]; then
    echo -e "\n${YELLOW}Note: DNS record will be managed by tunnel module${NC}"
    echo "The existing record will be replaced when tunnel is deployed"
fi

# Import Access Application if exists
if [[ -n "${OPENSEARCH_ACCESS_APP_ID:-}" ]]; then
    import_resource \
        "environments/operations/af-south-1/cloudflare/access" \
        "cloudflare_access_application.opensearch" \
        "$OPENSEARCH_ACCESS_APP_ID" \
        "OpenSearch Access Application"
fi

# Import Google Workspace IDP if exists
if [[ -n "${GOOGLE_WORKSPACE_IDP_ID:-}" ]]; then
    import_resource \
        "environments/operations/af-south-1/cloudflare/access" \
        "cloudflare_access_identity_provider.google_workspace" \
        "$GOOGLE_WORKSPACE_IDP_ID" \
        "Google Workspace Identity Provider"
fi

# Import WARP device posture rule if exists
if [[ -n "${EXISTING_WARP_RULE_ID:-}" ]]; then
    import_resource \
        "environments/operations/af-south-1/cloudflare/access" \
        "cloudflare_access_device_posture_rule.warp_connected" \
        "$EXISTING_WARP_RULE_ID" \
        "WARP Device Posture Rule"
fi

# Import tunnel if exists
if [[ -n "${EXISTING_TUNNEL_ID:-}" ]]; then
    echo -e "\n${YELLOW}⚠️  Found existing tunnel: $EXISTING_TUNNEL_ID${NC}"
    echo "You have two options:"
    echo "1. Import existing tunnel (recommended if it's working)"
    echo "2. Delete existing tunnel and create new one"
    echo ""
    read -p "Import existing tunnel? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        import_resource \
            "environments/operations/af-south-1/cloudflare/tunnel" \
            "cloudflare_tunnel.opensearch_tunnel" \
            "$EXISTING_TUNNEL_ID" \
            "OpenSearch Tunnel"
    else
        echo -e "${YELLOW}⚠️  You'll need to manually delete the existing tunnel before deploying${NC}"
        echo "Tunnel ID to delete: $EXISTING_TUNNEL_ID"
    fi
fi

echo -e "\n${GREEN}🎉 Import process complete!${NC}"
echo "=========================="

echo -e "\n${BLUE}Next Steps:${NC}"
echo "1. Review imported resources:"
echo "   cd environments/operations/af-south-1/cloudflare/access && terragrunt plan"
echo "   cd environments/operations/af-south-1/cloudflare/tunnel && terragrunt plan"
echo ""
echo "2. If plan shows no changes, resources are properly imported"
echo "3. If plan shows changes, you may need to adjust configuration to match existing setup"
echo ""
echo "4. Deploy any missing resources:"
echo "   terragrunt apply"

echo -e "\n${YELLOW}💡 Pro Tips:${NC}"
echo "- Use 'terragrunt plan' to see what changes would be made"
echo "- If you see unwanted changes, adjust the module configuration"
echo "- Consider backing up your current Cloudflare settings before proceeding"

# Clean up
rm -f /tmp/cloudflare_existing.env
