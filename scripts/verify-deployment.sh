#!/bin/bash

# Set variables
ENV="dev"
AWS_PROFILE="infodocs-main"
export AWS_PROFILE=$AWS_PROFILE

echo "Verifying $ENV environment deployment..."

# Check KMS keys
echo "Checking KMS keys..."
aws kms list-aliases | grep "infodocs-$ENV"

# Check SSM parameters
echo "Checking SSM parameters..."
aws ssm describe-parameters --parameter-filters "Key=Path,Values=/infodocs/$ENV" | jq '.Parameters[].Name'

# Check EC2 instances
echo "Checking EC2 instances..."
aws ec2 describe-instances --filters "Name=tag:Environment,Values=$ENV" "Name=tag:Project,Values=infodocs" | jq '.Reservations[].Instances[] | {InstanceId, State: .State.Name, Type: .InstanceType, Name: (.Tags[] | select(.Key=="Name") | .Value)}'

# Check security groups
echo "Checking security groups..."
aws ec2 describe-security-groups --filters "Name=tag:Environment,Values=$ENV" "Name=tag:Project,Values=infodocs" | jq '.SecurityGroups[] | {GroupId, GroupName, Description}'

echo "Verification complete."
