#!/bin/bash
# Compliance validation script for InfoDocs Infrastructure

set -e

echo "⚖️ Validating compliance requirements..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

ERRORS=0
WARNINGS=0

# POPIA Compliance Checks
echo "Checking POPIA compliance..."

# Data encryption at rest
if ! grep -r "server_side_encryption_configuration" modules/s3/ >/dev/null 2>&1; then
    echo -e "${RED}❌ POPIA: Data not encrypted at rest${NC}"
    ERRORS=$((ERRORS + 1))
fi

# Access logging
if ! grep -r "cloudwatch_log_group" modules/s3/ >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  POPIA: Access logging not configured${NC}"
    WARNINGS=$((WARNINGS + 1))
fi

# GDPR Compliance Checks
echo "Checking GDPR compliance..."

# Data retention policies
if ! grep -r "lifecycle_configuration" modules/s3/ >/dev/null 2>&1; then
    echo -e "${RED}❌ GDPR: No data retention policies found${NC}"
    ERRORS=$((ERRORS + 1))
fi

# Right to erasure capability
if ! grep -r "enable_automatic_deletion" modules/s3/ >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  GDPR: Right to erasure not explicitly configured${NC}"
    WARNINGS=$((WARNINGS + 1))
fi

# SOX Compliance Checks
echo "Checking SOX compliance..."

# Audit trail requirements
if ! grep -r "versioning_configuration" modules/s3/ >/dev/null 2>&1; then
    echo -e "${RED}❌ SOX: Versioning not enabled for audit trails${NC}"
    ERRORS=$((ERRORS + 1))
fi

# Immutable storage for financial records
if [ -d "modules/s3/legal-hold-s3-bucket" ]; then
    if ! grep -r "object_lock_configuration" modules/s3/legal-hold-s3-bucket/ >/dev/null 2>&1; then
        echo -e "${RED}❌ SOX: Immutable storage not configured${NC}"
        ERRORS=$((ERRORS + 1))
    fi
else
    echo -e "${RED}❌ SOX: Legal hold bucket not found${NC}"
    ERRORS=$((ERRORS + 1))
fi

# 7-year retention requirement
if ! grep -r "2555" modules/s3/ >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  SOX: 7-year retention period not explicitly set${NC}"
    WARNINGS=$((WARNINGS + 1))
fi

# HIPAA Compliance Checks (if applicable)
echo "Checking HIPAA compliance..."

# Encryption in transit
if ! grep -r "aws:SecureTransport" modules/s3/ >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  HIPAA: Secure transport policy not enforced${NC}"
    WARNINGS=$((WARNINGS + 1))
fi

# Access controls
if ! grep -r "iam_policy" modules/s3/ >/dev/null 2>&1; then
    echo -e "${RED}❌ HIPAA: IAM policies not configured${NC}"
    ERRORS=$((ERRORS + 1))
fi

# PCI-DSS Compliance Checks (for payment data)
echo "Checking PCI-DSS compliance..."

# Network segmentation
if ! grep -r "allowed_cidr_blocks" modules/ >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  PCI-DSS: Network segmentation not explicitly configured${NC}"
    WARNINGS=$((WARNINGS + 1))
fi

# Security monitoring
if [ ! -d "modules/s3/wazuh-s3-bucket" ]; then
    echo -e "${RED}❌ PCI-DSS: Security monitoring not configured${NC}"
    ERRORS=$((ERRORS + 1))
fi

# General Compliance Checks
echo "Checking general compliance requirements..."

# Data classification
if ! grep -r "data.classification\|DataClass\|security.classification" modules/ >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  Data classification tags missing${NC}"
    WARNINGS=$((WARNINGS + 1))
fi

# Compliance framework tags
FRAMEWORKS=("POPIA" "GDPR" "SOX" "HIPAA" "PCI-DSS")
for framework in "${FRAMEWORKS[@]}"; do
    if ! grep -r "$framework" modules/ >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  Compliance framework '$framework' not tagged${NC}"
        WARNINGS=$((WARNINGS + 1))
    fi
done

# Backup and disaster recovery
if ! grep -r "cross_region_replication\|backup" modules/ >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  Backup/DR configuration not found${NC}"
    WARNINGS=$((WARNINGS + 1))
fi

# Security incident response
if [ ! -d "modules/s3/legal-hold-s3-bucket" ] || [ ! -d "modules/lambda/wazuh-lambda-log-shipper" ]; then
    echo -e "${RED}❌ Security incident response capability not configured${NC}"
    ERRORS=$((ERRORS + 1))
fi

# Summary
echo ""
echo "📊 Compliance Validation Summary:"
echo "================================"

if [ $ERRORS -eq 0 ] && [ $WARNINGS -eq 0 ]; then
    echo -e "${GREEN}✅ All compliance requirements met!${NC}"
    exit 0
elif [ $ERRORS -eq 0 ]; then
    echo -e "${YELLOW}⚠️  $WARNINGS compliance warnings found (non-blocking)${NC}"
    echo -e "${GREEN}✅ No critical compliance violations${NC}"
    exit 0
else
    echo -e "${RED}❌ $ERRORS critical compliance violations found${NC}"
    echo -e "${YELLOW}⚠️  $WARNINGS compliance warnings found${NC}"
    echo ""
    echo "Critical violations must be resolved before deployment."
    exit 1
fi
