#!/bin/bash

# Deploy infrastructure using OpenTofu and Terragrunt
# Usage: ./deploy-infrastructure.sh [environment]

set -e

ENVIRONMENT=${1:-dev}
REGION="af-south-1"

echo "🚀 Deploying infrastructure for environment: $ENVIRONMENT in region: $REGION"

# Deploy KMS first (needed for encryption)
echo "📦 Deploying KMS module..."
cd environments/$ENVIRONMENT/$REGION/kms
terragrunt init
terragrunt apply -auto-approve

# Deploy VPC next (foundation for other services)
echo "📦 Deploying VPC module..."
cd ../vpc
terragrunt init
terragrunt apply -auto-approve

# Deploy remaining modules
for MODULE in cloudflare opensearch wazuh selenium; do
  echo "📦 Deploying $MODULE module..."
  cd ../$MODULE
  terragrunt init
  terragrunt apply -auto-approve
done

echo "✅ Infrastructure deployment complete!"
