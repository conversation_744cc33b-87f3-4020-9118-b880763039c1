#!/bin/bash

# Setup script for Cloudflare Zero Trust with OpenSearch
# This script creates the required SSM parameters for zero-trust access

set -e

echo "🔧 Setting up Cloudflare Zero Trust for OpenSearch Access"
echo "========================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to prompt for input
prompt_for_value() {
    local param_name="$1"
    local description="$2"
    local is_secret="$3"
    
    echo -e "\n${BLUE}Setting up: ${param_name}${NC}"
    echo -e "${YELLOW}Description: ${description}${NC}"
    
    if [[ "$is_secret" == "true" ]]; then
        read -s -p "Enter value (hidden): " value
        echo
    else
        read -p "Enter value: " value
    fi
    
    echo "$value"
}

echo -e "\n${GREEN}Step 1: Cloudflare Account Configuration${NC}"
echo "----------------------------------------"

# Cloudflare Account ID
if ! aws ssm get-parameter --name "/infodocs/cloudflare/account_id" >/dev/null 2>&1; then
    ACCOUNT_ID=$(prompt_for_value "/infodocs/cloudflare/account_id" "Your Cloudflare Account ID (found in Cloudflare dashboard sidebar)" "false")
    aws ssm put-parameter \
        --name "/infodocs/cloudflare/account_id" \
        --value "$ACCOUNT_ID" \
        --type "String" \
        --description "Cloudflare Account ID for Zero Trust configuration"
    echo -e "${GREEN}✓ Cloudflare Account ID stored${NC}"
else
    echo -e "${GREEN}✓ Cloudflare Account ID already exists${NC}"
fi

# Cloudflare Team Name
if ! aws ssm get-parameter --name "/infodocs/cloudflare/team_name" >/dev/null 2>&1; then
    TEAM_NAME=$(prompt_for_value "/infodocs/cloudflare/team_name" "Your Cloudflare Zero Trust team name (e.g., infodocs)" "false")
    aws ssm put-parameter \
        --name "/infodocs/cloudflare/team_name" \
        --value "$TEAM_NAME" \
        --type "String" \
        --description "Cloudflare Zero Trust team name"
    echo -e "${GREEN}✓ Cloudflare Team Name stored${NC}"
else
    echo -e "${GREEN}✓ Cloudflare Team Name already exists${NC}"
fi

echo -e "\n${GREEN}Step 2: Google Workspace OAuth Configuration${NC}"
echo "--------------------------------------------"
echo -e "${YELLOW}You need to create OAuth credentials in Google Cloud Console:${NC}"
echo "1. Go to: https://console.cloud.google.com/apis/credentials"
echo "2. Create OAuth 2.0 Client ID"
echo "3. Application type: Web application"
echo "4. Authorized redirect URIs: https://<your-team>.cloudflareaccess.com/cdn-cgi/access/callback"
echo ""

# Google OAuth Client ID
if ! aws ssm get-parameter --name "/infodocs/google/oauth/client_id" >/dev/null 2>&1; then
    CLIENT_ID=$(prompt_for_value "/infodocs/google/oauth/client_id" "Google OAuth Client ID from Google Cloud Console" "true")
    aws ssm put-parameter \
        --name "/infodocs/google/oauth/client_id" \
        --value "$CLIENT_ID" \
        --type "SecureString" \
        --description "Google OAuth Client ID for Workspace SSO"
    echo -e "${GREEN}✓ Google OAuth Client ID stored${NC}"
else
    echo -e "${GREEN}✓ Google OAuth Client ID already exists${NC}"
fi

# Google OAuth Client Secret
if ! aws ssm get-parameter --name "/infodocs/google/oauth/client_secret" >/dev/null 2>&1; then
    CLIENT_SECRET=$(prompt_for_value "/infodocs/google/oauth/client_secret" "Google OAuth Client Secret from Google Cloud Console" "true")
    aws ssm put-parameter \
        --name "/infodocs/google/oauth/client_secret" \
        --value "$CLIENT_SECRET" \
        --type "SecureString" \
        --description "Google OAuth Client Secret for Workspace SSO"
    echo -e "${GREEN}✓ Google OAuth Client Secret stored${NC}"
else
    echo -e "${GREEN}✓ Google OAuth Client Secret already exists${NC}"
fi

# Google Workspace Domain
if ! aws ssm get-parameter --name "/infodocs/google/workspace/domain" >/dev/null 2>&1; then
    WORKSPACE_DOMAIN=$(prompt_for_value "/infodocs/google/workspace/domain" "Your Google Workspace domain (e.g., infodocs.co.za)" "false")
    aws ssm put-parameter \
        --name "/infodocs/google/workspace/domain" \
        --value "$WORKSPACE_DOMAIN" \
        --type "String" \
        --description "Google Workspace domain for SSO authentication"
    echo -e "${GREEN}✓ Google Workspace Domain stored${NC}"
else
    echo -e "${GREEN}✓ Google Workspace Domain already exists${NC}"
fi

echo -e "\n${GREEN}Step 3: Verification${NC}"
echo "-------------------"

echo "Verifying all parameters are set:"
PARAMS=(
    "/infodocs/cloudflare/account_id"
    "/infodocs/cloudflare/team_name"
    "/infodocs/google/oauth/client_id"
    "/infodocs/google/oauth/client_secret"
    "/infodocs/google/workspace/domain"
)

for param in "${PARAMS[@]}"; do
    if aws ssm get-parameter --name "$param" >/dev/null 2>&1; then
        echo -e "${GREEN}✓ $param${NC}"
    else
        echo -e "${RED}✗ $param${NC}"
    fi
done

echo -e "\n${GREEN}🎉 Setup Complete!${NC}"
echo "=================="
echo -e "${YELLOW}Next Steps:${NC}"
echo "1. Deploy the Cloudflare Access module: cd environments/operations/af-south-1/cloudflare/access && terragrunt apply"
echo "2. Deploy the Cloudflare Tunnel module: cd environments/operations/af-south-1/cloudflare/tunnel && terragrunt apply"
echo "3. Configure bastion host with cloudflared daemon"
echo "4. Test access via opensearch.infodocs.co.za"
echo ""
echo -e "${BLUE}Access Flow:${NC}"
echo "WARP Connection → opensearch.infodocs.co.za → Google SSO → OpenSearch"
