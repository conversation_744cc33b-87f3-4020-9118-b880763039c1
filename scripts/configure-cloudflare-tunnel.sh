#!/bin/bash

# Configure Cloudflare Tunnel on bastion host
# This script configures the tunnel after infrastructure deployment

set -e

echo "🔧 Configuring Cloudflare Tunnel on bastion host"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get bastion host IP
echo "Getting bastion host information..."
BASTION_IP=$(cd environments/operations/af-south-1/ec2/bastion && terragrunt output -raw bastion_public_ip)
SSH_KEY_NAME=$(aws ssm get-parameter --name "/infodocs/operations/ec2/key_name" --query 'Parameter.Value' --output text)

if [[ -z "$BASTION_IP" ]]; then
    echo -e "${RED}❌ Could not get bastion host IP${NC}"
    exit 1
fi

echo -e "${GREEN}✓ Bastion host IP: $BASTION_IP${NC}"

# Check if tunnel parameters exist
echo "Checking tunnel configuration..."
TUNNEL_TOKEN=$(aws ssm get-parameter --name "/infodocs/cloudflare/tunnel/token" --with-decryption --query 'Parameter.Value' --output text 2>/dev/null || echo "")
TUNNEL_CONFIG=$(aws ssm get-parameter --name "/infodocs/cloudflare/tunnel/config" --query 'Parameter.Value' --output text 2>/dev/null || echo "")

if [[ -z "$TUNNEL_TOKEN" || -z "$TUNNEL_CONFIG" ]]; then
    echo -e "${RED}❌ Tunnel configuration not found in SSM${NC}"
    echo "Please deploy the Cloudflare tunnel module first:"
    echo "cd environments/operations/af-south-1/cloudflare/tunnel && terragrunt apply"
    exit 1
fi

echo -e "${GREEN}✓ Tunnel configuration found${NC}"

# Create tunnel configuration script
cat > /tmp/setup_tunnel.sh << 'EOF'
#!/bin/bash
set -e

echo "Configuring Cloudflare Tunnel..."

# Get AWS region
AWS_REGION=$(curl -s http://***************/latest/meta-data/placement/region)

# Get tunnel configuration from SSM
TUNNEL_TOKEN=$(aws ssm get-parameter --name "/infodocs/cloudflare/tunnel/token" --with-decryption --query 'Parameter.Value' --output text --region $AWS_REGION)
TUNNEL_CONFIG=$(aws ssm get-parameter --name "/infodocs/cloudflare/tunnel/config" --query 'Parameter.Value' --output text --region $AWS_REGION)

# Create credentials file
echo "Creating tunnel credentials..."
echo "$TUNNEL_TOKEN" | sudo tee /etc/cloudflared/credentials.json > /dev/null
sudo chmod 600 /etc/cloudflared/credentials.json

# Create configuration file
echo "Creating tunnel configuration..."
echo "$TUNNEL_CONFIG" | sudo tee /etc/cloudflared/config.yml > /dev/null
sudo chmod 644 /etc/cloudflared/config.yml

# Set ownership
sudo chown -R cloudflared:cloudflared /etc/cloudflared

# Create systemd service
echo "Creating systemd service..."
sudo tee /etc/systemd/system/cloudflared.service > /dev/null <<EOFSERVICE
[Unit]
Description=Cloudflare Tunnel
After=network.target

[Service]
Type=simple
User=cloudflared
Group=cloudflared
ExecStart=/usr/local/bin/cloudflared tunnel --config /etc/cloudflared/config.yml run
Restart=on-failure
RestartSec=5s

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=cloudflared

[Install]
WantedBy=multi-user.target
EOFSERVICE

# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable cloudflared
sudo systemctl start cloudflared

# Check status
echo "Checking tunnel status..."
sudo systemctl status cloudflared --no-pager

echo "✅ Cloudflare Tunnel configuration complete!"
EOF

# Copy and execute the script on bastion host
echo "Copying configuration script to bastion host..."
scp -i "${SSH_KEY_NAME}.pem" -o StrictHostKeyChecking=no /tmp/setup_tunnel.sh ec2-user@${BASTION_IP}:/tmp/

echo "Executing configuration script on bastion host..."
ssh -i "${SSH_KEY_NAME}.pem" -o StrictHostKeyChecking=no ec2-user@${BASTION_IP} "chmod +x /tmp/setup_tunnel.sh && /tmp/setup_tunnel.sh"

# Clean up
rm /tmp/setup_tunnel.sh

echo -e "\n${GREEN}🎉 Cloudflare Tunnel Configuration Complete!${NC}"
echo "=============================================="
echo -e "${YELLOW}Testing the tunnel:${NC}"
echo "1. Connect to Cloudflare WARP"
echo "2. Navigate to: https://opensearch.infodocs.co.za"
echo "3. Authenticate with your Google Workspace account"
echo "4. Access OpenSearch Dashboards"
echo ""
echo -e "${BLUE}Tunnel Status Commands:${NC}"
echo "ssh -i ${SSH_KEY_NAME}.pem ec2-user@${BASTION_IP} 'sudo systemctl status cloudflared'"
echo "ssh -i ${SSH_KEY_NAME}.pem ec2-user@${BASTION_IP} 'sudo journalctl -u cloudflared -f'"
