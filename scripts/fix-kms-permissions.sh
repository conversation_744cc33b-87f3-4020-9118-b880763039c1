#!/bin/bash

# Fix KMS permissions for CloudWatch Logs to write to encrypted S3 bucket

set -e

echo "🔐 Fixing KMS permissions for CloudWatch Logs"
echo "============================================="

# Configuration
KMS_KEY_ID="a4583e7a-6a62-47fc-a06e-baa59fb092e6"
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
AWS_REGION="af-south-1"

echo "KMS Key ID: $KMS_KEY_ID"
echo "AWS Account: $AWS_ACCOUNT_ID"
echo "Region: $AWS_REGION"
echo ""

# Get current KMS key policy
echo "Getting current KMS key policy..."
CURRENT_POLICY=$(aws kms get-key-policy --key-id "$KMS_KEY_ID" --policy-name default --query Policy --output text)

# Create new policy statement for CloudWatch Logs
CLOUDWATCH_STATEMENT=$(cat << EOF
    {
      "Sid": "AllowCloudWatchLogsExport",
      "Effect": "Allow",
      "Principal": {
        "Service": "logs.$AWS_REGION.amazonaws.com"
      },
      "Action": [
        "kms:Encrypt",
        "kms:Decrypt",
        "kms:ReEncrypt*",
        "kms:GenerateDataKey*",
        "kms:DescribeKey"
      ],
      "Resource": "*",
      "Condition": {
        "StringEquals": {
          "kms:ViaService": "s3.$AWS_REGION.amazonaws.com"
        },
        "StringLike": {
          "kms:EncryptionContext:aws:s3:arn": "arn:aws:s3:::infodocs-operations-opensearch/*"
        }
      }
    }
EOF
)

# Parse current policy and add new statement
echo "Adding CloudWatch Logs permissions to KMS key policy..."

# Create updated policy by adding the new statement
UPDATED_POLICY=$(echo "$CURRENT_POLICY" | jq --argjson new_statement "$CLOUDWATCH_STATEMENT" '
  .Statement += [$new_statement]
')

# Apply the updated policy
echo "$UPDATED_POLICY" | aws kms put-key-policy \
  --key-id "$KMS_KEY_ID" \
  --policy-name default \
  --policy file:///dev/stdin

if [[ $? -eq 0 ]]; then
    echo "✅ KMS key policy updated successfully"
else
    echo "❌ Failed to update KMS key policy"
    exit 1
fi

echo ""
echo "🧪 Testing export with KMS permissions..."

# Test export again
TEST_LOG_GROUP="/aws/lambda/BizPortalScraperV1"
CURRENT_TIME=$(date +%s)000
START_TIME=$((CURRENT_TIME - 86400000))  # 24 hours ago

TASK_ID=$(aws logs create-export-task \
    --log-group-name "$TEST_LOG_GROUP" \
    --from "$START_TIME" \
    --to "$CURRENT_TIME" \
    --destination "infodocs-operations-opensearch" \
    --destination-prefix "kms-test/" \
    --query 'taskId' \
    --output text 2>/dev/null)

if [[ -n "$TASK_ID" && "$TASK_ID" != "None" ]]; then
    echo "✅ Test export with KMS permissions successful: $TASK_ID"
    echo ""
    echo "🎉 KMS permissions are now working!"
    echo "You can now run the full export script:"
    echo "./scripts/export-logs-to-s3.sh"
else
    echo "❌ Test export still failing"
    echo "There may be additional permission issues"
fi

echo ""
echo "Monitor the test export:"
echo "aws logs describe-export-tasks --query 'exportTasks[?taskId==\`$TASK_ID\`]'"
