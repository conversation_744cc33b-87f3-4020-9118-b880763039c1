#!/bin/bash
# Script to create initial secrets in AWS Parameter Store
# Run this BEFORE deploying the infrastructure

set -e

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo "AWS CLI is required but not installed. Please install it first."
    exit 1
fi

# Check if environment is provided
if [ -z "$1" ]; then
    echo "Usage: $0 <environment>"
    echo "Example: $0 dev"
    exit 1
fi

ENV=$1
REGION="af-south-1"

# Get KMS key ID
KMS_KEY_ID=$(aws kms list-aliases --region $REGION --query "Aliases[?AliasName=='alias/infodocs-$ENV-key'].TargetKeyId" --output text)

if [ -z "$KMS_KEY_ID" ]; then
    echo "KMS key not found. Please create it first using the KMS module."
    exit 1
fi

# Generate a secure password
OPENSEARCH_PASSWORD=$(openssl rand -base64 16)

# Create the parameter
aws ssm put-parameter \
    --name "/infodocs/$ENV/opensearch/master_user" \
    --value "admin" \
    --type "SecureString" \
    --key-id "$KMS_KEY_ID" \
    --description "OpenSearch master username" \
    --region $REGION \
    --overwrite

aws ssm put-parameter \
    --name "/infodocs/$ENV/opensearch/master_password" \
    --value "$OPENSEARCH_PASSWORD" \
    --type "SecureString" \
    --key-id "$KMS_KEY_ID" \
    --description "OpenSearch master password" \
    --region $REGION \
    --overwrite

echo "Parameters created successfully."
echo "OpenSearch master password: $OPENSEARCH_PASSWORD"
echo "Please save this password securely and then clear your terminal history."
