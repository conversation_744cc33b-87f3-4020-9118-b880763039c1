#!/bin/bash

# Cloudflare Discovery Script
# Discovers existing Cloudflare resources to avoid conflicts with IaC

set -e

echo "🔍 Discovering existing Cloudflare configuration"
echo "==============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get required parameters
ZONE_ID=$(aws ssm get-parameter --name "/infodocs/cloudflare/zone_id" --query 'Parameter.Value' --output text 2>/dev/null || echo "")
API_TOKEN=$(aws ssm get-parameter --name "/infodocs/cloudflare/api_token" --with-decryption --query 'Parameter.Value' --output text 2>/dev/null || echo "")

if [[ -z "$ZONE_ID" || -z "$API_TOKEN" ]]; then
    echo -e "${RED}❌ Missing Cloudflare credentials in SSM${NC}"
    echo "Please run: ./scripts/setup-cloudflare-zero-trust.sh"
    exit 1
fi

echo -e "${GREEN}✓ Found Cloudflare credentials${NC}"

# Function to make Cloudflare API calls
cf_api() {
    local endpoint="$1"
    curl -s -H "Authorization: Bearer $API_TOKEN" \
         -H "Content-Type: application/json" \
         "https://api.cloudflare.com/v4/$endpoint"
}

echo -e "\n${BLUE}1. Checking DNS Records${NC}"
echo "------------------------"

# Check for opensearch DNS record
OPENSEARCH_RECORD=$(cf_api "zones/$ZONE_ID/dns_records?name=opensearch.infodocs.co.za" | jq -r '.result[0] // empty')

if [[ -n "$OPENSEARCH_RECORD" ]]; then
    RECORD_ID=$(echo "$OPENSEARCH_RECORD" | jq -r '.id')
    RECORD_TYPE=$(echo "$OPENSEARCH_RECORD" | jq -r '.type')
    RECORD_CONTENT=$(echo "$OPENSEARCH_RECORD" | jq -r '.content')
    RECORD_PROXIED=$(echo "$OPENSEARCH_RECORD" | jq -r '.proxied')
    
    echo -e "${YELLOW}⚠️  Found existing opensearch.infodocs.co.za record:${NC}"
    echo "   ID: $RECORD_ID"
    echo "   Type: $RECORD_TYPE"
    echo "   Content: $RECORD_CONTENT"
    echo "   Proxied: $RECORD_PROXIED"
    
    # Save for potential import
    echo "OPENSEARCH_DNS_RECORD_ID=$RECORD_ID" > /tmp/cloudflare_existing.env
else
    echo -e "${GREEN}✓ No existing opensearch DNS record found${NC}"
fi

echo -e "\n${BLUE}2. Checking Access Applications${NC}"
echo "-------------------------------"

# Get account ID
ACCOUNT_ID=$(cf_api "accounts" | jq -r '.result[0].id')
echo "Account ID: $ACCOUNT_ID"

# Check for existing access applications
ACCESS_APPS=$(cf_api "accounts/$ACCOUNT_ID/access/apps" | jq -r '.result[] | select(.domain == "opensearch.infodocs.co.za") | .id' 2>/dev/null || echo "")

if [[ -n "$ACCESS_APPS" ]]; then
    echo -e "${YELLOW}⚠️  Found existing Access Application for opensearch.infodocs.co.za:${NC}"
    echo "   App ID: $ACCESS_APPS"
    echo "OPENSEARCH_ACCESS_APP_ID=$ACCESS_APPS" >> /tmp/cloudflare_existing.env
else
    echo -e "${GREEN}✓ No existing Access Application found${NC}"
fi

echo -e "\n${BLUE}3. Checking Identity Providers${NC}"
echo "-----------------------------"

# Check for Google Workspace identity provider
GOOGLE_IDP=$(cf_api "accounts/$ACCOUNT_ID/access/identity_providers" | jq -r '.result[] | select(.type == "google-apps") | .id' 2>/dev/null || echo "")

if [[ -n "$GOOGLE_IDP" ]]; then
    echo -e "${YELLOW}⚠️  Found existing Google Workspace Identity Provider:${NC}"
    echo "   IDP ID: $GOOGLE_IDP"
    echo "GOOGLE_WORKSPACE_IDP_ID=$GOOGLE_IDP" >> /tmp/cloudflare_existing.env
else
    echo -e "${GREEN}✓ No existing Google Workspace IDP found${NC}"
fi

echo -e "\n${BLUE}4. Checking Tunnels${NC}"
echo "------------------"

# Check for existing tunnels
TUNNELS=$(cf_api "accounts/$ACCOUNT_ID/cfd_tunnel" | jq -r '.result[] | select(.name | contains("opensearch")) | .id' 2>/dev/null || echo "")

if [[ -n "$TUNNELS" ]]; then
    echo -e "${YELLOW}⚠️  Found existing tunnels with 'opensearch' in name:${NC}"
    echo "$TUNNELS" | while read tunnel_id; do
        TUNNEL_INFO=$(cf_api "accounts/$ACCOUNT_ID/cfd_tunnel/$tunnel_id")
        TUNNEL_NAME=$(echo "$TUNNEL_INFO" | jq -r '.result.name')
        echo "   Tunnel: $TUNNEL_NAME (ID: $tunnel_id)"
        echo "EXISTING_TUNNEL_ID=$tunnel_id" >> /tmp/cloudflare_existing.env
    done
else
    echo -e "${GREEN}✓ No existing OpenSearch tunnels found${NC}"
fi

echo -e "\n${BLUE}5. Checking Device Posture Rules${NC}"
echo "--------------------------------"

# Check for WARP device posture rules
WARP_RULES=$(cf_api "accounts/$ACCOUNT_ID/devices/posture" | jq -r '.result[] | select(.type == "warp") | .id' 2>/dev/null || echo "")

if [[ -n "$WARP_RULES" ]]; then
    echo -e "${YELLOW}⚠️  Found existing WARP device posture rules:${NC}"
    echo "$WARP_RULES" | while read rule_id; do
        RULE_INFO=$(cf_api "accounts/$ACCOUNT_ID/devices/posture/$rule_id")
        RULE_NAME=$(echo "$RULE_INFO" | jq -r '.result.name')
        echo "   Rule: $RULE_NAME (ID: $rule_id)"
        echo "EXISTING_WARP_RULE_ID=$rule_id" >> /tmp/cloudflare_existing.env
    done
else
    echo -e "${GREEN}✓ No existing WARP device posture rules found${NC}"
fi

echo -e "\n${GREEN}🎯 Discovery Complete!${NC}"
echo "====================="

if [[ -f /tmp/cloudflare_existing.env ]]; then
    echo -e "${YELLOW}⚠️  Found existing resources that may conflict:${NC}"
    cat /tmp/cloudflare_existing.env
    echo ""
    echo -e "${BLUE}Next Steps:${NC}"
    echo "1. Review the existing resources above"
    echo "2. Run: ./scripts/cloudflare-import.sh (to import existing resources)"
    echo "3. OR manually delete conflicting resources in Cloudflare dashboard"
    echo "4. Then proceed with IaC deployment"
else
    echo -e "${GREEN}✅ No conflicts found! Safe to proceed with IaC deployment.${NC}"
    echo ""
    echo -e "${BLUE}Next Steps:${NC}"
    echo "1. Run: ./scripts/setup-cloudflare-zero-trust.sh"
    echo "2. Deploy the modules as documented"
fi

echo ""
echo -e "${YELLOW}💡 Recommendation:${NC}"
echo "If you have working manual configuration, consider importing it rather than recreating."
