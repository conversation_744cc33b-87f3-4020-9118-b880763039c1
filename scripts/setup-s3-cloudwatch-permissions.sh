#!/bin/bash

# Setup S3 Bucket Policy for CloudWatch Logs Export
# This allows CloudWatch Logs to write export data to S3

set -e

echo "🔧 Setting up S3 permissions for CloudWatch Logs export"
echo "====================================================="

# Configuration
S3_BUCKET="infodocs-operations-opensearch"
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
AWS_REGION="af-south-1"

echo "S3 Bucket: $S3_BUCKET"
echo "AWS Account: $AWS_ACCOUNT_ID"
echo "Region: $AWS_REGION"
echo ""

# Create bucket policy for CloudWatch Logs
BUCKET_POLICY=$(cat << EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "logs.$AWS_REGION.amazonaws.com"
      },
      "Action": "s3:GetBucketAcl",
      "Resource": "arn:aws:s3:::$S3_BUCKET",
      "Condition": {
        "StringEquals": {
          "AWS:SourceAccount": "$AWS_ACCOUNT_ID"
        }
      }
    },
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "logs.$AWS_REGION.amazonaws.com"
      },
      "Action": "s3:PutObject",
      "Resource": "arn:aws:s3:::$S3_BUCKET/*",
      "Condition": {
        "StringEquals": {
          "s3:x-amz-acl": "bucket-owner-full-control",
          "AWS:SourceAccount": "$AWS_ACCOUNT_ID"
        }
      }
    }
  ]
}
EOF
)

echo "Creating S3 bucket policy..."
echo "$BUCKET_POLICY" | aws s3api put-bucket-policy --bucket "$S3_BUCKET" --policy file:///dev/stdin

if [[ $? -eq 0 ]]; then
    echo "✅ S3 bucket policy created successfully"
else
    echo "❌ Failed to create S3 bucket policy"
    exit 1
fi

echo ""
echo "🧪 Testing export permissions..."

# Test with a small export
TEST_LOG_GROUP="/aws/lambda/BizPortalScraperV1"
CURRENT_TIME=$(date +%s)000
START_TIME=$((CURRENT_TIME - ********))  # 24 hours ago

echo "Testing export for: $TEST_LOG_GROUP"
echo "Time range: Last 24 hours"

TASK_ID=$(aws logs create-export-task \
    --log-group-name "$TEST_LOG_GROUP" \
    --from "$START_TIME" \
    --to "$CURRENT_TIME" \
    --destination "$S3_BUCKET" \
    --destination-prefix "test-export/" \
    --query 'taskId' \
    --output text 2>/dev/null)

if [[ -n "$TASK_ID" && "$TASK_ID" != "None" ]]; then
    echo "✅ Test export task created: $TASK_ID"
    echo ""
    echo "Monitor progress with:"
    echo "aws logs describe-export-tasks --query 'exportTasks[?taskId==\`$TASK_ID\`]'"
else
    echo "❌ Test export failed"
    echo ""
    echo "Check CloudWatch Logs permissions and try again"
fi

echo ""
echo "🎯 Next steps:"
echo "1. Wait 5-10 minutes for test export to complete"
echo "2. Check S3: aws s3 ls s3://$S3_BUCKET/test-export/ --recursive"
echo "3. If successful, run the full export script again"
