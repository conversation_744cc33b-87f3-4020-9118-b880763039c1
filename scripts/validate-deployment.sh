#!/bin/bash

# Deployment Validation Script for InfoDocs Infrastructure
# This script validates that all modules are ready for deployment

set -e

ENVIRONMENT=${1:-dev}
REGION=${2:-af-south-1}

echo "🔍 Validating InfoDocs Infrastructure Deployment"
echo "=============================================="
echo "Environment: $ENVIRONMENT"
echo "Region: $REGION"
echo ""

# Define module deployment order
MODULES=("kms" "vpc" "ssm" "cloudflare" "opensearch" "wazuh" "selenium")

# Function to check if directory exists
check_directory() {
    local module="$1"
    local path="environments/$ENVIRONMENT/$REGION/$module"

    if [ -d "$path" ]; then
        echo "✅ $module: Directory exists"
        return 0
    else
        echo "❌ $module: Directory missing at $path"
        return 1
    fi
}

# Function to check if terragrunt.hcl exists
check_terragrunt_config() {
    local module="$1"
    local path="environments/$ENVIRONMENT/$REGION/$module/terragrunt.hcl"

    if [ -f "$path" ]; then
        echo "✅ $module: terragrunt.hcl exists"
        return 0
    else
        echo "❌ $module: terragrunt.hcl missing at $path"
        return 1
    fi
}

# Function to validate terragrunt configuration
validate_terragrunt() {
    local module="$1"
    local path="environments/$ENVIRONMENT/$REGION/$module"

    echo "🔍 Validating $module configuration..."
    cd "$path"

    if terragrunt validate --terragrunt-non-interactive > /dev/null 2>&1; then
        echo "✅ $module: Configuration valid"
        cd - > /dev/null
        return 0
    else
        echo "❌ $module: Configuration validation failed"
        cd - > /dev/null
        return 1
    fi
}

# Function to check prerequisites
check_prerequisites() {
    echo "🔍 Checking prerequisites..."

    # Check OpenTofu
    if command -v tofu &> /dev/null; then
        local tofu_version=$(tofu version | head -n1 | cut -d' ' -f2)
        echo "✅ OpenTofu: $tofu_version"
    else
        echo "❌ OpenTofu: Not installed"
        return 1
    fi

    # Check Terragrunt
    if command -v terragrunt &> /dev/null; then
        local tg_version=$(terragrunt --version | head -n1)
        echo "✅ Terragrunt: $tg_version"
    else
        echo "❌ Terragrunt: Not installed"
        return 1
    fi

    # Check AWS CLI
    if command -v aws &> /dev/null; then
        echo "✅ AWS CLI: Installed"

        # Check AWS credentials
        if aws sts get-caller-identity > /dev/null 2>&1; then
            echo "✅ AWS Credentials: Valid"
        else
            echo "❌ AWS Credentials: Invalid or not configured"
            return 1
        fi
    else
        echo "❌ AWS CLI: Not installed"
        return 1
    fi

    echo ""
}

# Main validation
echo "Starting validation..."
echo ""

# Check prerequisites
if ! check_prerequisites; then
    echo "❌ Prerequisites check failed"
    exit 1
fi

# Validate each module
validation_failed=false

for module in "${MODULES[@]}"; do
    echo "📦 Validating module: $module"

    if ! check_directory "$module"; then
        validation_failed=true
        continue
    fi

    if ! check_terragrunt_config "$module"; then
        validation_failed=true
        continue
    fi

    # Skip validation for modules that don't exist yet
    if [ ! -d "environments/$ENVIRONMENT/$REGION/$module" ]; then
        echo "⚠️  $module: Skipping validation (directory doesn't exist)"
        continue
    fi

    echo ""
done

echo ""
echo "📋 Validation Summary"
echo "===================="

if [ "$validation_failed" = true ]; then
    echo "❌ Validation failed - please fix the issues above before deployment"
    exit 1
else
    echo "✅ All validations passed!"
    echo ""
    echo "🚀 Ready for deployment!"
    echo ""
    echo "Next steps:"
    echo "1. Clear cache: ./scripts/clear-cache.sh"
    echo "2. Deploy modules in order:"
    for module in "${MODULES[@]}"; do
        echo "   - cd environments/$ENVIRONMENT/$REGION/$module && terragrunt plan && terragrunt apply"
    done
fi
