#!/bin/bash

# Simple Historical Log Import to OpenSearch
# Uses CloudWatch Logs Insights to query and export data

set -e

echo "📊 Simple Historical Log Import to OpenSearch"
echo "============================================="

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

# Configuration
LAMBDA_FUNCTION="infodocs-operations-opensearch-log-shipper"
HOURS_BACK=24  # Start with last 24 hours

# High-value log groups
LOG_GROUPS=(
    "/aws/lambda/BizPortalScraperV1"
    "/aws/lambda/Regbot2"
    "/aws/lambda/ValidationLambda"
)

# Calculate time range (last 24 hours)
END_TIME=$(date +%s)
START_TIME=$((END_TIME - (HOURS_BACK * 3600)))

echo -e "${BLUE}Importing logs from last $HOURS_BACK hours${NC}"
echo -e "${YELLOW}Start: $(date -r $START_TIME)${NC}"
echo -e "${YELLOW}End: $(date -r $END_TIME)${NC}"
echo ""

# Function to check if log group has recent data
check_log_group() {
    local log_group="$1"
    
    echo -e "${BLUE}Checking: $log_group${NC}"
    
    # Check if log group exists
    if ! aws logs describe-log-groups --log-group-name-prefix "$log_group" --query 'logGroups[0].logGroupName' --output text 2>/dev/null | grep -q "$log_group"; then
        echo -e "${YELLOW}⚠️  Log group not found${NC}"
        return 1
    fi
    
    # Check for recent log streams
    local recent_streams=$(aws logs describe-log-streams \
        --log-group-name "$log_group" \
        --order-by LastEventTime \
        --descending \
        --max-items 5 \
        --query "logStreams[?lastEventTime >= \`$((START_TIME * 1000))\`].logStreamName" \
        --output text 2>/dev/null)
    
    if [[ -n "$recent_streams" && "$recent_streams" != "None" ]]; then
        echo -e "${GREEN}✓ Found recent activity${NC}"
        echo "Recent streams: $recent_streams"
        return 0
    else
        echo -e "${YELLOW}⚠️  No recent activity in specified time range${NC}"
        return 1
    fi
}

# Function to trigger lambda with sample data
trigger_sample_processing() {
    local log_group="$1"
    
    echo -e "${BLUE}Triggering sample processing for: $log_group${NC}"
    
    # Create a simple test log event
    local test_event=$(cat << EOF
{
    "awslogs": {
        "data": "$(echo '{
            "messageType": "DATA_MESSAGE",
            "owner": "513036964009",
            "logGroup": "'$log_group'",
            "logStream": "test-stream",
            "subscriptionFilters": ["historical-import"],
            "logEvents": [
                {
                    "id": "test-event-1",
                    "timestamp": '$(date +%s)000',
                    "message": "Test historical import from '$log_group'"
                }
            ]
        }' | gzip | base64 -w 0)"
    }
}
EOF
    )
    
    # Invoke lambda with test data
    if aws lambda invoke \
        --function-name "$LAMBDA_FUNCTION" \
        --payload "$test_event" \
        --cli-binary-format raw-in-base64-out \
        /tmp/lambda-response.json >/dev/null 2>&1; then
        echo -e "${GREEN}✓ Successfully triggered processing${NC}"
        return 0
    else
        echo -e "${RED}✗ Failed to trigger processing${NC}"
        return 1
    fi
}

# Function to use CloudWatch Logs Insights for data export
export_with_insights() {
    local log_group="$1"
    
    echo -e "${BLUE}Using CloudWatch Logs Insights for: $log_group${NC}"
    
    # Start a CloudWatch Logs Insights query
    local query_id=$(aws logs start-query \
        --log-group-name "$log_group" \
        --start-time "$START_TIME" \
        --end-time "$END_TIME" \
        --query-string 'fields @timestamp, @message | limit 1000' \
        --query 'queryId' \
        --output text 2>/dev/null)
    
    if [[ -n "$query_id" && "$query_id" != "None" ]]; then
        echo "Query ID: $query_id"
        echo "Waiting for query to complete..."
        
        # Wait for query to complete (max 30 seconds)
        local attempts=0
        while [[ $attempts -lt 30 ]]; do
            local status=$(aws logs get-query-results \
                --query-id "$query_id" \
                --query 'status' \
                --output text 2>/dev/null)
            
            if [[ "$status" == "Complete" ]]; then
                local result_count=$(aws logs get-query-results \
                    --query-id "$query_id" \
                    --query 'length(results)' \
                    --output text 2>/dev/null)
                echo -e "${GREEN}✓ Query completed: $result_count results${NC}"
                return 0
            elif [[ "$status" == "Failed" ]]; then
                echo -e "${RED}✗ Query failed${NC}"
                return 1
            fi
            
            sleep 2
            attempts=$((attempts + 1))
        done
        
        echo -e "${YELLOW}⚠️  Query timed out${NC}"
        return 1
    else
        echo -e "${RED}✗ Failed to start query${NC}"
        return 1
    fi
}

echo -e "${YELLOW}Checking log groups for recent activity...${NC}"
echo ""

# Check each log group
active_groups=()
for log_group in "${LOG_GROUPS[@]}"; do
    if check_log_group "$log_group"; then
        active_groups+=("$log_group")
    fi
    echo ""
done

if [[ ${#active_groups[@]} -eq 0 ]]; then
    echo -e "${YELLOW}No log groups have recent activity in the last $HOURS_BACK hours.${NC}"
    echo ""
    echo -e "${BLUE}Options:${NC}"
    echo "1. Wait for your Lambda functions to run naturally"
    echo "2. Manually trigger one of your Lambda functions"
    echo "3. Check OpenSearch for any existing data"
    echo ""
    echo "Go to: https://localhost:8443/_dashboards"
    exit 0
fi

echo -e "${GREEN}Found ${#active_groups[@]} log groups with recent activity!${NC}"
echo ""

# Process active groups
for log_group in "${active_groups[@]}"; do
    echo -e "${BLUE}Processing: $log_group${NC}"
    
    # Try the insights export method
    if export_with_insights "$log_group"; then
        echo -e "${GREEN}✓ Exported data from $log_group${NC}"
    else
        echo -e "${YELLOW}⚠️  Export failed, trying sample trigger...${NC}"
        trigger_sample_processing "$log_group"
    fi
    echo ""
done

echo -e "\n${GREEN}🎉 Historical import process complete!${NC}"
echo "============================================"
echo ""
echo -e "${BLUE}Next steps:${NC}"
echo "1. Wait 5-10 minutes for data to appear in OpenSearch"
echo "2. Check OpenSearch: https://localhost:8443/_dashboards"
echo "3. Go to Stack Management → Index Management"
echo "4. Look for new indices with today's date"
echo ""
echo -e "${YELLOW}If no data appears, your Lambda functions may not have run recently.${NC}"
echo "The subscription filters will capture data when they next execute."
