#!/bin/bash

echo "🔍 Auditing Infrastructure for Dummy Data and Placeholders"
echo "=========================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

ISSUES_FOUND=0

echo -e "${BLUE}🔍 Checking for dummy IP addresses...${NC}"
DUMMY_IPS=$(grep -r "192\.0\.2\." environments/ 2>/dev/null || true)
if [[ -n "$DUMMY_IPS" ]]; then
    echo -e "${RED}❌ Found dummy IP addresses (192.0.2.x):${NC}"
    echo "$DUMMY_IPS"
    ISSUES_FOUND=$((ISSUES_FOUND + 1))
else
    echo -e "${GREEN}✅ No dummy IP addresses found${NC}"
fi

echo -e "\n${BLUE}🔍 Checking for placeholder values...${NC}"
PLACEHOLDERS=$(grep -r "REPLACE_WITH\|YOUR_.*_HERE\|PLACEHOLDER\|example\.com" environments/ 2>/dev/null || true)
if [[ -n "$PLACEHOLDERS" ]]; then
    echo -e "${RED}❌ Found placeholder values:${NC}"
    echo "$PLACEHOLDERS"
    ISSUES_FOUND=$((ISSUES_FOUND + 1))
else
    echo -e "${GREEN}✅ No placeholder values found${NC}"
fi

echo -e "\n${BLUE}🔍 Checking for hardcoded passwords...${NC}"
HARDCODED_PASSWORDS=$(grep -r -i "password.*=" environments/ | grep -v "ssm\|parameter\|variable\|output" | grep -v ".tf:.*#" 2>/dev/null || true)
if [[ -n "$HARDCODED_PASSWORDS" ]]; then
    echo -e "${RED}❌ Found potential hardcoded passwords:${NC}"
    echo "$HARDCODED_PASSWORDS"
    ISSUES_FOUND=$((ISSUES_FOUND + 1))
else
    echo -e "${GREEN}✅ No hardcoded passwords found${NC}"
fi

echo -e "\n${BLUE}🔍 Checking for hardcoded secrets...${NC}"
HARDCODED_SECRETS=$(grep -r -i "secret.*=\|api_key.*=\|token.*=" environments/ | grep -v "ssm\|parameter\|variable\|output\|path\|data\." | grep -v ".tf:.*#" 2>/dev/null || true)
if [[ -n "$HARDCODED_SECRETS" ]]; then
    echo -e "${RED}❌ Found potential hardcoded secrets:${NC}"
    echo "$HARDCODED_SECRETS"
    ISSUES_FOUND=$((ISSUES_FOUND + 1))
else
    echo -e "${GREEN}✅ No hardcoded secrets found${NC}"
fi

echo -e "\n${BLUE}🔍 Checking for hardcoded IDs and sensitive values...${NC}"
HARDCODED_IDS=$(grep -r -E "[a-f0-9]{32}|[A-Z0-9]{20,}" environments/ | grep -v "mock\|example\|ssm\|parameter\|variable\|output\|data\." | grep -v ".tf:.*#" 2>/dev/null || true)
if [[ -n "$HARDCODED_IDS" ]]; then
    echo -e "${RED}❌ Found potential hardcoded IDs or sensitive values:${NC}"
    echo "$HARDCODED_IDS"
    ISSUES_FOUND=$((ISSUES_FOUND + 1))
else
    echo -e "${GREEN}✅ No hardcoded IDs found${NC}"
fi

echo -e "\n${BLUE}🔍 Checking for hardcoded AWS account IDs...${NC}"
HARDCODED_ACCOUNTS=$(grep -r -E "arn:aws:iam::[0-9]{12}:|[0-9]{12}" environments/ --include="*.hcl" | grep -v "mock\|example\|get_aws_account_id\|ssm\|parameter\|cache" 2>/dev/null || true)
if [[ -n "$HARDCODED_ACCOUNTS" ]]; then
    echo -e "${RED}❌ Found hardcoded AWS account IDs or ARNs:${NC}"
    echo "$HARDCODED_ACCOUNTS"
    ISSUES_FOUND=$((ISSUES_FOUND + 1))
else
    echo -e "${GREEN}✅ No hardcoded AWS account IDs found${NC}"
fi

echo -e "\n${BLUE}🔍 Checking for test/dummy domains...${NC}"
DUMMY_DOMAINS=$(grep -r "example\.com\|test\.com\|dummy\." environments/ 2>/dev/null || true)
if [[ -n "$DUMMY_DOMAINS" ]]; then
    echo -e "${RED}❌ Found dummy domains:${NC}"
    echo "$DUMMY_DOMAINS"
    ISSUES_FOUND=$((ISSUES_FOUND + 1))
else
    echo -e "${GREEN}✅ No dummy domains found${NC}"
fi

echo -e "\n${BLUE}🔍 Checking SSM parameter usage...${NC}"
echo "Verifying all modules use SSM parameters correctly..."

# Check if modules are using SSM parameters
SSM_USAGE=$(grep -r "/infodocs/" environments/ 2>/dev/null | wc -l)
echo -e "${GREEN}✅ Found $SSM_USAGE references to /infodocs/ SSM parameters${NC}"

echo -e "\n${BLUE}🔍 Checking for missing mock outputs...${NC}"
MISSING_MOCKS=$(find environments/ -name "terragrunt.hcl" -exec grep -l "dependency" {} \; | xargs grep -L "mock_outputs" 2>/dev/null || true)
if [[ -n "$MISSING_MOCKS" ]]; then
    echo -e "${YELLOW}⚠️  Files with dependencies but no mock outputs:${NC}"
    echo "$MISSING_MOCKS"
else
    echo -e "${GREEN}✅ All dependencies have mock outputs${NC}"
fi

echo -e "\n${BLUE}📊 Summary${NC}"
echo "=========="
if [[ $ISSUES_FOUND -eq 0 ]]; then
    echo -e "${GREEN}🎉 No critical issues found!${NC}"
    echo -e "${GREEN}✅ All modules appear to be using SSM parameters correctly${NC}"
    echo -e "${GREEN}✅ No dummy data or placeholders detected${NC}"
else
    echo -e "${RED}⚠️  Found $ISSUES_FOUND issue(s) that need attention${NC}"
    echo ""
    echo -e "${YELLOW}🔧 Action Items:${NC}"
    echo "1. Replace all placeholder values with actual values"
    echo "2. Ensure all secrets use SSM Parameter Store"
    echo "3. Update dummy IP addresses with actual server IPs"
    echo "4. Run this script again after fixes"
fi

echo -e "\n${BLUE}💡 Next Steps:${NC}"
echo "1. Run: ./scripts/get-server-ip.sh to find your actual server IP"
echo "2. Update Cloudflare configuration with real IP address"
echo "3. Verify all SSM parameters are created: ./scripts/get-login-credentials.sh"
echo "4. Test modules: ./test_all_environments.sh"

exit $ISSUES_FOUND
