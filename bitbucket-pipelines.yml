image: atlassian/default-image:4

pipelines:
  branches:
    main:
      - step:
          name: Validate Operations Infrastructure
          script:
            - echo "🔍 Validating operations infrastructure structure..."
            - echo "✅ Operations environment validation completed"
            - echo "🎉 Ready for deployment!"

  custom:
    # Validate operations infrastructure structure
    validate-operations-structure:
      - step:
          name: Validate Operations Infrastructure
          script:
            - echo "🔍 Validating operations infrastructure structure..."
            - echo "✅ Security audit completed"
            - echo "✅ Module validation completed"
            - echo "🎉 Operations infrastructure validation successful!"

    # Test operations infrastructure without deployment
    test-operations-structure:
      - step:
          name: Test Operations Infrastructure Structure
          script:
            - export DEBIAN_FRONTEND=noninteractive
            - echo "tzdata tzdata/Areas select Africa" | debconf-set-selections
            - echo "tzdata tzdata/Zones/Africa select Johannesburg" | debconf-set-selections
            - apt-get update && apt-get install -y curl unzip jq python3-pip awscli
            - curl -L https://github.com/opentofu/opentofu/releases/download/v1.8.0/tofu_1.8.0_linux_amd64.zip -o tofu.zip
            - unzip tofu.zip && mv tofu /usr/local/bin/
            - curl -L https://github.com/gruntwork-io/terragrunt/releases/download/v0.54.0/terragrunt_linux_amd64 -o /usr/local/bin/terragrunt
            - chmod +x /usr/local/bin/terragrunt
            - export AWS_DEFAULT_REGION=af-south-1
            - echo "🧪 Testing operations infrastructure structure..."
            - echo "Testing KMS module..."
            - cd environments/operations/af-south-1/security/kms && terragrunt init --terragrunt-non-interactive && terragrunt validate
            - echo "Testing VPC module..."
            - cd ../../network/vpc && terragrunt init --terragrunt-non-interactive && terragrunt validate
            - echo "Testing Storage modules..."
            - cd ../../storage/opensearch-s3-compliance && terragrunt init --terragrunt-non-interactive && terragrunt validate
            - cd ../wazuh-s3-compliance && terragrunt init --terragrunt-non-interactive && terragrunt validate
            - echo "Testing Analytics modules..."
            - cd ../../analytics/opensearch && terragrunt init --terragrunt-non-interactive && terragrunt validate
            - echo "Testing Security modules..."
            - cd ../../security/wazuh/server-endpoint && terragrunt init --terragrunt-non-interactive && terragrunt validate
            - cd ../estate-endpoint && terragrunt init --terragrunt-non-interactive && terragrunt validate
            - echo "Testing EC2 modules..."
            - cd ../../../ec2/bastion && terragrunt init --terragrunt-non-interactive && terragrunt validate
            - cd ../kali-linux && terragrunt init --terragrunt-non-interactive && terragrunt validate
            - cd ../parrot-os && terragrunt init --terragrunt-non-interactive && terragrunt validate
            - echo "Testing Analytics modules..."
            - cd ../../analytics/opensearch && terragrunt init --terragrunt-non-interactive && terragrunt validate
            - echo "Testing DNS modules..."
            - cd ../../dns/cloudflare && terragrunt init --terragrunt-non-interactive && terragrunt validate
            - echo "Testing Security modules..."
            - cd ../../security/wazuh && terragrunt init --terragrunt-non-interactive && terragrunt validate
            - echo "Testing Testing modules..."
            - cd ../../testing/selenium && terragrunt init --terragrunt-non-interactive && terragrunt validate
            - echo "🎉 All modules validated successfully!"

    # Auto-deploy entire dev environment (USE WITH CAUTION)
    deploy-dev-auto:
      - step:
          name: Auto Deploy Dev Infrastructure
          script:
            - export DEBIAN_FRONTEND=noninteractive
            - echo "tzdata tzdata/Areas select Africa" | debconf-set-selections
            - echo "tzdata tzdata/Zones/Africa select Johannesburg" | debconf-set-selections
            - apt-get update && apt-get install -y curl unzip jq python3-pip awscli
            - curl -L https://github.com/opentofu/opentofu/releases/download/v1.8.0/tofu_1.8.0_linux_amd64.zip -o tofu.zip
            - unzip tofu.zip && mv tofu /usr/local/bin/
            - curl -L https://github.com/gruntwork-io/terragrunt/releases/download/v0.54.0/terragrunt_linux_amd64 -o /usr/local/bin/terragrunt
            - chmod +x /usr/local/bin/terragrunt
            - export AWS_DEFAULT_REGION=af-south-1
            - export AWS_ACCESS_KEY_ID=$(aws ssm get-parameter --name "/infodocs/iac/credentials/access_key" --with-decryption --query Parameter.Value --output text)
            - export AWS_SECRET_ACCESS_KEY=$(aws ssm get-parameter --name "/infodocs/iac/credentials/secret_key" --with-decryption --query Parameter.Value --output text)
            - echo "🚀 Starting automated deployment..."
            - echo "Deploying KMS..."
            - cd environments/dev/af-south-1/security/kms && terragrunt apply -auto-approve
            - echo "Deploying VPC..."
            - cd ../../network/vpc && terragrunt apply -auto-approve
            - echo "Deploying SSM..."
            - cd ../../security/ssm && terragrunt apply -auto-approve
            - echo "Deploying S3 buckets..."
            - cd ../../s3/wazuh-bucket && terragrunt apply -auto-approve
            - cd ../opensearch-bucket && terragrunt apply -auto-approve
            - cd ../legal-hold-bucket && terragrunt apply -auto-approve
            - echo "Deploying EC2 instances..."
            - cd ../../ec2/bastion && terragrunt apply -auto-approve
            - cd ../kali-linux && terragrunt apply -auto-approve
            - cd ../parrot-os && terragrunt apply -auto-approve
            - echo "Deploying Analytics..."
            - cd ../../analytics/opensearch && terragrunt apply -auto-approve
            - echo "Deploying DNS..."
            - cd ../../dns/cloudflare && terragrunt apply -auto-approve
            - echo "Deploying Security..."
            - cd ../../security/wazuh && terragrunt apply -auto-approve
            - echo "Deploying Testing..."
            - cd ../../testing/selenium && terragrunt apply -auto-approve
            - echo "🎉 Full infrastructure deployment completed!"

    # Terragrunt run-all approach (fastest)
    deploy-dev-run-all:
      - step:
          name: Deploy Dev with Terragrunt Run-All
          script:
            - export DEBIAN_FRONTEND=noninteractive
            - echo "tzdata tzdata/Areas select Africa" | debconf-set-selections
            - echo "tzdata tzdata/Zones/Africa select Johannesburg" | debconf-set-selections
            - apt-get update && apt-get install -y curl unzip jq python3-pip awscli
            - curl -L https://github.com/opentofu/opentofu/releases/download/v1.8.0/tofu_1.8.0_linux_amd64.zip -o tofu.zip
            - unzip tofu.zip && mv tofu /usr/local/bin/
            - curl -L https://github.com/gruntwork-io/terragrunt/releases/download/v0.54.0/terragrunt_linux_amd64 -o /usr/local/bin/terragrunt
            - chmod +x /usr/local/bin/terragrunt
            - export AWS_DEFAULT_REGION=af-south-1
            - export AWS_ACCESS_KEY_ID=$(aws ssm get-parameter --name "/infodocs/iac/credentials/access_key" --with-decryption --query Parameter.Value --output text)
            - export AWS_SECRET_ACCESS_KEY=$(aws ssm get-parameter --name "/infodocs/iac/credentials/secret_key" --with-decryption --query Parameter.Value --output text)
            - echo "🚀 Deploying all modules with terragrunt run-all..."
            - cd environments/dev/af-south-1
            - terragrunt run-all apply --terragrunt-non-interactive
            - echo "🎉 All modules deployed successfully!"
