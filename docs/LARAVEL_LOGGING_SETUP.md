# Laravel Application Logging Setup

This guide configures your Laravel applications to send logs and metrics to your monitoring stack.

## 🎯 **Data Flow**
```
<PERSON><PERSON> App → CloudWatch Logs → Lambda → OpenSearch → Grafana
Laravel App → CloudWatch Metrics → Grafana
```

## 📋 **Step 1: Install AWS SDK in Laravel**

```bash
composer require aws/aws-sdk-php
composer require monolog/monolog
```

## 📋 **Step 2: Configure Laravel Logging**

### **config/logging.php**
```php
<?php

return [
    'default' => env('LOG_CHANNEL', 'stack'),

    'channels' => [
        'stack' => [
            'driver' => 'stack',
            'channels' => ['cloudwatch', 'daily'],
            'ignore_exceptions' => false,
        ],

        // CloudWatch Logs Channel
        'cloudwatch' => [
            'driver' => 'custom',
            'via' => App\Logging\CloudWatchLoggerFactory::class,
            'level' => 'debug',
            'name' => env('CLOUDWATCH_LOG_GROUP', '/infodocs/laravel/application'),
            'stream' => env('CLOUDWATCH_LOG_STREAM', 'laravel-app'),
            'region' => env('AWS_DEFAULT_REGION', 'af-south-1'),
        ],

        // Audit Logs (separate channel)
        'audit' => [
            'driver' => 'custom',
            'via' => App\Logging\CloudWatchLoggerFactory::class,
            'level' => 'info',
            'name' => '/infodocs/laravel/audit',
            'stream' => 'audit-logs',
            'region' => env('AWS_DEFAULT_REGION', 'af-south-1'),
        ],

        'daily' => [
            'driver' => 'daily',
            'path' => storage_path('logs/laravel.log'),
            'level' => 'debug',
            'days' => 14,
        ],
    ],
];
```

## 📋 **Step 3: Create CloudWatch Logger Factory**

### **app/Logging/CloudWatchLoggerFactory.php**
```php
<?php

namespace App\Logging;

use Aws\CloudWatchLogs\CloudWatchLogsClient;
use Maxbanton\Cwh\Handler\CloudWatch;
use Monolog\Logger;
use Monolog\Formatter\JsonFormatter;

class CloudWatchLoggerFactory
{
    public function __invoke(array $config)
    {
        $client = new CloudWatchLogsClient([
            'region' => $config['region'],
            'version' => 'latest',
            'credentials' => [
                'key' => env('AWS_ACCESS_KEY_ID'),
                'secret' => env('AWS_SECRET_ACCESS_KEY'),
            ]
        ]);

        $handler = new CloudWatch(
            $client,
            $config['name'],
            $config['stream'],
            14, // Retention days
            10000, // Batch size
            ['application' => env('APP_NAME', 'laravel')]
        );

        $handler->setFormatter(new JsonFormatter());

        $logger = new Logger($config['name']);
        $logger->pushHandler($handler);

        return $logger;
    }
}
```

## 📋 **Step 4: Environment Configuration**

### **.env additions**
```env
# CloudWatch Logging
CLOUDWATCH_LOG_GROUP=/infodocs/laravel/application
CLOUDWATCH_LOG_STREAM=laravel-app
AWS_DEFAULT_REGION=af-south-1

# Use your existing AWS credentials or IAM role
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
```

## 📋 **Step 5: Custom Metrics Middleware**

### **app/Http/Middleware/MetricsMiddleware.php**
```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Aws\CloudWatch\CloudWatchClient;

class MetricsMiddleware
{
    private $cloudWatch;

    public function __construct()
    {
        $this->cloudWatch = new CloudWatchClient([
            'region' => env('AWS_DEFAULT_REGION', 'af-south-1'),
            'version' => 'latest'
        ]);
    }

    public function handle(Request $request, Closure $next)
    {
        $startTime = microtime(true);
        
        $response = $next($request);
        
        $duration = (microtime(true) - $startTime) * 1000; // Convert to milliseconds
        
        // Send metrics to CloudWatch
        $this->sendMetrics([
            [
                'MetricName' => 'ResponseTime',
                'Value' => $duration,
                'Unit' => 'Milliseconds',
                'Dimensions' => [
                    [
                        'Name' => 'Route',
                        'Value' => $request->route() ? $request->route()->getName() : 'unknown'
                    ],
                    [
                        'Name' => 'Method',
                        'Value' => $request->method()
                    ]
                ]
            ],
            [
                'MetricName' => 'RequestCount',
                'Value' => 1,
                'Unit' => 'Count',
                'Dimensions' => [
                    [
                        'Name' => 'StatusCode',
                        'Value' => (string) $response->getStatusCode()
                    ]
                ]
            ]
        ]);

        return $response;
    }

    private function sendMetrics(array $metrics)
    {
        try {
            $this->cloudWatch->putMetricData([
                'Namespace' => 'InfoDocs/Laravel',
                'MetricData' => $metrics
            ]);
        } catch (\Exception $e) {
            \Log::error('Failed to send metrics to CloudWatch: ' . $e->getMessage());
        }
    }
}
```

## 📋 **Step 6: Register Middleware**

### **app/Http/Kernel.php**
```php
protected $middleware = [
    // ... other middleware
    \App\Http\Middleware\MetricsMiddleware::class,
];
```

## 📋 **Step 7: Usage Examples**

### **Application Logging**
```php
// In your controllers/services
Log::info('User logged in', [
    'user_id' => $user->id,
    'ip_address' => request()->ip(),
    'user_agent' => request()->userAgent()
]);

Log::error('Payment failed', [
    'user_id' => $user->id,
    'amount' => $amount,
    'error' => $exception->getMessage()
]);
```

### **Audit Logging**
```php
// For audit trails
Log::channel('audit')->info('User action', [
    'action' => 'document_created',
    'user_id' => auth()->id(),
    'resource_id' => $document->id,
    'timestamp' => now()->toISOString()
]);
```

## 🎯 **What You'll Get**

### **In OpenSearch:**
- Application logs with structured JSON
- Error tracking and debugging info
- User activity audit trails

### **In Grafana:**
- Response time metrics
- Request count by status code
- Error rate monitoring
- User activity dashboards

### **In CloudWatch:**
- Real-time metrics
- Alarms for high error rates
- Performance monitoring

## 🚀 **Next Steps**

1. **Deploy the logging configuration**
2. **Test with sample logs**
3. **View in Grafana dashboards**
4. **Set up alerts for critical errors**
