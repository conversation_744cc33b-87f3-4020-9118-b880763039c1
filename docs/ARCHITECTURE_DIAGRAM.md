# InfoDocs Infrastructure Architecture

## 🏗️ **Complete System Architecture**

```mermaid
graph TB
    subgraph "Internet"
        USER[👤 Users]
        OFFICE[🏢 Office Network]
    end

    subgraph "Cloudflare"
        CF_DNS[🌐 DNS]
        CF_WAF[🛡️ WAF]
        CF_CDN[⚡ CDN]
    end

    subgraph "AWS Operations VPC (********/16)"
        subgraph "Public Subnet"
            BASTION[🏰 Bastion Host<br/>Operations Gateway]
            NAT[🌐 NAT Gateway]
            ESTATE_WAZUH[🍎 Estate Wazuh<br/>Mac Fleet Monitoring]
        end

        subgraph "Private Subnet"
            subgraph "Central Operations"
                SERVER_WAZUH[🛡️ Server Wazuh<br/>Infrastructure Security]
                OPENSEARCH[🔍 OpenSearch<br/>Central Logging]
            end

            subgraph "Lambda Functions"
                WAZUH_LAMBDA[⚡ Wazuh Log Shipper<br/>Security Processing]
                OPENSEARCH_LAMBDA[⚡ OpenSearch Log Shipper<br/>Application Processing]
            end
        end
    end

    subgraph "S3 Storage"
        S3_WAZUH[🗄️ Wazuh Logs Bucket<br/>Security Events<br/>7-year retention]
        S3_OPENSEARCH[🗄️ OpenSearch Logs Bucket<br/>Application Logs<br/>Cost optimized]
        S3_LEGAL[⚖️ Legal Hold Bucket<br/>Immutable Evidence<br/>Object Lock]
    end

    subgraph "Security & Encryption"
        KMS[🔐 KMS Keys<br/>Encryption]
        SSM[🔧 SSM Parameters<br/>Secrets]
    end

    subgraph "Monitoring & Alerts"
        CW_LOGS[📊 CloudWatch Logs]
        CW_ALARMS[🚨 CloudWatch Alarms]
        SQS_DLQ[📬 Dead Letter Queues]
    end

    %% User Flow
    USER --> CF_DNS
    OFFICE --> BASTION
    CF_DNS --> CF_WAF
    CF_WAF --> CF_CDN

    %% Secure Access
    BASTION -.->|SSH Tunnels| WAZUH
    BASTION -.->|SSH Tunnels| OPENSEARCH

    %% Security Monitoring
    WAZUH -->|Security Events| WAZUH_LAMBDA
    OPENSEARCH -->|Application Logs| OPENSEARCH_LAMBDA

    %% Log Storage
    WAZUH_LAMBDA -->|Encrypted| S3_WAZUH
    OPENSEARCH_LAMBDA -->|Encrypted| S3_OPENSEARCH
    WAZUH_LAMBDA -.->|High Severity| S3_LEGAL
    OPENSEARCH_LAMBDA -.->|Critical Events| S3_LEGAL

    %% Security Infrastructure
    S3_WAZUH --> KMS
    S3_OPENSEARCH --> KMS
    S3_LEGAL --> KMS
    WAZUH --> SSM
    OPENSEARCH --> SSM

    %% Monitoring
    WAZUH_LAMBDA --> CW_LOGS
    OPENSEARCH_LAMBDA --> CW_LOGS
    CW_LOGS --> CW_ALARMS
    WAZUH_LAMBDA -.->|Failures| SQS_DLQ
    OPENSEARCH_LAMBDA -.->|Failures| SQS_DLQ

    %% Styling
    classDef security fill:#ff6b6b,stroke:#d63031,stroke-width:2px,color:#fff
    classDef storage fill:#74b9ff,stroke:#0984e3,stroke-width:2px,color:#fff
    classDef compute fill:#55a3ff,stroke:#2d3436,stroke-width:2px,color:#fff
    classDef network fill:#fd79a8,stroke:#e84393,stroke-width:2px,color:#fff
    classDef monitoring fill:#fdcb6e,stroke:#e17055,stroke-width:2px,color:#000

    class WAZUH,WAZUH_LAMBDA,S3_LEGAL,KMS security
    class S3_WAZUH,S3_OPENSEARCH,SSM storage
    class OPENSEARCH,OPENSEARCH_LAMBDA,BASTION compute
    class CF_DNS,CF_WAF,CF_CDN,NAT network
    class CW_LOGS,CW_ALARMS,SQS_DLQ monitoring
```

## 🔐 **Security Architecture Flow**

```mermaid
sequenceDiagram
    participant Agent as 🖥️ Wazuh Agent
    participant Server as 🛡️ Wazuh Server
    participant Lambda as ⚡ Lambda Processor
    participant S3 as 🗄️ S3 Storage
    participant Legal as ⚖️ Legal Hold

    Agent->>Server: Security Events
    Server->>Lambda: Process & Classify

    alt High Severity (Level ≥10)
        Lambda->>Legal: Store in Legal Hold
        Lambda->>S3: Store in Wazuh Bucket
        Note over Legal: Immutable Storage<br/>Object Lock Enabled
    else Normal Events
        Lambda->>S3: Store in Wazuh Bucket
    end

    Note over S3: Lifecycle Policy:<br/>30d → IA<br/>90d → Glacier<br/>365d → Deep Archive
```

## 📊 **Data Flow Architecture**

```mermaid
flowchart LR
    subgraph "Data Sources"
        SERVERS[🖥️ Servers]
        APPS[📱 Applications]
        CLOUD[☁️ Cloud Services]
    end

    subgraph "Processing Layer"
        WAZUH_PROC[🛡️ Wazuh<br/>Security Processing]
        OPENSEARCH_PROC[🔍 OpenSearch<br/>Log Processing]
    end

    subgraph "Lambda Layer"
        WAZUH_L[⚡ Wazuh Lambda<br/>Security Classification]
        OPENSEARCH_L[⚡ OpenSearch Lambda<br/>Cost Optimization]
    end

    subgraph "Storage Layer"
        S3_SEC[🗄️ Security Logs<br/>Never Delete]
        S3_APP[🗄️ Application Logs<br/>Cost Optimized]
        S3_LEGAL[⚖️ Legal Hold<br/>Immutable]
    end

    SERVERS --> WAZUH_PROC
    APPS --> OPENSEARCH_PROC
    CLOUD --> WAZUH_PROC

    WAZUH_PROC --> WAZUH_L
    OPENSEARCH_PROC --> OPENSEARCH_L

    WAZUH_L --> S3_SEC
    WAZUH_L -.->|Critical| S3_LEGAL
    OPENSEARCH_L --> S3_APP
    OPENSEARCH_L -.->|Critical| S3_LEGAL
```

## 🏗️ **Module Structure**

```
modules/
├── s3/
│   ├── wazuh-s3-bucket/          # Security logs (never delete)
│   ├── opensearch-s3-bucket/     # Application logs (cost optimized)
│   └── legal-hold-s3-bucket/     # Legal evidence (immutable)
├── lambda/
│   ├── wazuh-lambda-log-shipper/     # Security event processing
│   └── opensearch-lambda-log-shipper/ # Application log processing
├── ec2/
│   └── bastion/                  # Secure access gateway
└── [existing modules...]
```

## 🔒 **Security Controls**

| **Layer** | **Control** | **Implementation** |
|-----------|-------------|-------------------|
| **Network** | Private Subnets | No direct internet access |
| **Access** | Bastion Host | SSH tunneling only |
| **Encryption** | KMS | All data encrypted at rest |
| **Storage** | Object Lock | Legal hold immutable |
| **Monitoring** | CloudWatch | All access logged |
| **Compliance** | Lifecycle | Automated retention |

## 💰 **Cost Optimization**

| **Service** | **Optimization** | **Savings** |
|-------------|------------------|-------------|
| **S3 Storage** | Lifecycle Policies | 96% after 1 year |
| **OpenSearch** | Log Sampling | 50-70% reduction |
| **Lambda** | Right-sizing | Optimal memory allocation |
| **Monitoring** | Centralized | Single instance costs |

## 🎯 **Compliance Mapping**

- **POPIA**: Encrypted storage, access controls, retention policies
- **GDPR**: Data minimization, right to erasure (configurable)
- **SOX**: 7-year retention, immutable audit trails
- **HIPAA**: Encryption, access logging, secure transmission

This architecture provides enterprise-grade security monitoring with optimal costs and full compliance coverage! 🚀
