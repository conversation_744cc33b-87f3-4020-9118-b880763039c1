# Slack Webhook Management

## 🎯 **Easy Webhook Management in Code**

Slack webhooks are now managed directly in the Terragrunt configuration file, making them easy to view, update, and version control.

## 📍 **Location**

File: `environments/operations/af-south-1/analytics/grafana-config/terragrunt.hcl`

```hcl
slack_webhooks = {
  critical       = "https://hooks.slack.com/services/YOUR/CRITICAL/WEBHOOK"
  infrastructure = "https://hooks.slack.com/services/YOUR/INFRASTRUCTURE/WEBHOOK"
  application    = "https://hooks.slack.com/services/YOUR/APPLICATION/WEBHOOK"
  security       = "https://hooks.slack.com/services/YOUR/SECURITY/WEBHOOK"
}
```

## 🔧 **Setting Up Slack Webhooks**

### **Step 1: Create Slack App**

1. Go to https://api.slack.com/apps
2. Click **"Create New App"** → **"From scratch"**
3. Name: `<PERSON><PERSON>`
4. Choose your workspace

### **Step 2: Enable Incoming Webhooks**

1. In your app, go to **"Incoming Webhooks"**
2. Toggle **"Activate Incoming Webhooks"** to **On**
3. Click **"Add New Webhook to Workspace"**

### **Step 3: Create Webhooks for Each Channel**

Create webhooks for these channels:

#### **Critical Alerts** → `#critical-alerts`
- **Purpose**: System down, security breaches, data loss
- **Urgency**: Immediate response required
- **Audience**: On-call team, senior engineers

#### **Infrastructure Alerts** → `#infrastructure-alerts`
- **Purpose**: High CPU, memory, disk usage warnings
- **Urgency**: Monitor and investigate if persistent
- **Audience**: DevOps team, infrastructure engineers

#### **Application Alerts** → `#application-alerts`
- **Purpose**: API errors, slow response times, queue issues
- **Urgency**: Check application logs and performance
- **Audience**: Backend developers, application team

#### **Security Alerts** → `#security-alerts`
- **Purpose**: Wazuh security events, failed logins, threats
- **Urgency**: Security incident investigation
- **Audience**: Security team, system administrators

### **Step 4: Update Configuration**

Edit `environments/operations/af-south-1/analytics/grafana-config/terragrunt.hcl`:

```hcl
slack_webhooks = {
  critical       = "https://hooks.slack.com/services/T1234567/B1234567/CRITICAL_WEBHOOK_HERE"
  infrastructure = "https://hooks.slack.com/services/T1234567/B1234567/INFRASTRUCTURE_WEBHOOK_HERE"
  application    = "https://hooks.slack.com/services/T1234567/B1234567/APPLICATION_WEBHOOK_HERE"
  security       = "https://hooks.slack.com/services/T1234567/B1234567/SECURITY_WEBHOOK_HERE"
}
```

### **Step 5: Deploy Changes**

```bash
cd environments/operations/af-south-1/analytics/grafana-config
terragrunt plan
terragrunt apply
```

## 🔄 **Managing Webhooks**

### **Adding New Channels**

1. **Create new Slack webhook** for the channel
2. **Add to terragrunt.hcl**:
   ```hcl
   slack_webhooks = {
     # ... existing webhooks
     new_channel = "https://hooks.slack.com/services/YOUR/NEW/WEBHOOK"
   }
   ```
3. **Update variables.tf** in the module (if needed)
4. **Add contact point** in `alerting.tf`
5. **Deploy**: `terragrunt apply`

### **Updating Existing Webhooks**

1. **Edit terragrunt.hcl** with new webhook URL
2. **Deploy**: `terragrunt apply`
3. **Test**: Send test alert to verify

### **Removing Webhooks**

1. **Remove from terragrunt.hcl**
2. **Remove contact point** from `alerting.tf`
3. **Deploy**: `terragrunt apply`

## 🧪 **Testing Webhooks**

### **Test Individual Webhook**

```bash
curl -X POST -H 'Content-type: application/json' \
  --data '{"text":"🧪 Test message from Grafana setup"}' \
  YOUR_WEBHOOK_URL
```

### **Test Through Grafana**

1. Go to **Alerting** → **Contact Points**
2. Find your contact point
3. Click **"Test"**
4. Send test notification

## 📊 **Alert Routing Logic**

Alerts are automatically routed based on:

### **By Severity**
- `severity = critical` → **#critical-alerts**
- `severity = warning` → **#infrastructure-alerts**

### **By Alert Name Pattern**
- `*Security*|*Wazuh*|*Auth*|*Login*` → **#security-alerts**
- `*CPU*|*Memory*|*Disk*|*Network*` → **#infrastructure-alerts**

### **By Service**
- `service = laravel|app|api` → **#application-alerts**

## 🎨 **Message Formatting**

Each channel gets customized message format:

### **Critical Alerts**
```
🚨 CRITICAL ALERT - operations

Alert: High CPU usage detected on server-01
Description: CPU usage is above 80% for more than 5 minutes
Severity: critical
Team: devops
Instance: i-1234567890abcdef0
Started: 2024-01-15 10:30:00 SAST

Status: firing
View in Grafana: https://infodocs.grafana.net/...

⚠️ Action Required - Please investigate immediately
```

### **Infrastructure Alerts**
```
⚠️ Infrastructure Alert - operations

Alert: High memory usage detected on server-01
Description: Memory usage is above 85% for more than 5 minutes
Severity: warning
Instance: i-1234567890abcdef0
Started: 2024-01-15 10:30:00 SAST

Status: firing
View in Grafana: https://infodocs.grafana.net/...

📊 Monitor and investigate if persistent
```

## 🔒 **Security Considerations**

### **Webhook Security**
- ✅ **Webhooks are stored in code** - visible to team
- ✅ **Version controlled** - track changes
- ✅ **No secrets in SSM** - easier management
- ⚠️ **Webhook URLs are sensitive** - don't share publicly

### **Access Control**
- **Slack workspace admins** can create webhooks
- **Infrastructure team** can update configuration
- **Git repository access** controls who can modify

## 🚀 **Benefits of This Approach**

### **Easy Management**
- ✅ **Visible in code** - no hidden SSM parameters
- ✅ **Quick updates** - edit file and deploy
- ✅ **Team collaboration** - everyone can see configuration
- ✅ **Version controlled** - track all changes

### **Flexible Routing**
- ✅ **Multiple channels** - different teams get relevant alerts
- ✅ **Custom formatting** - tailored messages per channel
- ✅ **Smart routing** - automatic based on alert properties
- ✅ **Easy testing** - test individual webhooks

### **Operational Benefits**
- ✅ **Faster incident response** - alerts go to right teams
- ✅ **Reduced noise** - relevant alerts only
- ✅ **Better visibility** - team-specific channels
- ✅ **Audit trail** - all changes tracked in Git

## 📝 **Next Steps**

1. **Create Slack channels** if they don't exist
2. **Set up webhooks** for each channel
3. **Update terragrunt.hcl** with real webhook URLs
4. **Deploy configuration**
5. **Test each webhook** to ensure delivery
6. **Monitor alert delivery** and adjust as needed

This approach makes Slack webhook management much more transparent and easier to maintain! 🎉
