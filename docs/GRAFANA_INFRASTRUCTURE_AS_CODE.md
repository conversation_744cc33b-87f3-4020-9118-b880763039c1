# Grafana Infrastructure as Code

This guide shows how to manage your Grafana Cloud configuration (data sources, dashboards, alerts, and Slack notifications) as Infrastructure as Code using OpenTofu.

## 🎯 What This Achieves

- **Manage Grafana as Code**: All dashboards, alerts, and data sources in version control
- **Automated Slack Alerting**: Route alerts to appropriate Slack channels based on severity
- **Laravel Metrics Integration**: Custom CloudWatch metrics from your Laravel application
- **Centralized Monitoring**: AWS infrastructure + application + security monitoring

## 📁 Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Laravel App   │───▶│   CloudWatch     │───▶│  Grafana Cloud  │
│   (Custom       │    │   (Metrics)      │    │  (Your existing │
│    Metrics)     │    │                  │    │   account)      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                        │
┌─────────────────┐    ┌──────────────────┐            │
│   OpenSearch    │───▶│   Grafana Data   │◀───────────┘
│   (Logs &       │    │   Sources        │
│    Security)    │    │                  │
└─────────────────┘    └──────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │   Slack Alerts   │
                       │ #critical-alerts │
                       │ #infra-alerts    │
                       │ #app-alerts      │
                       │ #security-alerts │
                       └──────────────────┘
```

## 🚀 Quick Start

### 1. Setup SSM Parameters

```bash
# Create required SSM parameters
./scripts/setup-grafana-cloud-ssm.sh
```

### 2. Configure Grafana Cloud

1. **Create Service Account**:
   - Go to your Grafana Cloud instance
   - Navigate to Administration → Service Accounts
   - Create new service account with **Editor** role
   - Generate token

2. **Update SSM Parameters**:
   ```bash
   # Update with your Grafana Cloud URL
   aws ssm put-parameter \
     --name "/infodocs/grafana/cloud_url" \
     --value "https://your-org.grafana.net" \
     --type "String" \
     --overwrite

   # Update with your service account token
   aws ssm put-parameter \
     --name "/infodocs/grafana/service_account_token" \
     --value "YOUR_GRAFANA_SERVICE_ACCOUNT_TOKEN" \
     --type "SecureString" \
     --overwrite
   ```

### 3. Use Existing infrastructure-as-code-kms User

Your existing `infrastructure-as-code-kms` user already has all required permissions:
- ✅ **PowerUserAccess** - Includes CloudWatch permissions
- ✅ **CloudWatchLogsFullAccess** - Full CloudWatch Logs access
- ✅ **KMS PowerUser** - Can use KMS keys

**Store your existing credentials in SSM**:
```bash
aws ssm put-parameter \
  --name "/infodocs/grafana/aws_access_key_id" \
  --value "YOUR_EXISTING_ACCESS_KEY" \
  --type "SecureString" \
  --overwrite

aws ssm put-parameter \
  --name "/infodocs/grafana/aws_secret_access_key" \
  --value "YOUR_EXISTING_SECRET_KEY" \
  --type "SecureString" \
  --overwrite
```

### 4. Configure Slack Webhooks (In Code!)

1. **Create Slack App** at https://api.slack.com/apps
2. **Enable Incoming Webhooks**
3. **Create webhooks for channels**:
   - `#critical-alerts`
   - `#infrastructure-alerts`
   - `#application-alerts`
   - `#security-alerts`

4. **Update Terragrunt Configuration**:
   Edit `environments/operations/af-south-1/analytics/grafana-config/terragrunt.hcl`:
   ```hcl
   slack_webhooks = {
     critical       = "https://hooks.slack.com/services/YOUR/CRITICAL/WEBHOOK"
     infrastructure = "https://hooks.slack.com/services/YOUR/INFRASTRUCTURE/WEBHOOK"
     application    = "https://hooks.slack.com/services/YOUR/APPLICATION/WEBHOOK"
     security       = "https://hooks.slack.com/services/YOUR/SECURITY/WEBHOOK"
   }
   ```

### 5. Deploy Grafana Configuration

```bash
cd environments/operations/af-south-1/analytics/grafana-config
terragrunt plan
terragrunt apply
```

## 📊 What Gets Created

### Data Sources
- **CloudWatch**: AWS infrastructure metrics
- **CloudWatch Logs**: Application and system logs
- **OpenSearch-Logs**: General log analytics
- **OpenSearch-Security**: Wazuh security data

### Dashboards
- **AWS Infrastructure**: EC2, Lambda, RDS metrics
- **Laravel Application**: Custom application metrics
- **Security Monitoring**: Wazuh alerts and security events
- **Lambda Functions**: Serverless function monitoring
- **Cost Monitoring**: AWS cost tracking

### Alert Rules
- **High CPU Usage**: > 80% for 5 minutes
- **High Memory Usage**: > 85% for 5 minutes
- **High Disk Usage**: > 90% for 2 minutes
- **Lambda Errors**: > 5 errors in 2 minutes
- **Security Events**: Critical Wazuh alerts

### Slack Integration
- **Critical Alerts** → `#critical-alerts`
- **Infrastructure Alerts** → `#infrastructure-alerts`
- **Application Alerts** → `#application-alerts`
- **Security Alerts** → `#security-alerts`

## 🔧 Laravel Integration

### 1. Install Dependencies

```bash
composer require aws/aws-sdk-php
```

### 2. Configure Environment

```env
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_DEFAULT_REGION=af-south-1
AWS_CLOUDWATCH_NAMESPACE=InfoDocs/Laravel
```

### 3. Add Metrics Middleware

See `docs/LARAVEL_METRICS_INTEGRATION.md` for detailed implementation.

### 4. Custom Metrics Examples

```php
// Request metrics (automatic via middleware)
// - RequestCount
// - ResponseTime
// - ErrorCount
// - MemoryUsage

// Business metrics (manual)
$this->incrementMetric('DocumentCreated');
$this->recordMetric('PaymentAmount', $amount, 'None');
$this->recordTime('ProcessingTime', $milliseconds);
```

## 🔄 Managing Changes

### Adding New Dashboards

1. **Create JSON file** in `modules/analytics/grafana-config/dashboards/`
2. **Add to dashboards.tf**:
   ```hcl
   resource "grafana_dashboard" "new_dashboard" {
     folder      = grafana_folder.your_folder.id
     config_json = templatefile("${path.module}/dashboards/new-dashboard.json", {
       datasource_uid = grafana_data_source.cloudwatch.uid
       environment    = var.environment
     })
   }
   ```
3. **Deploy**: `terragrunt apply`

### Adding New Alert Rules

1. **Add to alerting.tf**:
   ```hcl
   rule {
     name      = "NewAlert"
     condition = "C"
     # ... rule configuration
   }
   ```
2. **Deploy**: `terragrunt apply`

### Updating Slack Channels

1. **Update SSM parameters**:
   ```bash
   aws ssm put-parameter \
     --name "/infodocs/grafana/slack_webhook_new_channel" \
     --value "https://hooks.slack.com/services/..." \
     --type "SecureString" \
     --overwrite
   ```
2. **Add contact point** in `alerting.tf`
3. **Deploy**: `terragrunt apply`

## 🔍 Monitoring the Monitoring

### Grafana Health Checks

- Monitor Grafana Cloud uptime
- Check data source connectivity
- Verify alert delivery to Slack

### Cost Monitoring

- CloudWatch API calls
- Grafana Cloud usage
- Data transfer costs

### Performance Optimization

- Optimize dashboard queries
- Reduce metric cardinality
- Implement metric sampling

## 🚨 Troubleshooting

### Common Issues

1. **Data source connection failed**:
   - Check AWS credentials in SSM
   - Verify IAM permissions
   - Test OpenSearch connectivity

2. **Alerts not firing**:
   - Check alert rule conditions
   - Verify notification policies
   - Test Slack webhooks

3. **Dashboard not loading**:
   - Check data source UIDs
   - Verify metric names
   - Review CloudWatch namespaces

### Debugging Commands

```bash
# Check SSM parameters
aws ssm get-parameters-by-path --path "/infodocs/grafana/"

# Test Slack webhook
curl -X POST -H 'Content-type: application/json' \
  --data '{"text":"Test message"}' \
  YOUR_WEBHOOK_URL

# Verify CloudWatch metrics
aws cloudwatch list-metrics --namespace "InfoDocs/Laravel"
```

## 📈 Next Steps

1. **Extend Laravel Metrics**: Add more business-specific metrics
2. **Custom Dashboards**: Create team-specific dashboards
3. **Advanced Alerting**: Implement escalation policies
4. **Integration**: Connect with other monitoring tools
5. **Automation**: Add automated responses to alerts

This Infrastructure as Code approach ensures your monitoring configuration is:
- **Version controlled**
- **Reproducible**
- **Auditable**
- **Scalable**
- **Maintainable**
