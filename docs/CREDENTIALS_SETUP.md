# InfoDocs Infrastructure Credentials Setup Guide

## 📋 Overview

This guide details all the credentials and domain settings you need to configure for Wazuh and OpenSearch before deployment.

## 🔐 Required Credentials

### **Existing SSM Parameters (Already Set Up)**
✅ `/infodocs/cloudflare/api_token` - Cloudflare API token
✅ `/infodocs/iac/credentials/access_key` - AWS access key for CI/CD
✅ `/infodocs/iac/credentials/secret_key` - AWS secret key for CI/CD
✅ `/infodocs/kms/credentials/access_key` - KMS access key
✅ `/infodocs/kms/credentials/secret_key` - KMS secret key

### **New SSM Parameters (To Be Created)**

#### 🌐 **Cloudflare Configuration**
- **Parameter**: `/infodocs/dev/cloudflare/zone_id`
- **Value**: Your Cloudflare zone ID for infodocs.co.za
- **How to find**: Cloudflare Dashboard → Domain → Zone ID (right sidebar)
- **Example**: `1234567890abcdef1234567890abcdef`

#### 🛡️ **Wazuh Security Platform**
- **Parameter**: `/infodocs/dev/wazuh/admin_password`
- **Purpose**: Admin login for Wazuh Web UI (https://WAZUH_IP)
- **Requirements**: Strong password (16+ chars, mixed case, numbers, symbols)
- **Example**: `WazuhAdmin2024!SecurePass`

- **Parameter**: `/infodocs/dev/wazuh/api_key`
- **Purpose**: API authentication for Wazuh REST API
- **Requirements**: Random string (32+ characters)
- **Example**: `wazuh-api-key-abc123def456ghi789jkl012`

#### 🔍 **OpenSearch Analytics Platform**
- **Parameter**: `/infodocs/dev/opensearch/master_user`
- **Purpose**: Admin username for OpenSearch Dashboards
- **Value**: `admin` (standard)

- **Parameter**: `/infodocs/dev/opensearch/master_password`
- **Purpose**: Admin password for OpenSearch Dashboards
- **Requirements**: AWS password policy (12+ chars, mixed case, numbers, symbols)
- **Example**: `OpenSearch2024!Admin`

## 🔑 **SSH Key Setup**

### **Create EC2 Key Pair**
1. Go to AWS Console → EC2 → Key Pairs
2. Create new key pair: `infodocs-dev-key`
3. Download the .pem file
4. Store securely for SSH access to Wazuh instances

## 🌍 **Domain and Access Information**

### **Wazuh Access**
- **Web UI**: `https://<WAZUH_EC2_IP>:443`
- **API**: `https://<WAZUH_EC2_IP>:55000`
- **Username**: `admin`
- **Password**: Value from `/infodocs/dev/wazuh/admin_password`
- **No custom domain required** - uses EC2 public IP

### **OpenSearch Access**
- **Dashboards**: AWS-provided endpoint (e.g., `https://search-infodocs-dev-opensearch-xyz.af-south-1.es.amazonaws.com`)
- **Username**: Value from `/infodocs/dev/opensearch/master_user` (admin)
- **Password**: Value from `/infodocs/dev/opensearch/master_password`
- **No custom domain required** - uses AWS-provided endpoint

## 📝 **Deployment Steps**

### **1. Update SSM Parameters**
After deploying the SSM module, update these parameters with real values:

```bash
# Deploy SSM module first
cd environments/dev/af-south-1/ssm
terragrunt apply

# Then update parameters in AWS Console or CLI:
aws ssm put-parameter --name "/infodocs/dev/cloudflare/zone_id" --value "YOUR_ZONE_ID" --overwrite
aws ssm put-parameter --name "/infodocs/dev/wazuh/admin_password" --value "YOUR_STRONG_PASSWORD" --type "SecureString" --overwrite
aws ssm put-parameter --name "/infodocs/dev/wazuh/api_key" --value "YOUR_API_KEY" --type "SecureString" --overwrite
aws ssm put-parameter --name "/infodocs/dev/opensearch/master_password" --value "YOUR_OPENSEARCH_PASSWORD" --type "SecureString" --overwrite
```

### **2. Create SSH Key**
```bash
# Create EC2 key pair
aws ec2 create-key-pair --key-name infodocs-dev-key --query 'KeyMaterial' --output text > infodocs-dev-key.pem
chmod 400 infodocs-dev-key.pem
```

### **3. Deploy Infrastructure**
```bash
# Deploy in order
cd environments/dev/af-south-1/kms && terragrunt apply
cd ../vpc && terragrunt apply
cd ../ssm && terragrunt apply
# Update SSM parameters here
cd ../opensearch && terragrunt apply
cd ../wazuh && terragrunt apply
```

## 🔒 **Security Notes**

- **All passwords are stored encrypted** in SSM Parameter Store using KMS
- **SSH access** is restricted to your VPC CIDR blocks
- **Web UIs** are accessible via HTTPS only
- **API access** requires authentication tokens
- **No hardcoded credentials** in any configuration files

## ✅ **Summary**

**You need to provide:**
1. Cloudflare zone ID for your domain
2. Strong password for Wazuh admin (16+ chars)
3. API key for Wazuh (32+ chars random string)
4. Strong password for OpenSearch admin (12+ chars)
5. Create EC2 key pair for SSH access

**No custom domains needed** - both services use IP addresses or AWS-provided endpoints.
