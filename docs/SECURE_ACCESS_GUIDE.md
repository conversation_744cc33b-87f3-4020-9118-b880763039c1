# Secure Access Guide for InfoDocs Monitoring Systems

## 🔒 Security Architecture

Your monitoring systems (Wazuh and OpenSearch) are now **completely isolated** from public internet access:

- ✅ **No public IPs** on monitoring systems
- ✅ **VPC-only access** (********/16 internal network)
- ✅ **Bastion host** for secure tunneling
- ✅ **IP whitelisting** for SSH access

## 🏗️ Access Architecture

```
Internet → Your Office IP → Bastion Host → Internal Monitoring Systems
   ❌           ✅              ✅                    ✅
Public      Whitelisted    Public Subnet        Private Subnet
Access      IP Only        (Bastion)           (Wazuh/OpenSearch)
```

## 🔑 Secure Access Process

### **Step 1: Get Your Public IP**
```bash
# Find your current public IP
curl -s https://ipinfo.io/ip
# Example output: **************
```

### **Step 2: Update Bastion Configuration**
```bash
# Edit the bastion terragrunt.hcl file
# Replace YOUR_OFFICE_IP and YOUR_HOME_IP with actual IPs:
allowed_ssh_cidr_blocks = [
  "**************/32",    # Your office IP
  "*************/32"      # Your home IP
]
```

### **Step 3: Deploy Bastion Host**
```bash
cd environments/dev/af-south-1/bastion
terragrunt init
terragrunt plan
terragrunt apply
```

### **Step 4: Connect to Bastion**
```bash
# SSH to bastion host
ssh -i infodocs-dev-infrastructure.pem ec2-user@BASTION_PUBLIC_IP
```

### **Step 5: Create SSH Tunnels for Monitoring Access**

#### **Access Wazuh (God's Eye Security)**
```bash
# From your local machine, create tunnel to Wazuh
ssh -i infodocs-dev-infrastructure.pem -L 8443:WAZUH_PRIVATE_IP:443 ec2-user@BASTION_PUBLIC_IP

# Then access Wazuh in your browser:
https://localhost:8443
Username: admin
Password: [from SSM parameter]
```

#### **Access OpenSearch (God's Eye Logs)**
```bash
# From your local machine, create tunnel to OpenSearch
ssh -i infodocs-dev-infrastructure.pem -L 8444:OPENSEARCH_ENDPOINT:443 ec2-user@BASTION_PUBLIC_IP

# Then access OpenSearch in your browser:
https://localhost:8444
Username: admin
Password: [from SSM parameter]
```

## 🛡️ Security Benefits

### **Complete Network Isolation**
- **Wazuh**: Only accessible from VPC (********/16)
- **OpenSearch**: Only accessible from VPC (********/16)
- **No direct internet access** to monitoring systems

### **Controlled Access Points**
- **Single entry point**: Bastion host only
- **IP whitelisting**: Only your office/home IPs
- **SSH key authentication**: No password access
- **Audit logging**: All access logged to CloudWatch

### **Defense in Depth**
1. **Perimeter**: IP whitelisting on bastion
2. **Access**: SSH key authentication
3. **Network**: VPC isolation
4. **Application**: Username/password on monitoring systems
5. **Encryption**: All traffic encrypted (SSH + HTTPS)

## 📋 Daily Access Workflow

### **Morning Security Check**
```bash
# 1. SSH to bastion
ssh -i infodocs-dev-infrastructure.pem ec2-user@BASTION_PUBLIC_IP

# 2. Create tunnels (in separate terminals)
ssh -i infodocs-dev-infrastructure.pem -L 8443:WAZUH_PRIVATE_IP:443 ec2-user@BASTION_PUBLIC_IP
ssh -i infodocs-dev-infrastructure.pem -L 8444:OPENSEARCH_ENDPOINT:443 ec2-user@BASTION_PUBLIC_IP

# 3. Access monitoring systems
# Wazuh: https://localhost:8443
# OpenSearch: https://localhost:8444
```

### **Emergency Access**
```bash
# If you need access from a new location:
# 1. Get your current IP: curl -s https://ipinfo.io/ip
# 2. Update bastion security group via AWS Console
# 3. Add your new IP to allowed_ssh_cidr_blocks
# 4. Apply terragrunt changes
```

## 🚨 Security Alerts

### **Failed SSH Attempts**
- All failed SSH attempts logged to CloudWatch
- Monitor `/aws/ec2/bastion` log group
- Set up alerts for suspicious activity

### **Unusual Access Patterns**
- Monitor bastion access times
- Alert on access outside business hours
- Track which IPs are connecting

## ✅ Security Checklist

- [ ] Bastion host deployed with IP whitelisting
- [ ] Monitoring systems have no public access
- [ ] SSH tunnels working for Wazuh and OpenSearch
- [ ] CloudWatch logging enabled on bastion
- [ ] SSH keys properly secured
- [ ] Office/home IPs whitelisted only
- [ ] Emergency access procedure documented

**Your monitoring systems are now completely secure and isolated!** 🔒
