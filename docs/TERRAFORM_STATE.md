# Terraform State Management

This document explains how Terraform state is managed in the InfoDocs infrastructure using Terragrunt and AWS S3 backend.

## Overview

Terraform state is stored remotely in AWS S3 with DynamoDB for state locking, ensuring safe collaboration and preventing state corruption.

## State Configuration

### S3 Backend Configuration
```hcl
remote_state {
  backend = "s3"
  config = {
    bucket         = "infodocs-terraform-state"
    key            = "${path_relative_to_include()}/terraform.tfstate"
    region         = "af-south-1"
    encrypt        = true
    dynamodb_table = "infodocs-terraform-locks"
  }
}
```

### Key Components

| Component | Value | Purpose |
|-----------|-------|---------|
| **S3 Bucket** | `infodocs-terraform-state` | Stores Terraform state files |
| **DynamoDB Table** | `infodocs-terraform-locks` | Provides state locking mechanism |
| **Region** | `af-south-1` | South Africa region for data sovereignty |
| **Encryption** | `true` | State files encrypted at rest |
| **KMS Key** | `infodocs-operations-encryption-key` | Encrypts state files and infrastructure |

## State File Structure

### Directory Layout
```
s3://infodocs-terraform-state/
├── environments/operations/af-south-1/
│   ├── security/kms/terraform.tfstate
│   ├── network/vpc/terraform.tfstate
│   ├── analytics/opensearch/terraform.tfstate
│   ├── storage/opensearch-s3-compliance/terraform.tfstate
│   ├── storage/wazuh-s3-compliance/terraform.tfstate
│   ├── security/wazuh/server-endpoint/terraform.tfstate
│   ├── security/wazuh/estate-endpoint/terraform.tfstate
│   └── ec2/bastion/terraform.tfstate
├── environments/dev/af-south-1/
│   └── applications/*/terraform.tfstate
└── environments/prod/af-south-1/
    └── applications/*/terraform.tfstate
```

### State File Naming Convention
- **Pattern**: `{environment}/{region}/{service}/{component}/terraform.tfstate`
- **Example**: `environments/operations/af-south-1/security/kms/terraform.tfstate`

## State Locking

### How It Works
1. **Lock Acquisition**: When `terragrunt plan/apply` runs, it acquires a lock in DynamoDB
2. **Concurrent Protection**: Other operations wait until lock is released
3. **Automatic Release**: Lock released when operation completes
4. **Force Unlock**: Manual unlock available if process crashes

### DynamoDB Lock Table
```
Table: infodocs-terraform-locks
├── LockID (Primary Key): MD5 hash of state file path
├── Info: JSON with operation details
├── Digest: State file checksum
└── Created: Lock creation timestamp
```

## Encryption

### State File Encryption
- **At Rest**: S3 server-side encryption with KMS
- **In Transit**: HTTPS/TLS encryption
- **KMS Key**: `alias/infodocs-operations-encryption-key`

### Infrastructure Encryption
All infrastructure resources use the same KMS key:
- **OpenSearch domains**
- **S3 buckets**
- **EBS volumes**
- **Parameter Store values**

## Multi-Engineer Workflow

### Safe Collaboration
```bash
# Engineer A
cd environments/operations/af-south-1/security/kms
terragrunt plan  # ✅ Read-only, no lock needed

# Engineer B (simultaneously)
cd environments/operations/af-south-1/network/vpc
terragrunt plan  # ✅ Different state file, no conflict

# Engineer A
terragrunt apply  # 🔒 Acquires lock on KMS state

# Engineer B
cd environments/operations/af-south-1/security/kms
terragrunt apply  # ⏳ Waits for Engineer A's lock to release
```

### Best Practices
1. **Always run `terragrunt plan`** before `terragrunt apply`
2. **Keep apply operations short** to minimize lock time
3. **Communicate with team** before major changes
4. **Use feature branches** for experimental changes
5. **Never force unlock** unless absolutely necessary

## State Management Commands

### Common Operations
```bash
# View current state
terragrunt state list

# Show specific resource
terragrunt state show aws_instance.bastion

# Import existing resource
terragrunt import aws_instance.example i-1234567890abcdef0

# Remove resource from state (doesn't destroy)
terragrunt state rm aws_instance.example

# Move resource in state
terragrunt state mv aws_instance.old aws_instance.new
```

### Backup and Recovery
```bash
# Manual state backup
terragrunt state pull > backup-$(date +%Y%m%d-%H%M%S).tfstate

# Restore from backup (emergency only)
terragrunt state push backup-20250626-120000.tfstate
```

## Troubleshooting

### Common Issues

#### Lock Stuck
```bash
# Check lock status
aws dynamodb get-item \
  --table-name infodocs-terraform-locks \
  --key '{"LockID":{"S":"md5-hash-of-state-path"}}'

# Force unlock (use with caution)
terragrunt force-unlock LOCK_ID
```

#### State Corruption
```bash
# Verify state integrity
terragrunt state list

# Refresh state from actual infrastructure
terragrunt refresh

# Import missing resources
terragrunt import resource_type.name resource_id
```

#### Access Issues
```bash
# Verify AWS credentials
aws sts get-caller-identity

# Check S3 bucket access
aws s3 ls s3://infodocs-terraform-state/

# Check DynamoDB table access
aws dynamodb describe-table --table-name infodocs-terraform-locks
```

## Security Considerations

### Access Control
- **S3 Bucket**: Restricted to infrastructure team IAM roles
- **DynamoDB Table**: Read/write access for state locking
- **KMS Key**: Decrypt permissions for infrastructure team
- **State Files**: Never commit to version control

### Audit Trail
- **CloudTrail**: Logs all S3 and DynamoDB API calls
- **S3 Access Logs**: Track state file access
- **DynamoDB Logs**: Monitor lock operations

## Monitoring

### State Health Checks
```bash
# Verify state bucket exists
aws s3api head-bucket --bucket infodocs-terraform-state

# Check DynamoDB table status
aws dynamodb describe-table --table-name infodocs-terraform-locks

# Validate state file integrity
terragrunt validate
```

### Alerts
- **S3 bucket deletion protection**
- **DynamoDB table deletion protection**
- **KMS key rotation monitoring**
- **Unusual state access patterns**

## Disaster Recovery

### Backup Strategy
- **S3 Versioning**: Enabled on state bucket
- **Cross-region replication**: Planned for EU backup
- **Point-in-time recovery**: DynamoDB backup enabled
- **KMS key backup**: Multi-region key replication

### Recovery Procedures
1. **State corruption**: Restore from S3 version history
2. **Lock table loss**: Recreate DynamoDB table
3. **Bucket deletion**: Restore from cross-region replica
4. **Complete disaster**: Rebuild from infrastructure code

## References

- [Terragrunt Documentation](https://terragrunt.gruntwork.io/)
- [Terraform S3 Backend](https://www.terraform.io/docs/language/settings/backends/s3.html)
- [AWS S3 Encryption](https://docs.aws.amazon.com/AmazonS3/latest/userguide/UsingEncryption.html)
- [DynamoDB Locking](https://www.terraform.io/docs/language/settings/backends/s3.html#dynamodb-state-locking)
