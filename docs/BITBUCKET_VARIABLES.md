# Bitbucket Repository Variables for InfoDocs Infrastructure

## 🔧 **Required Repository Variables**

Add these variables in Bitbucket: **Repository Settings → Pipelines → Repository Variables**

### **1. AWS Credentials**
```
Variable Name: AWS_ACCESS_KEY_ID
Value: YOUR_AWS_ACCESS_KEY_ID
Secured: ✅ YES

Variable Name: AWS_SECRET_ACCESS_KEY
Value: YOUR_AWS_SECRET_ACCESS_KEY
Secured: ✅ YES

Variable Name: AWS_DEFAULT_REGION
Value: af-south-1
Secured: ❌ NO
```

### **2. Terragrunt Configuration**
```
Variable Name: TF_VAR_environment
Value: dev
Secured: ❌ NO

Variable Name: TERRAGRUNT_NON_INTERACTIVE
Value: true
Secured: ❌ NO

Variable Name: TF_IN_AUTOMATION
Value: true
Secured: ❌ NO
```

### **3. Infrastructure Configuration**
```
Variable Name: INFODOCS_DOMAIN
Value: infodocs.co.za
Secured: ❌ NO

Variable Name: DEPLOYMENT_ENVIRONMENT
Value: dev
Secured: ❌ NO

Variable Name: PROJECT_NAME
Value: infodocs
Secured: ❌ NO
```

### **4. Security Configuration**
```
Variable Name: ENABLE_SECURITY_SCANNING
Value: true
Secured: ❌ NO

Variable Name: COMPLIANCE_MODE
Value: strict
Secured: ❌ NO

Variable Name: DATA_CLASSIFICATION
Value: confidential
Secured: ❌ NO
```

### **5. Notification Configuration (Optional)**
```
Variable Name: SLACK_WEBHOOK_URL
Value: YOUR_SLACK_WEBHOOK_URL
Secured: ✅ YES

Variable Name: TEAMS_WEBHOOK_URL
Value: YOUR_TEAMS_WEBHOOK_URL
Secured: ✅ YES

Variable Name: EMAIL_NOTIFICATIONS
Value: <EMAIL>
Secured: ❌ NO
```

### **6. Pipeline Configuration**
```
Variable Name: ENABLE_MANUAL_APPROVAL
Value: true
Secured: ❌ NO

Variable Name: AUTO_DEPLOY_DEV
Value: false
Secured: ❌ NO

Variable Name: ENABLE_COST_ALERTS
Value: true
Secured: ❌ NO
```

## 📋 **How to Add Variables in Bitbucket**

1. **Navigate to Repository Settings**
   ```
   Your Repository → Settings → Pipelines → Repository Variables
   ```

2. **Add Each Variable**
   - Click "Add Variable"
   - Enter Variable Name (exactly as shown above)
   - Enter Variable Value
   - Check "Secured" for sensitive values
   - Click "Add"

3. **Verify Variables**
   ```bash
   # In pipeline, you can echo non-secured variables:
   echo $AWS_DEFAULT_REGION
   echo $PROJECT_NAME

   # Never echo secured variables in logs!
   ```

## 🔒 **Security Best Practices**

### **✅ DO:**
- Mark all credentials as "Secured"
- Use least-privilege AWS IAM policies
- Rotate credentials regularly
- Monitor variable access in audit logs

### **❌ DON'T:**
- Echo secured variables in pipeline logs
- Use production credentials in dev pipelines
- Store secrets in code or comments
- Share credentials via insecure channels

## 🚨 **Required Before First Pipeline Run**

1. ✅ Add all AWS credentials as secured variables
2. ✅ Set correct AWS region (af-south-1)
3. ✅ Configure project-specific variables
4. ✅ Test pipeline with validation steps only
5. ✅ Verify no secrets appear in logs

## 🔧 **Pipeline Variable Usage**

Variables are automatically available in pipeline steps:

```yaml
# Example usage in bitbucket-pipelines.yml
script:
  - echo "Deploying to $AWS_DEFAULT_REGION"
  - echo "Project: $PROJECT_NAME"
  - echo "Environment: $DEPLOYMENT_ENVIRONMENT"
  - aws configure set region $AWS_DEFAULT_REGION
  - terragrunt init --terragrunt-non-interactive
```

## 📊 **Variable Validation**

Add this step to validate all required variables are set:

```yaml
- step:
    name: Validate Environment Variables
    script:
      - |
        REQUIRED_VARS=(
          "AWS_ACCESS_KEY_ID"
          "AWS_SECRET_ACCESS_KEY"
          "AWS_DEFAULT_REGION"
          "PROJECT_NAME"
          "DEPLOYMENT_ENVIRONMENT"
        )

        for var in "${REQUIRED_VARS[@]}"; do
          if [ -z "${!var}" ]; then
            echo "❌ ERROR: Required variable $var is not set"
            exit 1
          else
            echo "✅ $var is set"
          fi
        done
```

**All variables configured = Ready for deployment!** 🚀
