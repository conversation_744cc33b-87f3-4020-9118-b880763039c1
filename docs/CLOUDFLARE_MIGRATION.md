# 🌐 Cloudflare Migration Strategy

## **📋 Current Situation**
- ✅ Cloudflare account exists
- ❌ DNS records not yet created in Cloudflare
- ❌ Nameservers not yet updated to Cloudflare
- ✅ Current DNS working (infodocs.co.za → ***********)

## **🚀 Safe Migration Plan**

### **Phase 1: Prepare Cloudflare (No Impact)**
1. **Deploy Cloudflare module** (creates DNS records in Cloudflare)
2. **Verify records** are created correctly
3. **Test resolution** using Cloudflare nameservers directly
4. **No impact** on live traffic (still using old nameservers)

### **Phase 2: Switch Nameservers (Go Live)**
1. **Update domain registrar** to use Cloudflare nameservers
2. **Monitor DNS propagation** (24-48 hours)
3. **Verify all services** working through Cloudflare
4. **Rollback plan** ready if needed

## **🔧 Step-by-Step Implementation**

### **Step 1: Deploy Cloudflare Infrastructure**

```bash
# Deploy Cloudflare module (creates records, doesn't affect live traffic)
cd environments/dev/af-south-1/dns/cloudflare
terragrunt plan   # Review what will be created
terragrunt apply  # Create DNS records in Cloudflare
```

**What this does:**
- ✅ Creates DNS records in Cloudflare
- ✅ Configures security settings
- ❌ **NO IMPACT** on live traffic (nameservers unchanged)

### **Step 2: Verify Cloudflare Configuration**

```bash
# Test DNS resolution using Cloudflare nameservers directly
dig @ns1.cloudflare.com infodocs.co.za
dig @ns2.cloudflare.com infodocs.co.za

# Should return: *********** (your current IP)
```

### **Step 3: Get Cloudflare Nameservers**

```bash
# Get your Cloudflare nameservers
aws ssm get-parameter \
  --name "/infodocs/cloudflare/zone_id" \
  --query 'Parameter.Value' \
  --output text

# Or check Cloudflare dashboard for nameservers like:
# ns1.cloudflare.com
# ns2.cloudflare.com
```

### **Step 4: Update Domain Registrar (Go Live)**

**⚠️ This is the only step that affects live traffic**

1. **Login to your domain registrar** (where you bought infodocs.co.za)
2. **Find DNS/Nameserver settings**
3. **Replace current nameservers** with Cloudflare nameservers:
   ```
   ns1.cloudflare.com
   ns2.cloudflare.com
   ```
4. **Save changes**

### **Step 5: Monitor Migration**

```bash
# Check DNS propagation
dig infodocs.co.za
nslookup infodocs.co.za

# Monitor website availability
curl -I https://infodocs.co.za
```

## **🛡️ Safety Measures**

### **Before Migration:**
- [ ] Backup current DNS records
- [ ] Document current nameservers
- [ ] Test Cloudflare records work correctly
- [ ] Have rollback plan ready

### **During Migration:**
- [ ] Monitor website availability
- [ ] Check email delivery (if using email)
- [ ] Verify all subdomains work
- [ ] Monitor for 24-48 hours

### **Rollback Plan:**
If issues occur, immediately:
1. **Revert nameservers** to original ones
2. **DNS will revert** in 24-48 hours
3. **No permanent damage** - just time delay

## **📊 Migration Timeline**

| Phase | Duration | Impact | Reversible |
|-------|----------|--------|------------|
| Deploy Cloudflare Module | 5 minutes | None | N/A |
| Verify Configuration | 15 minutes | None | N/A |
| Update Nameservers | 2 minutes | **LIVE TRAFFIC** | Yes |
| DNS Propagation | 24-48 hours | Gradual switch | Yes |

## **🔍 Pre-Migration Checklist**

### **Infrastructure Ready:**
- [ ] SSM parameters created
- [ ] Cloudflare API token working
- [ ] Zone ID correct
- [ ] IP address correct (***********)

### **DNS Records to Create:**
- [ ] A record: infodocs.co.za → ***********
- [ ] A record: www.infodocs.co.za → ***********
- [ ] Existing subdomains:
  - [ ] uat.uat-api.infodocs.co.za → *************
  - [ ] staging.lego2.infodocs.co.za → **************
  - [ ] secure.api.infodocs.co.za → *************

### **Backup Current DNS:**
```bash
# Export current DNS records before migration
dig infodocs.co.za ANY > dns_backup_$(date +%Y%m%d).txt
```

## **🚨 Risk Assessment**

### **Low Risk:**
- ✅ Deploying Cloudflare module (no traffic impact)
- ✅ Testing DNS records
- ✅ Preparing configuration

### **Medium Risk:**
- ⚠️ Updating nameservers (affects live traffic)
- ⚠️ DNS propagation delays
- ⚠️ Potential email delivery issues

### **Mitigation:**
- ✅ Deploy during low-traffic hours
- ✅ Have rollback plan ready
- ✅ Monitor closely for 48 hours
- ✅ Test all services after migration

## **💡 Recommended Approach**

1. **Deploy Cloudflare module first** (safe, no impact)
2. **Test thoroughly** using Cloudflare nameservers
3. **Schedule nameserver update** during maintenance window
4. **Monitor closely** for 48 hours
5. **Keep rollback plan** ready

**The key is that deploying the Cloudflare module has ZERO impact on live traffic until you update the nameservers at your domain registrar.**
