# 🔐 Password Management & Rotation Guide

## **📋 Overview**

All infrastructure passwords are stored in AWS SSM Parameter Store with encryption. This guide covers access, rotation, and best practices.

## **🔑 Accessing Passwords**

### **Fetch Passwords from SSM Parameter Store**

```bash
# Get Wazuh admin password
aws ssm get-parameter \
  --name "/infodocs/wazuh/admin_password" \
  --with-decryption \
  --query 'Parameter.Value' \
  --output text

# Get OpenSearch master password
aws ssm get-parameter \
  --name "/infodocs/opensearch/master_password" \
  --with-decryption \
  --query 'Parameter.Value' \
  --output text

# Get all infrastructure passwords at once
aws ssm get-parameters-by-path \
  --path "/infodocs" \
  --recursive \
  --with-decryption \
  --query 'Parameters[?contains(Name, `password`)].{Name:Name,Value:Value}' \
  --output table
```

### **Login Helper Script**

```bash
#!/bin/bash
# Save as scripts/get-login-credentials.sh

echo "🔐 InfoDocs Infrastructure Login Credentials"
echo "============================================="

echo "Wazuh Admin:"
echo "Username: admin"
echo "Password: $(aws ssm get-parameter --name "/infodocs/wazuh/admin_password" --with-decryption --query 'Parameter.Value' --output text)"

echo -e "\nOpenSearch Admin:"
echo "Username: admin"
echo "Password: $(aws ssm get-parameter --name "/infodocs/opensearch/master_password" --with-decryption --query 'Parameter.Value' --output text)"

echo -e "\nCloudflare Zone ID: $(aws ssm get-parameter --name "/infodocs/cloudflare/zone_id" --query 'Parameter.Value' --output text)"
```

## **🔄 Password Rotation Strategy**

### **Recommended Rotation Schedule**
- **Security-critical passwords**: Every 3 months
- **API keys**: Every 6 months
- **Infrastructure passwords**: Every 6 months
- **Emergency rotation**: Immediately if compromised

### **Rotation Process**

1. **Generate new password**:
```bash
NEW_PASSWORD=$(openssl rand -base64 32)
echo "New password: $NEW_PASSWORD"
```

2. **Update SSM Parameter**:
```bash
aws ssm put-parameter \
  --name "/infodocs/wazuh/admin_password" \
  --value "$NEW_PASSWORD" \
  --type "SecureString" \
  --overwrite
```

3. **Update service configuration**:
   - Login to Wazuh/OpenSearch admin panel
   - Change password in the service
   - Verify new password works

4. **Update dependent systems**:
   - Any automation scripts
   - Monitoring systems
   - Backup processes

### **Automated Rotation Script**

```bash
#!/bin/bash
# Save as scripts/rotate-passwords.sh

rotate_password() {
    local service=$1
    local parameter_name=$2

    echo "🔄 Rotating password for $service..."

    # Generate new password
    NEW_PASSWORD=$(openssl rand -base64 32)

    # Update SSM Parameter
    aws ssm put-parameter \
      --name "$parameter_name" \
      --value "$NEW_PASSWORD" \
      --type "SecureString" \
      --overwrite

    echo "✅ Password updated in SSM Parameter Store"
    echo "⚠️  Manual step: Update password in $service admin panel"
    echo "New password: $NEW_PASSWORD"
}

# Rotate all infrastructure passwords
rotate_password "Wazuh" "/infodocs/wazuh/admin_password"
rotate_password "OpenSearch" "/infodocs/opensearch/master_password"
```

## **🛡️ Security Best Practices**

1. **Access Control**: Only authorized personnel can access SSM parameters
2. **Audit Trail**: All parameter access is logged in CloudTrail
3. **Encryption**: All passwords encrypted with AWS managed KMS keys
4. **No Hardcoding**: Never store passwords in code or configuration files
5. **Regular Rotation**: Follow the rotation schedule above
6. **Emergency Procedures**: Have a process for immediate password changes

## **📊 Password Inventory**

| Service | Parameter Path | Rotation Frequency | Last Rotated |
|---------|---------------|-------------------|--------------|
| Wazuh Admin | `/infodocs/wazuh/admin_password` | 3 months | Initial |
| Wazuh API | `/infodocs/wazuh/api_key` | 6 months | Initial |
| OpenSearch Master | `/infodocs/opensearch/master_password` | 3 months | Initial |
| OpenSearch API | `/infodocs/opensearch/api_key` | 6 months | Initial |
| Infrastructure DB Master | `/infodocs/infrastructure/database/master_password` | 6 months | Initial |
| Infrastructure DB App | `/infodocs/infrastructure/database/app_password` | 6 months | Initial |

**Note**: Infrastructure database parameters are separate from your existing RDS database to avoid conflicts.

## **🚨 Emergency Procedures**

If a password is compromised:

1. **Immediate rotation**:
```bash
./scripts/rotate-passwords.sh
```

2. **Check access logs**:
```bash
aws logs filter-log-events \
  --log-group-name "/aws/ssm/parameter-store" \
  --start-time $(date -d '24 hours ago' +%s)000
```

3. **Update all dependent systems immediately**
4. **Review and strengthen access controls**
5. **Document the incident**
