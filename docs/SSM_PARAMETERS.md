# Required SSM Parameters for InfoDocs Infrastructure

## 🔧 **AWS SSM Parameter Store Setup**

Run these commands to create all required parameters:

### **1. Cloudflare Configuration**
```bash
# Cloudflare API Token (Global API Key)
aws ssm put-parameter \
  --name "/infodocs/cloudflare/api_token" \
  --value "YOUR_CLOUDFLARE_GLOBAL_API_KEY" \
  --type "SecureString" \
  --description "Cloudflare Global API Key for DNS management"

# Cloudflare Zone ID
aws ssm put-parameter \
  --name "/infodocs/cloudflare/zone_id" \
  --value "YOUR_CLOUDFLARE_ZONE_ID" \
  --type "String" \
  --description "Cloudflare Zone ID for infodocs.co.za"

# Cloudflare Account ID
aws ssm put-parameter \
  --name "/infodocs/cloudflare/account_id" \
  --value "YOUR_CLOUDFLARE_ACCOUNT_ID" \
  --type "String" \
  --description "Cloudflare Account ID"
```

### **2. IAC Credentials (for Terragrunt)**
```bash
# IAC Access Key
aws ssm put-parameter \
  --name "/infodocs/iac/credentials/access_key" \
  --value "YOUR_IAC_ACCESS_KEY" \
  --type "SecureString" \
  --description "Access key for infrastructure automation"

# IAC Secret Key
aws ssm put-parameter \
  --name "/infodocs/iac/credentials/secret_key" \
  --value "YOUR_IAC_SECRET_KEY" \
  --type "SecureString" \
  --description "Secret key for infrastructure automation"
```

### **3. KMS Credentials**
```bash
# KMS Access Key
aws ssm put-parameter \
  --name "/infodocs/kms/credentials/access_key" \
  --value "YOUR_KMS_ACCESS_KEY" \
  --type "SecureString" \
  --description "Access key for KMS operations"

# KMS Secret Key
aws ssm put-parameter \
  --name "/infodocs/kms/credentials/secret_key" \
  --value "YOUR_KMS_SECRET_KEY" \
  --type "SecureString" \
  --description "Secret key for KMS operations"
```

### **4. Wazuh Configuration**
```bash
# Wazuh Admin Password
aws ssm put-parameter \
  --name "/infodocs/wazuh/admin_password" \
  --value "$(openssl rand -base64 32)" \
  --type "SecureString" \
  --description "Wazuh admin user password"

# Wazuh API Key
aws ssm put-parameter \
  --name "/infodocs/wazuh/api_key" \
  --value "$(openssl rand -hex 32)" \
  --type "SecureString" \
  --description "Wazuh API authentication key"

# Wazuh Registration Password
aws ssm put-parameter \
  --name "/infodocs/wazuh/registration_password" \
  --value "$(openssl rand -base64 16)" \
  --type "SecureString" \
  --description "Wazuh agent registration password"
```

### **5. OpenSearch Configuration**
```bash
# OpenSearch Master Password
aws ssm put-parameter \
  --name "/infodocs/opensearch/master_password" \
  --value "$(openssl rand -base64 32)" \
  --type "SecureString" \
  --description "OpenSearch master user password"

# OpenSearch API Key
aws ssm put-parameter \
  --name "/infodocs/opensearch/api_key" \
  --value "$(openssl rand -hex 32)" \
  --type "SecureString" \
  --description "OpenSearch API authentication key"
```

### **6. SSH Keys**
```bash
# SSH Public Key for Infrastructure
aws ssm put-parameter \
  --name "/infodocs/ssh/infrastructure_public_key" \
  --value "$(cat ~/.ssh/infodocs-dev-infrastructure.pub)" \
  --type "String" \
  --description "SSH public key for infrastructure access"

# SSH Private Key for Infrastructure (if needed for automation)
aws ssm put-parameter \
  --name "/infodocs/ssh/infrastructure_private_key" \
  --value "$(cat ~/.ssh/infodocs-dev-infrastructure)" \
  --type "SecureString" \
  --description "SSH private key for infrastructure automation"
```

### **7. Database Credentials (Future)**
```bash
# Database Master Password
aws ssm put-parameter \
  --name "/infodocs/database/master_password" \
  --value "$(openssl rand -base64 32)" \
  --type "SecureString" \
  --description "Database master password"

# Database Application Password
aws ssm put-parameter \
  --name "/infodocs/database/app_password" \
  --value "$(openssl rand -base64 32)" \
  --type "SecureString" \
  --description "Database application user password"
```

## 📋 **Verification Commands**

```bash
# List all InfoDocs parameters
aws ssm get-parameters-by-path \
  --path "/infodocs" \
  --recursive \
  --query 'Parameters[].Name' \
  --output table

# Verify parameter encryption
aws ssm get-parameter \
  --name "/infodocs/wazuh/admin_password" \
  --with-decryption \
  --query 'Parameter.Value' \
  --output text
```

## 🔒 **Security Notes**

- All sensitive parameters use `SecureString` type
- Parameters are encrypted with AWS managed KMS key
- Use `--with-decryption` flag when retrieving sensitive values
- Rotate passwords regularly (quarterly recommended)
- Monitor parameter access via CloudTrail

## 🚨 **Required Before Deployment**

1. ✅ Create Cloudflare API token
2. ✅ Generate SSH key pair
3. ✅ Run all SSM parameter commands above
4. ✅ Verify parameters are created
5. ✅ Update Bitbucket repository variables (see below)
