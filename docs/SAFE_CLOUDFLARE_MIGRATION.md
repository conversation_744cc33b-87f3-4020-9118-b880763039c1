# Safe Cloudflare Zero Trust Migration

This guide ensures you can safely migrate your existing manual Cloudflare setup to Infrastructure-as-Code without breaking anything.

## 🚨 **Risk Assessment**

### **What Could Break:**
- ❌ Existing `opensearch.infodocs.co.za` access
- ❌ Current WARP + SSO configuration
- ❌ DNS resolution for OpenSearch
- ❌ Access policies and user permissions

### **What We'll Protect:**
- ✅ Import existing resources instead of recreating
- ✅ Validate configuration before applying
- ✅ Provide rollback procedures
- ✅ Test each step incrementally

## 🛡️ **Safe Migration Strategy**

### **Phase 1: Discovery (No Risk)**
Discover what exists without making any changes:

```bash
# Install required tools
sudo yum install -y jq curl

# Discover existing Cloudflare configuration
./scripts/cloudflare-discovery.sh
```

**What this does:**
- ✅ Scans your Cloudflare account for existing resources
- ✅ Identifies potential conflicts
- ✅ **NO CHANGES MADE** - read-only operation

### **Phase 2: Backup (No Risk)**
Create backups of your current setup:

```bash
# Backup current DNS records
dig opensearch.infodocs.co.za ANY > opensearch_dns_backup_$(date +%Y%m%d).txt

# Export current Cloudflare settings (manual)
# 1. Go to Cloudflare Dashboard
# 2. Zero Trust → Access → Applications → Export settings
# 3. Save screenshots of current policies
```

### **Phase 3: Import Existing Resources (Low Risk)**
Bring existing resources under IaC management:

```bash
# Import existing resources into Terraform state
./scripts/cloudflare-import.sh
```

**What this does:**
- ✅ Imports existing resources into Terraform
- ✅ No changes to actual resources
- ✅ Makes Terraform aware of existing setup

### **Phase 4: Validate Configuration (Low Risk)**
Ensure IaC matches your existing setup:

```bash
# Check what changes would be made
cd environments/operations/af-south-1/cloudflare/access
terragrunt plan  # Should show "No changes" if import was successful

cd ../tunnel
terragrunt plan  # Check tunnel configuration
```

**Expected Results:**
- ✅ "No changes" = Perfect import
- ⚠️ "Changes detected" = Configuration needs adjustment

### **Phase 5: Deploy Missing Components (Medium Risk)**
Deploy only what doesn't exist:

```bash
# Deploy in order (only if plan shows it's needed)
cd environments/operations/af-south-1/cloudflare/access
terragrunt apply

cd ../tunnel
terragrunt apply
```

## 🔧 **Detailed Steps**

### **Step 1: Pre-Migration Checklist**

Before starting, verify:
- [ ] You have working access to `opensearch.infodocs.co.za`
- [ ] WARP is connected and working
- [ ] Google SSO authentication works
- [ ] You have Cloudflare dashboard access
- [ ] AWS CLI is configured with proper permissions

### **Step 2: Run Discovery**

```bash
./scripts/cloudflare-discovery.sh
```

**Possible Outcomes:**

#### **Scenario A: No Conflicts Found**
```
✅ No conflicts found! Safe to proceed with IaC deployment.
```
**Action:** Proceed with normal deployment

#### **Scenario B: Existing Resources Found**
```
⚠️ Found existing resources that may conflict:
OPENSEARCH_ACCESS_APP_ID=abc123
GOOGLE_WORKSPACE_IDP_ID=def456
```
**Action:** Proceed to import step

### **Step 3: Handle Existing Resources**

#### **Option 1: Import (Recommended)**
```bash
./scripts/cloudflare-import.sh
```

#### **Option 2: Manual Cleanup**
If import fails, manually delete conflicting resources:
1. Go to Cloudflare Dashboard
2. Zero Trust → Access → Applications
3. Delete OpenSearch application
4. Zero Trust → Settings → Authentication → Delete Google IDP
5. Then proceed with fresh deployment

### **Step 4: Validate Before Applying**

```bash
# Check access module
cd environments/operations/af-south-1/cloudflare/access
terragrunt plan

# Check tunnel module  
cd ../tunnel
terragrunt plan
```

**What to Look For:**
- ✅ `No changes` = Perfect
- ⚠️ `Plan: 0 to add, X to change, 0 to destroy` = Review changes
- ❌ `Plan: X to add, 0 to change, Y to destroy` = Stop and investigate

### **Step 5: Deploy Incrementally**

Deploy one module at a time:

```bash
# Deploy access policies first
cd environments/operations/af-south-1/cloudflare/access
terragrunt apply

# Test access still works
curl -I https://opensearch.infodocs.co.za

# Deploy tunnel
cd ../tunnel
terragrunt apply

# Test tunnel connectivity
```

## 🚨 **Rollback Procedures**

### **If Access Breaks:**

#### **Quick Fix:**
```bash
# Revert to manual configuration
cd environments/operations/af-south-1/cloudflare/access
terragrunt destroy

# Manually recreate in Cloudflare dashboard using backup screenshots
```

#### **DNS Issues:**
```bash
# Restore DNS record manually
# Use backup from opensearch_dns_backup_*.txt
```

### **If Tunnel Breaks:**

```bash
# Destroy tunnel
cd environments/operations/af-south-1/cloudflare/tunnel
terragrunt destroy

# Restore original DNS record pointing to bastion IP
# Use SSH tunnel as fallback:
ssh -i key.pem -L 8443:opensearch-endpoint:443 ec2-user@bastion-ip
```

## 🧪 **Testing Strategy**

### **After Each Phase:**

1. **Test WARP Connection:**
   ```bash
   # Check WARP status
   warp-cli status
   ```

2. **Test DNS Resolution:**
   ```bash
   dig opensearch.infodocs.co.za
   nslookup opensearch.infodocs.co.za
   ```

3. **Test Access:**
   ```bash
   curl -I https://opensearch.infodocs.co.za
   # Should return 302 redirect to Cloudflare Access
   ```

4. **Test Full Flow:**
   - Connect to WARP
   - Navigate to `https://opensearch.infodocs.co.za`
   - Authenticate with Google
   - Access OpenSearch

### **Validation Commands:**

```bash
# Check tunnel status (if deployed)
ssh -i key.pem ec2-user@bastion-ip 'sudo systemctl status cloudflared'

# Check tunnel logs
ssh -i key.pem ec2-user@bastion-ip 'sudo journalctl -u cloudflared -f'

# Test from different locations
curl -H "CF-Access-Client-Id: test" https://opensearch.infodocs.co.za
```

## 📞 **Emergency Contacts**

If something breaks:

1. **Immediate:** Revert using rollback procedures above
2. **DNS Issues:** Contact domain registrar if needed
3. **Cloudflare Issues:** Use Cloudflare support if you have a paid plan

## 💡 **Best Practices**

1. **Test During Low Usage:** Deploy during off-hours
2. **Have Backup Access:** Keep SSH tunnel method available
3. **Document Changes:** Note what you change manually
4. **Incremental Deployment:** One module at a time
5. **Validate Each Step:** Don't proceed if tests fail

## 🎯 **Success Criteria**

Migration is successful when:
- ✅ `opensearch.infodocs.co.za` works via WARP + SSO
- ✅ All resources are managed by Terraform
- ✅ `terragrunt plan` shows no unexpected changes
- ✅ Tunnel is running and healthy
- ✅ Access policies are correctly configured
