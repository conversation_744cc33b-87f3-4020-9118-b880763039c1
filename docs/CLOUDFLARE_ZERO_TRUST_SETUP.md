# Cloudflare Zero Trust Setup for OpenSearch

This guide walks you through setting up Cloudflare Zero Trust access for your OpenSearch instance, enabling secure access via `opensearch.infodocs.co.za` with Google Workspace SSO and WARP-only access.

## 🎯 **What This Achieves**

- **Zero Trust Access**: Only users connected to Cloudflare WARP can access OpenSearch
- **Single Sign-On**: Authenticate with your Google Workspace account
- **No VPN Required**: Secure access without traditional VPN infrastructure
- **Centralized Control**: Manage access policies in Cloudflare dashboard

## 📋 **Prerequisites**

1. **Cloudflare Account** with Zero Trust plan
2. **Google Workspace** domain (infodocs.co.za)
3. **Deployed Infrastructure**: OpenSearch and bastion host must be running
4. **AWS CLI** configured with appropriate permissions

## 🚀 **Step-by-Step Setup**

### **Step 1: Configure SSM Parameters**

Run the setup script to configure required parameters:

```bash
./scripts/setup-cloudflare-zero-trust.sh
```

This will prompt you for:
- Cloudflare Account ID
- Cloudflare Team Name
- Google OAuth Client ID & Secret
- Google Workspace Domain

### **Step 2: Create Google OAuth Credentials**

1. Go to [Google Cloud Console](https://console.cloud.google.com/apis/credentials)
2. Create **OAuth 2.0 Client ID**
3. Application type: **Web application**
4. Authorized redirect URIs: `https://<your-team>.cloudflareaccess.com/cdn-cgi/access/callback`
   - Replace `<your-team>` with your Cloudflare team name

### **Step 3: Deploy Cloudflare Access Module**

```bash
cd environments/operations/af-south-1/cloudflare/access
terragrunt apply
```

This creates:
- Google Workspace identity provider
- Access application for OpenSearch
- Access policies (WARP + Google SSO required)

### **Step 4: Deploy Cloudflare Tunnel Module**

```bash
cd environments/operations/af-south-1/cloudflare/tunnel
terragrunt apply
```

This creates:
- Cloudflare Tunnel
- DNS record pointing to tunnel
- SSM parameters for tunnel configuration

### **Step 5: Update DNS Configuration**

Remove the old opensearch DNS record:

```bash
cd environments/operations/af-south-1/network/dns/cloudflare
terragrunt apply
```

### **Step 6: Configure Bastion Host**

Update bastion host to support tunnel:

```bash
cd environments/operations/af-south-1/ec2/bastion
terragrunt apply
```

### **Step 7: Setup Tunnel on Bastion Host**

Configure the tunnel daemon:

```bash
./scripts/configure-cloudflare-tunnel.sh
```

## 🔧 **Configuration Details**

### **Access Flow**
```
User → WARP Connection → opensearch.infodocs.co.za → Cloudflare Access → Google SSO → OpenSearch
```

### **Security Policies**
- **WARP Required**: Users must be connected to Cloudflare WARP
- **Domain Restriction**: Only @infodocs.co.za Google accounts
- **Session Duration**: 24 hours
- **Deny All Others**: Explicit deny for non-matching requests

### **Network Architecture**
```
Internet → Cloudflare Edge → Tunnel → Bastion Host → OpenSearch (VPC)
```

## 🧪 **Testing the Setup**

### **Step 1: Install Cloudflare WARP**
1. Download from [Cloudflare WARP](https://*******/)
2. Install and sign up
3. Connect to WARP

### **Step 2: Test Access**
1. Navigate to: `https://opensearch.infodocs.co.za`
2. You should see Cloudflare Access login page
3. Click "Login with Google"
4. Authenticate with your @infodocs.co.za account
5. Access OpenSearch Dashboards

### **Step 3: Verify Security**
- Disconnect from WARP → Access should be denied
- Try from different Google account → Access should be denied
- Check tunnel status: `ssh bastion 'sudo systemctl status cloudflared'`

## 🛠️ **Troubleshooting**

### **Common Issues**

#### **"Access Denied" Error**
- Ensure you're connected to Cloudflare WARP
- Verify your Google account domain is @infodocs.co.za
- Check if your email is in the allowed list

#### **"Tunnel Not Found" Error**
- Verify tunnel is running: `sudo systemctl status cloudflared`
- Check tunnel logs: `sudo journalctl -u cloudflared -f`
- Restart tunnel: `sudo systemctl restart cloudflared`

#### **DNS Resolution Issues**
- Verify DNS record points to tunnel CNAME
- Check Cloudflare proxy status (should be orange cloud)
- Clear DNS cache: `sudo systemctl flush-dns`

### **Useful Commands**

```bash
# Check tunnel status
ssh -i key.pem ec2-user@bastion-ip 'sudo systemctl status cloudflared'

# View tunnel logs
ssh -i key.pem ec2-user@bastion-ip 'sudo journalctl -u cloudflared -f'

# Restart tunnel
ssh -i key.pem ec2-user@bastion-ip 'sudo systemctl restart cloudflared'

# Test DNS resolution
dig opensearch.infodocs.co.za

# Check access policies
# (Use Cloudflare dashboard: Zero Trust → Access → Applications)
```

## 📊 **Monitoring & Maintenance**

### **Cloudflare Dashboard**
- **Zero Trust → Access → Applications**: Manage access policies
- **Zero Trust → Logs**: View authentication attempts
- **Zero Trust → Analytics**: Monitor usage patterns

### **AWS CloudWatch**
- Bastion host logs: `/aws/ec2/bastion`
- Tunnel service logs via CloudWatch agent

### **Regular Tasks**
- Review access logs monthly
- Update allowed user list as needed
- Monitor tunnel uptime
- Rotate Google OAuth credentials annually

## 🔒 **Security Considerations**

- **Principle of Least Privilege**: Only grant access to necessary users
- **Regular Audits**: Review access logs and user permissions
- **Backup Access**: Maintain SSH tunnel capability as fallback
- **Certificate Management**: Cloudflare handles SSL/TLS automatically
- **Network Isolation**: OpenSearch remains in private VPC

## 📞 **Support**

If you encounter issues:
1. Check the troubleshooting section above
2. Review Cloudflare Zero Trust documentation
3. Check AWS CloudWatch logs
4. Verify all SSM parameters are correctly set
