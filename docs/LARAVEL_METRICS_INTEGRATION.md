# Laravel Metrics Integration with CloudWatch

This guide shows how to integrate your Laravel application with CloudWatch to send custom metrics that will be visualized in Grafana.

## 1. Install AWS SDK for Laravel

```bash
composer require aws/aws-sdk-php
```

## 2. Configure AWS Credentials

Add to your `.env` file:
```env
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_DEFAULT_REGION=af-south-1
AWS_CLOUDWATCH_NAMESPACE=InfoDocs/Laravel
```

## 3. Create CloudWatch Service

Create `app/Services/CloudWatchService.php`:

```php
<?php

namespace App\Services;

use Aws\CloudWatch\CloudWatchClient;
use Illuminate\Support\Facades\Log;

class CloudWatchService
{
    private $cloudWatch;
    private $namespace;

    public function __construct()
    {
        $this->cloudWatch = new CloudWatchClient([
            'version' => 'latest',
            'region' => config('aws.region', 'af-south-1'),
            'credentials' => [
                'key' => config('aws.access_key_id'),
                'secret' => config('aws.secret_access_key'),
            ],
        ]);

        $this->namespace = config('aws.cloudwatch_namespace', 'InfoDocs/Laravel');
    }

    public function putMetric($metricName, $value, $unit = 'Count', $dimensions = [])
    {
        try {
            $metricData = [
                'Namespace' => $this->namespace,
                'MetricData' => [
                    [
                        'MetricName' => $metricName,
                        'Value' => $value,
                        'Unit' => $unit,
                        'Timestamp' => time(),
                        'Dimensions' => array_merge([
                            [
                                'Name' => 'Environment',
                                'Value' => config('app.env')
                            ]
                        ], $dimensions)
                    ]
                ]
            ];

            $this->cloudWatch->putMetricData($metricData);
        } catch (\Exception $e) {
            Log::error('CloudWatch metric failed: ' . $e->getMessage());
        }
    }

    public function incrementCounter($metricName, $dimensions = [])
    {
        $this->putMetric($metricName, 1, 'Count', $dimensions);
    }

    public function recordTime($metricName, $milliseconds, $dimensions = [])
    {
        $this->putMetric($metricName, $milliseconds, 'Milliseconds', $dimensions);
    }

    public function recordMemory($metricName, $bytes, $dimensions = [])
    {
        $this->putMetric($metricName, $bytes, 'Bytes', $dimensions);
    }
}
```

## 4. Create Middleware for Request Metrics

Create `app/Http/Middleware/MetricsMiddleware.php`:

```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Services\CloudWatchService;

class MetricsMiddleware
{
    private $cloudWatch;

    public function __construct(CloudWatchService $cloudWatch)
    {
        $this->cloudWatch = $cloudWatch;
    }

    public function handle(Request $request, Closure $next)
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);

        $response = $next($request);

        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);

        // Calculate metrics
        $responseTime = ($endTime - $startTime) * 1000; // Convert to milliseconds
        $memoryUsed = $endMemory - $startMemory;

        // Send metrics to CloudWatch
        $dimensions = [
            [
                'Name' => 'Route',
                'Value' => $request->route() ? $request->route()->getName() ?? 'unnamed' : 'unknown'
            ],
            [
                'Name' => 'Method',
                'Value' => $request->method()
            ],
            [
                'Name' => 'StatusCode',
                'Value' => (string) $response->getStatusCode()
            ]
        ];

        // Request count
        $this->cloudWatch->incrementCounter('RequestCount', $dimensions);

        // Response time
        $this->cloudWatch->recordTime('ResponseTime', $responseTime, $dimensions);

        // Memory usage
        $this->cloudWatch->recordMemory('MemoryUsage', $memoryUsed, $dimensions);

        // Error tracking
        if ($response->getStatusCode() >= 400) {
            $this->cloudWatch->incrementCounter('ErrorCount', $dimensions);
        }

        return $response;
    }
}
```

## 5. Register Middleware

In `app/Http/Kernel.php`, add to the `$middleware` array:

```php
protected $middleware = [
    // ... other middleware
    \App\Http\Middleware\MetricsMiddleware::class,
];
```

## 6. Create Custom Metrics for Business Logic

Create `app/Traits/HasMetrics.php`:

```php
<?php

namespace App\Traits;

use App\Services\CloudWatchService;

trait HasMetrics
{
    protected function recordMetric($metricName, $value, $unit = 'Count', $dimensions = [])
    {
        $cloudWatch = app(CloudWatchService::class);
        $cloudWatch->putMetric($metricName, $value, $unit, $dimensions);
    }

    protected function incrementMetric($metricName, $dimensions = [])
    {
        $cloudWatch = app(CloudWatchService::class);
        $cloudWatch->incrementCounter($metricName, $dimensions);
    }
}
```

## 7. Use Metrics in Your Controllers

```php
<?php

namespace App\Http\Controllers;

use App\Traits\HasMetrics;
use Illuminate\Http\Request;

class DocumentController extends Controller
{
    use HasMetrics;

    public function store(Request $request)
    {
        $startTime = microtime(true);

        try {
            // Your business logic here
            $document = Document::create($request->all());

            // Record successful document creation
            $this->incrementMetric('DocumentCreated', [
                [
                    'Name' => 'DocumentType',
                    'Value' => $document->type
                ]
            ]);

            $processingTime = (microtime(true) - $startTime) * 1000;
            $this->recordMetric('DocumentProcessingTime', $processingTime, 'Milliseconds');

            return response()->json($document, 201);

        } catch (\Exception $e) {
            // Record error
            $this->incrementMetric('DocumentCreationError', [
                [
                    'Name' => 'ErrorType',
                    'Value' => get_class($e)
                ]
            ]);

            throw $e;
        }
    }
}
```

## 8. Queue Job Metrics

Create `app/Jobs/MetricAwareJob.php`:

```php
<?php

namespace App\Jobs;

use App\Services\CloudWatchService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

abstract class MetricAwareJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $cloudWatch;

    public function __construct()
    {
        $this->cloudWatch = app(CloudWatchService::class);
    }

    public function handle()
    {
        $startTime = microtime(true);

        try {
            $this->execute();

            $processingTime = (microtime(true) - $startTime) * 1000;
            $this->cloudWatch->recordTime('QueueJobProcessingTime', $processingTime, [
                [
                    'Name' => 'JobClass',
                    'Value' => get_class($this)
                ]
            ]);

            $this->cloudWatch->incrementCounter('QueueJobsProcessed', [
                [
                    'Name' => 'JobClass',
                    'Value' => get_class($this)
                ]
            ]);

        } catch (\Exception $e) {
            $this->cloudWatch->incrementCounter('QueueJobsFailed', [
                [
                    'Name' => 'JobClass',
                    'Value' => get_class($this)
                ],
                [
                    'Name' => 'ErrorType',
                    'Value' => get_class($e)
                ]
            ]);

            throw $e;
        }
    }

    abstract protected function execute();
}
```

## 9. Database Query Metrics

Create an event listener for database queries in `app/Providers/AppServiceProvider.php`:

```php
use Illuminate\Support\Facades\DB;
use App\Services\CloudWatchService;

public function boot()
{
    if (config('app.env') === 'production') {
        DB::listen(function ($query) {
            $cloudWatch = app(CloudWatchService::class);

            $cloudWatch->recordTime('DatabaseQueryTime', $query->time, [
                [
                    'Name' => 'Connection',
                    'Value' => $query->connectionName
                ]
            ]);

            $cloudWatch->incrementCounter('DatabaseQueryCount', [
                [
                    'Name' => 'Connection',
                    'Value' => $query->connectionName
                ]
            ]);
        });
    }
}
```

## 10. Custom Business Metrics

Add business-specific metrics throughout your application:

```php
// User registration
$this->incrementMetric('UserRegistered', [
    ['Name' => 'RegistrationMethod', 'Value' => 'email']
]);

// Document processing
$this->recordMetric('DocumentSize', $document->size_bytes, 'Bytes');

// Payment processing
$this->recordMetric('PaymentAmount', $payment->amount, 'None', [
    ['Name' => 'Currency', 'Value' => $payment->currency]
]);

// API usage
$this->incrementMetric('APICall', [
    ['Name' => 'Endpoint', 'Value' => $request->path()],
    ['Name' => 'ClientId', 'Value' => $request->user()->client_id]
]);
```

## 11. Configuration

Add to `config/aws.php`:

```php
<?php

return [
    'access_key_id' => env('AWS_ACCESS_KEY_ID'),
    'secret_access_key' => env('AWS_SECRET_ACCESS_KEY'),
    'region' => env('AWS_DEFAULT_REGION', 'af-south-1'),
    'cloudwatch_namespace' => env('AWS_CLOUDWATCH_NAMESPACE', 'InfoDocs/Laravel'),
];
```

## 12. Testing Metrics

Create a test command to verify metrics are working:

```php
<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\CloudWatchService;

class TestMetrics extends Command
{
    protected $signature = 'metrics:test';
    protected $description = 'Test CloudWatch metrics integration';

    public function handle(CloudWatchService $cloudWatch)
    {
        $this->info('Sending test metrics to CloudWatch...');

        $cloudWatch->incrementCounter('TestMetric');
        $cloudWatch->recordTime('TestResponseTime', 150);
        $cloudWatch->recordMemory('TestMemoryUsage', 1024 * 1024);

        $this->info('Test metrics sent successfully!');
    }
}
```

Run with: `php artisan metrics:test`

## Metrics That Will Be Available in Grafana

- **RequestCount**: Number of HTTP requests
- **ResponseTime**: Average response time per request
- **ErrorCount**: Number of HTTP errors (4xx, 5xx)
- **MemoryUsage**: Memory usage per request
- **DatabaseQueryTime**: Database query execution time
- **DatabaseQueryCount**: Number of database queries
- **QueueJobsProcessed**: Successfully processed queue jobs
- **QueueJobsFailed**: Failed queue jobs
- **DocumentCreated**: Business metric for document creation
- **UserRegistered**: Business metric for user registrations
- **PaymentAmount**: Business metric for payment processing

These metrics will automatically appear in your Grafana dashboards and can be used for alerting.
