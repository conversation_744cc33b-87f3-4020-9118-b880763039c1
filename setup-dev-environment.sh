#!/bin/bash
# setup-dev-environment.sh
# InfoDocs Infrastructure as Code Development Environment Setup

set -e

echo "🚀 Setting up InfoDocs Infrastructure as Code Environment..."
echo "=========================================================="

# Detect OS
OS="$(uname -s)"
case "${OS}" in
    Darwin*)    OS_TYPE=Mac;;
    Linux*)     OS_TYPE=Linux;;
    *)          echo "❌ Unsupported OS: ${OS}"; exit 1;;
esac

# Install package manager if needed
install_package_manager() {
    echo "🔧 Checking package manager..."
    if [[ "$OS_TYPE" == "Mac" ]]; then
        if ! command -v brew &> /dev/null; then
            echo "Installing Homebrew..."
            /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
        else
            echo "✅ Homebrew already installed"
        fi
    elif [[ "$OS_TYPE" == "Linux" ]]; then
        if command -v apt-get &> /dev/null; then
            echo "Updating apt..."
            sudo apt-get update
        elif command -v yum &> /dev/null; then
            echo "Updating yum..."
            sudo yum update -y
        fi
    fi
}

# Install OpenTofu
install_opentofu() {
    echo "🔧 Installing OpenTofu..."
    if [[ "$OS_TYPE" == "Mac" ]]; then
        if ! command -v tofu &> /dev/null; then
            brew install opentofu/tap/opentofu
        else
            echo "✅ OpenTofu already installed"
        fi
    elif [[ "$OS_TYPE" == "Linux" ]]; then
        if ! command -v tofu &> /dev/null; then
            TOFU_VERSION="1.8.0"
            wget -O tofu.zip "https://github.com/opentofu/opentofu/releases/download/v${TOFU_VERSION}/tofu_${TOFU_VERSION}_linux_amd64.zip"
            unzip tofu.zip
            sudo mv tofu /usr/local/bin/
            rm tofu.zip
        else
            echo "✅ OpenTofu already installed"
        fi
    fi
}

# Install Terragrunt
install_terragrunt() {
    echo "🔧 Installing Terragrunt..."
    if [[ "$OS_TYPE" == "Mac" ]]; then
        if ! command -v terragrunt &> /dev/null; then
            brew install terragrunt@0.54.0
        else
            echo "✅ Terragrunt already installed"
        fi
    elif [[ "$OS_TYPE" == "Linux" ]]; then
        if ! command -v terragrunt &> /dev/null; then
            TERRAGRUNT_VERSION="v0.54.0"
            wget -O terragrunt "https://github.com/gruntwork-io/terragrunt/releases/download/${TERRAGRUNT_VERSION}/terragrunt_linux_amd64"
            chmod +x terragrunt
            sudo mv terragrunt /usr/local/bin/
        else
            echo "✅ Terragrunt already installed"
        fi
    fi
}

# Install AWS CLI
install_aws_cli() {
    echo "🔧 Installing AWS CLI..."
    if ! command -v aws &> /dev/null; then
        if [[ "$OS_TYPE" == "Mac" ]]; then
            brew install awscli
        elif [[ "$OS_TYPE" == "Linux" ]]; then
            curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
            unzip awscliv2.zip
            sudo ./aws/install
            rm -rf aws awscliv2.zip
        fi
    else
        echo "✅ AWS CLI already installed"
    fi
}

# Install kubectl
install_kubectl() {
    echo "🔧 Installing kubectl..."
    if ! command -v kubectl &> /dev/null; then
        if [[ "$OS_TYPE" == "Mac" ]]; then
            brew install kubectl
        elif [[ "$OS_TYPE" == "Linux" ]]; then
            KUBECTL_VERSION=$(curl -L -s https://dl.k8s.io/release/stable.txt)
            curl -LO "https://dl.k8s.io/release/${KUBECTL_VERSION}/bin/linux/amd64/kubectl"
            chmod +x kubectl
            sudo mv kubectl /usr/local/bin/
        fi
    else
        echo "✅ kubectl already installed"
    fi
}

# Install Helm
install_helm() {
    echo "🔧 Installing Helm..."
    if ! command -v helm &> /dev/null; then
        if [[ "$OS_TYPE" == "Mac" ]]; then
            brew install helm
        elif [[ "$OS_TYPE" == "Linux" ]]; then
            curl -fsSL -o get_helm.sh https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3
            chmod 700 get_helm.sh
            ./get_helm.sh
            rm get_helm.sh
        fi
    else
        echo "✅ Helm already installed"
    fi
}

# Install Docker
install_docker() {
    echo "🔧 Installing Docker..."
    if ! command -v docker &> /dev/null; then
        if [[ "$OS_TYPE" == "Mac" ]]; then
            brew install --cask docker
            echo "⚠️ Please open Docker Desktop to complete setup"
        elif [[ "$OS_TYPE" == "Linux" ]]; then
            sudo apt-get update
            sudo apt-get install -y apt-transport-https ca-certificates curl software-properties-common
            curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo apt-key add -
            sudo add-apt-repository "deb [arch=amd64] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable"
            sudo apt-get update
            sudo apt-get install -y docker-ce
            sudo usermod -aG docker $USER
            echo "⚠️ IMPORTANT: Log out and log back in to use Docker without sudo"
        fi
    else
        echo "✅ Docker already installed"
    fi
}

# Install additional tools
install_additional_tools() {
    echo "🔧 Installing additional tools..."
    if [[ "$OS_TYPE" == "Mac" ]]; then
        brew install jq yq git-crypt pre-commit terraform-docs sops trivy
        if ! command -v pipx &> /dev/null; then brew install pipx; pipx ensurepath; else echo "✅ pipx already installed"; fi
        if ! command -v detect-secrets &> /dev/null; then pipx install detect-secrets; pipx ensurepath; else echo "✅ detect-secrets already installed"; fi
    elif [[ "$OS_TYPE" == "Linux" ]]; then
        if command -v apt-get &> /dev/null; then sudo apt-get install -y jq git-crypt python3-pip curl wget unzip; fi
        if ! command -v yq &> /dev/null; then YQ_VERSION="v4.35.2"; wget -O yq "https://github.com/mikefarah/yq/releases/download/${YQ_VERSION}/yq_linux_amd64"; chmod +x yq; sudo mv yq /usr/local/bin/; else echo "✅ yq already installed"; fi
        if ! command -v pre-commit &> /dev/null; then pip3 install pre-commit; else echo "✅ pre-commit already installed"; fi
        if ! command -v pipx &> /dev/null; then pip3 install pipx; pipx ensurepath; else echo "✅ pipx already installed"; fi
        if ! command -v detect-secrets &> /dev/null; then pipx install detect-secrets; else echo "✅ detect-secrets already installed"; fi
        if ! command -v terraform-docs &> /dev/null; then TERRAFORM_DOCS_VERSION="v0.16.0"; wget -O terraform-docs.tar.gz "https://terraform-docs.io/dl/${TERRAFORM_DOCS_VERSION}/terraform-docs-${TERRAFORM_DOCS_VERSION}-linux-amd64.tar.gz"; tar -xzf terraform-docs.tar.gz; chmod +x terraform-docs; sudo mv terraform-docs /usr/local/bin/; rm terraform-docs.tar.gz; else echo "✅ terraform-docs already installed"; fi
        if ! command -v sops &> /dev/null; then curl -sSLo sops https://github.com/mozilla/sops/releases/download/v3.8.1/sops-v3.8.1.linux.amd64; chmod +x sops; sudo mv sops /usr/local/bin/; else echo "✅ sops already installed"; fi
        if ! command -v trivy &> /dev/null; then TRIVY_VERSION="v0.53.0"; wget -O trivy.tar.gz "https://github.com/aquasecurity/trivy/releases/download/${TRIVY_VERSION}/trivy_${TRIVY_VERSION}_Linux-64bit.tar.gz"; tar -xzf trivy.tar.gz trivy; chmod +x trivy; sudo mv trivy /usr/local/bin/; rm trivy.tar.gz; else echo "✅ trivy already installed"; fi
    fi
}

# Setup Git hooks
setup_git_hooks() {
    echo "🔧 Setting up Git hooks..."
    if [ -f ".pre-commit-config.yaml" ]; then
        pre-commit install
        echo "✅ Git hooks installed"
    else
        echo "⚠️ .pre-commit-config.yaml not found, skipping hook installation"
    fi
}

# Create AWS credentials template
create_aws_credentials_template() {
    echo "🔧 Checking AWS credentials..."
    AWS_DIR="$HOME/.aws"
    mkdir -p "$AWS_DIR"
    if [ ! -f "$AWS_DIR/credentials" ]; then
        cat > "$AWS_DIR/credentials" << EOF
# AWS Credentials for InfoDocs IAC
# Replace with your actual credentials for local development
# For pipelines, use AWS SSM Parameter Store with KMS encryption
[default]
aws_access_key_id = YOUR_ACCESS_KEY_HERE
aws_secret_access_key = YOUR_SECRET_KEY_HERE
region = af-south-1

[infodocs-main]
aws_access_key_id = YOUR_ACCESS_KEY_HERE
aws_secret_access_key = YOUR_SECRET_KEY_HERE
region = af-south-1
EOF
        echo "📝 AWS credentials template created at $AWS_DIR/credentials"
        echo "⚠️ Please update with your actual credentials for local use"
        echo "⚠️ For pipelines, store credentials in SSM at /infodocs/iac/credentials/*"
    else
        echo "✅ AWS credentials file already exists"
    fi
}

# Create helper scripts
create_helper_scripts() {
    echo "🔧 Creating helper scripts..."
    mkdir -p scripts
    cat > scripts/route53-to-cloudflare.sh << 'EOF'
#!/bin/bash
# Route53 to Cloudflare DNS migration script
AWS_PROFILE="default"
DOMAIN="infodocs.co.za"
OUTPUT_DIR="./cloudflare-migration"

mkdir -p $OUTPUT_DIR

# Set AWS profile
export AWS_PROFILE=$AWS_PROFILE
echo "Using AWS Profile: $AWS_PROFILE"

# Verify AWS identity
echo "Verifying AWS identity..."
aws stscli get-caller-identity

# Get hosted zone ID
echo "Finding hosted Hosted zone ID for $DOMAIN..."
HOSTED_ZONE_ID=$(aws route53 list-hosted-zones --query "HostedZones[?Name=='$DOMAIN\.'].Id" | jq -r -output text | sed 's/\/hostedzone\///')
if [ -z "$HOSTED_ZONE_ID" ]; then
  echo "Error: Hosted zone for $DOMAIN not found"
  exit 1
fi

echo "Found hosted zone ID: $HOSTED_ZONE_ID"

# Export records
echo "Exporting DNS records..."
aws route53 list-resource-record-sets --hosted-zone-id $HOSTED_ZONE_ID > "$OUTPUT_DIR/route53_records.json"

# Convert to Terraform format
echo "Converting to Terraform format..."
cat > "$OUTPUT_DIR/cloudflare_records.tf" << TFEOF
# Cloudflare DNS records for $DOMAIN
# Generated from Route53 export

resource "cloudflare_zone" "${DOMAIN//./_}" {
  zone = "$DOMAIN"
}

TFEOF

jq -c '.ResourceRecordSets[]' "$OUTPUT_DIR/route53_records.json" | while read -r record; do
  NAME=$(echo "$record" | jq -r '.Name' | sed "s/\\.$DOMAIN\\.//g;s/\.$//")
  TYPE=$(echo "$record" | jq -r '.Type')
  if [[ "$TYPE" == "SOA" ]] || ([[ "$TYPE" == "NS" ]] && [[ "$NAME" == "" ]]); then
    continue
  fi

  if [[ "$TYPE" == "A" || "$TYPE" == "AAAA" || "$TYPE" == "CNAME" || "$TYPE" == "TXT" ]]; then
    VALUES=$(echo "$record" | jq -r '.ResourceRecords[].Value')
    echo "$VALUES" | while read -r value; do
      if [[ "$TYPE" == "TXT" ]]; then
        value=$(echo "$value" | sed 's/^"//g;s/"$//g')
      fi

      RESOURCE_NAME=$(echo "${NAME:-${TYPE}" | tr '.-' '__' | tr '[:upper:]' '[:lower:]')

      cat >> "$OUTPUT_DIR/cloudflare_records.tf" << TFEOF
resource "cloudflare_record" "${RESOURCE_NAME}_$(uuidgen | cut -d'-' -f1)" {
  zone_id = cloudflare_zone.${DOMAIN//./_}.id
  name    = "${NAME:-@}"
  type    = "$TYPE"
  value   = "$value"
  ttl     = ${TTL}
  proxied = false
}

TFEOF
    done
  elif [[ "$TYPE" == "MX" ]]; then
    echo "$record" | jq -r '.ResourceRecords[]' | while read -r mx_record; do
      PRIORITY=$(echo "$mx_record" | jq -r '.Value' | cut -d' ' -f1)
      VALUE=$(echo "$mx_record" | jq -r '.Value' | cut -d' ' -f2-)

      RESOURCE_NAME=$(echo "${NAME:-mx_${VALUE//./_}" | tr '.-' '__' | tr '[:upper:]' '[:lower:]')

      cat >> "$OUTPUT_DIR/cloudflare_records.tf" << TFEOF
resource "cloudflare_record" "${RESOURCE_NAME}_$(uuidgen | cut -d'-' -f1)" {
  zone_id  = cloudflare_zone.${DOMAIN//./_}.id
  name    = "${NAME:-@}"
  type    = "MX"
  value   = "${PRIORITY} ${VALUE}"
  ttl     = ${TTL}
  proxied = false
}

TFEOF
    done
done

echo "DNS migration completed! Check $OUTPUT_DIR/cloudflare_records.tf for the generated Terraform code."
EOF
    chmod +x scripts/route53-to-cloudflare.sh
}

# Verify installations
verify_installations() {
    echo ""
    echo "🔍 Verifying installations..."
    echo "============================="
    tools=("tofu" "terragrunt" "aws" "kubectl" "helm" "docker" "jq" "yq" "pre-commit" "detect-secrets" "terraform-docs" "sops" "trivy")
    for tool in "${tools[@]}"; do
        if command -v "$tool" &> /dev/null; then
            case ${tool} in
                "tofu") version=$(tofu version | head -n1 | cut -d' ' -f2); ;;
                "terragrunt") version=$(terragrunt --version | head -n1); ;;
                "aws") version=$(aws --version | cut -d' ' -f1); ;;
                "kubectl") version=$(kubectl version --client --short | cut -d' ' -f3 || echo "installed"); ;;
                "helm") version=$(helm version --short | cut -d':' -f2); ;;
                "docker") version=$(docker --version | cut -d' ' -f3 | tr -d ','); ;;
                "jq") version=$(jq --version); ;;
                "yq") version=$(yq --version); ;;
                "pre-commit") version=$(pre-commit; --version); ;;
                "detect-secrets") version=$(detect-secrets --version); ;;
                "terraform-docs") version=$(terraform-docs --version); ;;
                "sops") version=$(sops --version); ;;
                "trivy") version=$(trivy --version); ;;
                *) version="installed"; ;;
            esac
            echo "✅ $tool: $version"
        else
            echo "❌ $tool: not found"
        fi
    done
}

# Main execution
main() {
    echo ""
    echo "Starting InfoDocs IAC development environment setup..."
    echo ""
    install_package_manager
    install_opentofu
    install_terragrunt
    install_aws_cli
    install_kubectl
    install_helm
    install_docker
    install_additional_tools
    setup_git_hooks
    create_aws_credentials_template
    create_helper_scripts
    verify_installations
    echo ""
    echo "🎉 Development environment setup complete!"
    echo "=========================================="
    echo "📋 Next steps:"
    echo "1. Update your AWS credentials in ~/.aws/credentials (for local use)"
    echo "2. Store pipeline credentials in AWS SSM at /infodocs/iac/credentials/*"
    echo "3. Run 'aws configure' to set up your default profile"
    echo "4. Test AWS connection: aws sts get-caller-identity"
    echo "5. Navigate to a module directory and run: terragrunt init"
    echo "6. Deploy infrastructure: terragrunt plan && terragrunt apply"
    echo ""
    echo "🚀 Ready to build infrastructure!"
    if [[ "$OS_TYPE" == "Linux" ]] && groups $USER | grep -q docker; then
        echo ""
        echo "⚠️ IMPORTANT: Log out and log back in for Docker group membership to take effect"
    fi
    if [[ "$OS_TYPE" == "Mac" ]]; then
        echo ""
        echo "🔧 Updating PATH for current session..."
        export PATH="$HOME/.local/bin:$PATH"
    fi
}

main "$@"
