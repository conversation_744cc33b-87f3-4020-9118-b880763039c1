# InfoDocs Infrastructure as Code

This repository implements Infrastructure as Code (IaC) for the InfoDocs platform using **OpenTofu** and **Terragrunt**. It manages AWS infrastructure, including networking, security, monitoring, and compliance, across African regions with secure secrets management via AWS SSM Parameter Store.

## ✅ Recent Updates & Improvements

- **Grafana Cloud Monitoring**: Complete Infrastructure as Code monitoring solution with dashboards, alerting, and Slack integration
- **OpenTofu Migration**: Fully migrated from Terraform to OpenTofu v1.8.0
- **Enhanced Pipeline**: Complete CI/CD pipeline covering all modules with proper deployment order
- **Consistent Structure**: Standardized module structure across all environments
- **Security Improvements**: Updated provider configurations and version constraints
- **Complete Module Coverage**: All environments now have consistent module deployments

## 🎓 Getting Started with Infrastructure as Code

### What is Infrastructure as Code (IaC)?
IaC manages cloud infrastructure (e.g., servers, networks) through code rather than manual configuration, ensuring consistency, version control, and automation.

### Why Use IaC?
- **Reproducibility**: Identical environments every time
- **Version Control**: Track changes in Git
- **Automation**: Eliminate manual errors
- **Collaboration**: Team-reviewed changes
- **Recovery**: Rebuild from code post-disaster

### Key Tools
- **OpenTofu v1.8.0**: Open-source Terraform fork that defines infrastructure in `.tf` files
- **Terragrunt v0.54.0**: Organizes multi-environment configurations and DRY principles
- **AWS Provider v5.0+**: Deploys resources to AWS with latest features
- **Cloudflare Provider v4.0**: Manages DNS and security settings

### Repository Structure
```
infrastructure-as-code/
├── README.md                  # Project overview
├── terragrunt.hcl             # Root configuration
├── setup-dev-environment.sh   # Environment setup script
├── bitbucket-pipelines.yml    # CI/CD pipeline configuration
├── .secrets.baseline          # Secret detection baseline
├── .pre-commit-config.yaml    # Pre-commit hooks configuration
├── .gitignore                 # Git ignore patterns
├── modules/                   # Reusable modules
│   ├── network/vpc/          # Networking module
│   ├── dns/cloudflare/       # DNS/WAF module
│   ├── analytics/            # Analytics & monitoring modules
│   │   ├── opensearch/      # Log analytics platform
│   │   └── grafana/         # Grafana Cloud monitoring
│   ├── security/             # Security modules
│   │   ├── wazuh/           # Security monitoring
│   │   ├── kms/             # Encryption keys
│   │   └── ssm/             # Parameter store
│   ├── s3/                   # Storage modules
│   │   ├── wazuh-bucket/    # Security logs
│   │   ├── opensearch-bucket/ # Application logs
│   │   └── legal-hold-bucket/ # Compliance storage
│   ├── ec2/                  # Compute modules
│   │   ├── bastion/         # Secure access
│   │   ├── kali-linux/      # Penetration testing
│   │   └── parrot-os/       # Privacy-focused testing
│   ├── lambda/               # Serverless modules
│   │   ├── wazuh-lambda-log-shipper/
│   │   └── opensearch-lambda-log-shipper/
│   └── testing/selenium/     # Automated testing
├── environments/              # Environment deployments
│   ├── operations/           # Operations (Cross-environment services)
│   │   └── af-south-1/       # South Africa region
│   │       ├── network/
│   │       │   ├── vpc/      # VPC deployment
│   │       │   └── dns/      # Cloudflare DNS
│   │       ├── security/
│   │       │   ├── kms/      # KMS encryption keys
│   │       │   └── wazuh/    # Wazuh SIEM
│   │       │       ├── estate-endpoint/   # Mac fleet monitoring
│   │       │       └── server-endpoint/   # Infrastructure monitoring
│   │       ├── analytics/
│   │       │   ├── opensearch/  # Central logging platform
│   │       │   └── grafana/     # Grafana Cloud monitoring
│   │       ├── storage/
│   │       │   ├── opensearch-s3-compliance/  # OpenSearch logs
│   │       │   └── wazuh-s3-compliance/       # Wazuh logs
│   │       └── ec2/
│   │           └── bastion/  # Bastion host
│   ├── dev/                  # Development environment
│   │   └── af-south-1/       # South Africa region
│   │       └── applications/ # Development applications
│   └── prod/                 # Production environment
│       └── af-south-1/       # South Africa region
│           └── applications/ # Production applications
```

## 🚀 Quick Start

### Prerequisites
- Git access to this repository
- AWS account with appropriate IAM permissions
- macOS or Linux development environment

### 1. Clone Repository
```bash
<NAME_EMAIL>:infodocs/infrastructure-as-code.git
cd infrastructure-as-code
```

### 2. Set Up Development Environment
Run the automated setup script:
```bash
chmod +x setup-dev-environment.sh
./setup-dev-environment.sh
```
Installs: OpenTofu, Terragrunt, AWS CLI, kubectl, Helm, Docker, and utilities.

### 3. Configure AWS Credentials
```bash
aws configure
# AWS Access Key ID: [Your IAM user access key]
# AWS Secret Access Key: [Your IAM user secret key]
# Default region name: af-south-1
# Default output format: json
```

### 4. Verify Setup
```bash
tofu version                              # Should show v1.8.0+
terragrunt --version                      # Should show v0.54.0+
aws sts get-caller-identity              # Verify AWS credentials
aws s3 ls s3://infodocs-terraform-state  # Verify state bucket access
```

## 🏗️ Infrastructure Deployment

### Module Deployment Order
**CRITICAL**: Deploy modules in this exact order to satisfy dependencies:

1. **KMS** (Foundation): Encryption keys for securing data
2. **VPC** (Network): Network foundation with subnets and routing
3. **SSM** (Secrets): Parameter store for credentials and configuration
4. **Cloudflare** (DNS/Security): DNS management and WAF protection
5. **OpenSearch** (Monitoring): Log aggregation and analysis
6. **Wazuh** (Security): Security monitoring and threat detection
7. **Selenium** (Testing): Automated browser testing (optional)

### Automated Deployment
Use the enhanced CI/CD pipeline for automated deployment:
```bash
# Deploy all modules in correct order
git push origin main  # Triggers full pipeline

# Or use custom pipeline for full deployment
# In Bitbucket: Pipelines > Run custom pipeline > deploy-dev-full
```

### Manual Deployment (Step by Step)

#### 1. Deploy KMS Foundation
```bash
cd environments/dev/af-south-1/kms
terragrunt validate
terragrunt init
terragrunt plan
terragrunt apply
```

#### 2. Deploy VPC Foundation
```bash
cd environments/dev/af-south-1/vpc
terragrunt validate
terragrunt init
terragrunt plan
terragrunt apply
```

#### 3. Deploy SSM Parameters
```bash
cd environments/dev/af-south-1/ssm
terragrunt validate
terragrunt init
terragrunt plan
terragrunt apply
```

#### 4. Deploy Remaining Modules
Continue with Cloudflare, OpenSearch, Wazuh, and Selenium in order.

### Infrastructure Specifications
- **CIDR Ranges**: ********/16 (operations), ********/16 (dev), ********/16 (prod)
- **Operations Environment**: Cross-environment services (Wazuh, OpenSearch, DNS)
- **Subnets**: 6 per VPC (3 public, 3 private)
- **Components**: Internet Gateway, NAT Gateway, route tables
- **Encryption**: KMS keys for all sensitive data
- **Monitoring**: Centralized logging with 10-year retention for compliance

## 🌿 Development Workflow

### Branch Strategy
- **main**: Production
- **develop**: Integration/testing
- **feature/NFRPS-XXX-description**: Feature branches (e.g., feature/NFRPS-020-opensearch)

### Contribution Process
1. Create a Jira ticket (e.g., NFRPS-XXX)
2. Branch from develop: `git checkout -b feature/NFRPS-XXX-description`
3. Commit with ticket reference: `git commit -m "NFRPS-XXX #comment Added module"`
4. Create a PR to develop, request peer review
5. Merge after approval

### Commands
```bash
# Navigate to environment/service
cd environments/{env}/af-south-1/{service}

# Validate configuration
terragrunt validate

# Initialize (download providers and modules)
terragrunt init

# Plan (preview changes)
terragrunt plan

# Apply (deploy infrastructure)
terragrunt apply

# Destroy (CAUTION: removes infrastructure)
terragrunt destroy

# Clear cache (if needed)
terragrunt init --terragrunt-non-interactive --upgrade
```

### Cache Management
```bash
# Clear Terragrunt cache
find . -type d -name ".terragrunt-cache" -exec rm -rf {} +

# Clear OpenTofu cache
find . -type d -name ".terraform" -exec rm -rf {} +

# Clear lock files
find . -name ".terraform.lock.hcl" -delete
```

## 🔐 Security & Compliance

### Terraform State Management
- **S3 Bucket**: `infodocs-terraform-state` (encrypted, versioned)
- **DynamoDB Table**: `infodocs-terraform-locks` (state locking)
- **KMS Key**: `alias/infodocs-operations-encryption-key`
- **Region**: `af-south-1` (data sovereignty compliance)
- **Documentation**: See [docs/TERRAFORM_STATE.md](docs/TERRAFORM_STATE.md) for detailed information

### Encryption Strategy
- **KMS Keys**: Dedicated keys per environment with automatic rotation
- **Encrypted State**: S3 with versioning, DynamoDB locking, and encryption at rest
- **Secrets Management**: AWS SSM Parameter Store with KMS encryption
- **Compliance**: POPIA and GDPR ready (af-south-1 data residency)
- **Access Control**: IAM policies with least privilege principle

### Security Features
- **Wazuh**: Security monitoring, threat detection, and compliance reporting
- **Cloudflare**: WAF protection, DDoS mitigation, and SSL/TLS termination
- **KMS**: Centralized encryption key management with audit trails
- **VPC Security**: Private subnets, security groups, and NACLs
- **Monitoring**: CloudWatch logs with configurable retention periods

## 📊 Monitoring & Analytics

### Grafana Cloud Integration
Comprehensive monitoring solution using Grafana Cloud with Infrastructure as Code management.

**Features:**
- **AWS CloudWatch Integration**: EC2, Lambda, RDS, and custom metrics monitoring
- **OpenSearch Analytics**: Centralized log analysis and security event monitoring
- **Smart Slack Alerting**: Intelligent alert routing to appropriate teams
- **Production Dashboards**: 5 pre-built dashboards for infrastructure, applications, security, Lambda, and cost monitoring
- **Infrastructure as Code**: All dashboards, data sources, and alerts managed in Git

**Quick Start:**
```bash
# Deploy Grafana monitoring
cd environments/operations/af-south-1/analytics/grafana
terragrunt apply
```

**Documentation:**
- [Grafana Infrastructure as Code Guide](docs/GRAFANA_INFRASTRUCTURE_AS_CODE.md)
- [Laravel Metrics Integration](docs/LARAVEL_METRICS_INTEGRATION.md)
- [Slack Webhook Management](docs/SLACK_WEBHOOK_MANAGEMENT.md)

### OpenSearch Platform
Centralized logging and analytics platform for application and security monitoring.

**Features:**
- **Multi-Source Ingestion**: Lambda log shippers for Wazuh and application logs
- **Security Analytics**: Wazuh SIEM integration for threat detection
- **Compliance Logging**: Long-term retention for audit and compliance requirements
- **Real-time Analysis**: Live log streaming and alerting capabilities

### Wazuh Security Monitoring
Enterprise-grade security monitoring with separate instances for different use cases.

**Deployments:**
- **Server Endpoint**: Private monitoring for infrastructure and servers
- **Estate Endpoint**: Public monitoring for Mac fleet (35+ devices) with IP whitelisting
- **Integration**: Connected to OpenSearch for centralized security analytics

## 📦 Module Overview

### VPC Module
Creates a complete network foundation with public, private, and database subnets across multiple availability zones.
- **Features**: Multi-AZ design, NAT Gateways, Internet Gateway, route tables
- **Use Case**: Foundation for all other infrastructure

### KMS Module
Manages encryption keys for securing sensitive data.
- **Features**: Key rotation, access policies, alias management
- **Use Case**: Encrypt EBS volumes, RDS databases, SSM parameters

### SSM Module
Manages secure parameter storage for credentials and configuration.
- **Features**: Encrypted parameters, hierarchical organization
- **Use Case**: Store credentials for Wazuh, OpenSearch, and other services

### Cloudflare Module
Manages DNS records and security settings for domains.
- **Features**: DNS management, WAF rules, DDoS protection
- **Use Case**: Domain management and security

### OpenSearch Module
Deploys OpenSearch clusters for log aggregation and analysis.
- **Features**: Encrypted storage, access controls, dashboards
- **Use Case**: Centralized logging and monitoring

### Wazuh Module
Deploys Wazuh security monitoring platform.
- **Features**: Threat detection, compliance monitoring, security dashboards
- **Use Case**: Security monitoring and compliance

### Selenium Module
Deploys Selenium Grid for automated browser testing.
- **Features**: Chrome/Firefox browsers, scalable grid
- **Use Case**: Automated UI testing

## 🚀 Deployment Readiness Checklist

Before running `terragrunt plan` on each module:

### Prerequisites
- [ ] AWS credentials configured and tested
- [ ] OpenTofu v1.8.0+ installed
- [ ] Terragrunt v0.54.0+ installed
- [ ] All cache directories cleared
- [ ] SSM parameters populated with actual values

### Deployment Steps
1. [ ] Deploy KMS module first
2. [ ] Deploy VPC module second
3. [ ] Deploy SSM module third (update parameter values)
4. [ ] Deploy remaining modules in order
5. [ ] Verify all resources in AWS console
6. [ ] Test connectivity and functionality

### Post-Deployment
- [ ] Update SSM parameters with real credentials
- [ ] Configure monitoring alerts
- [ ] Set up backup policies
- [ ] Document any environment-specific configurations

## 📚 Documentation

### Core Documentation
- **[Terraform State Management](docs/TERRAFORM_STATE.md)** - State backend, locking, and collaboration
- **[Operations Environment](environments/operations/README.md)** - Cross-environment services architecture
- **[Bitbucket Variables](docs/BITBUCKET_VARIABLES.md)** - CI/CD pipeline configuration

### Architecture Diagrams
- **[Architecture Overview](docs/ARCHITECTURE_DIAGRAM.md)** - Visual infrastructure overview

## 🤝 Contributing
- **Prerequisites**: Setup script, AWS credentials, Jira/Bitbucket access
- **Standards**: Descriptive names, consistent tags, no hardcoded secrets
- **OpenTofu**: All configurations use OpenTofu v1.8.0+
- **Testing**: Validate and plan before applying changes

## 📈 Roadmap
- **Completed**: OpenTofu migration, enhanced pipeline, consistent structure
- **Next Priorities**: RDS, Lambda, enhanced monitoring modules
- **Future Enhancements**: Multi-region expansion, disaster recovery, cost optimization

## 📄 License
Proprietary code for InfoDocs; unauthorized use prohibited.

**Support**: Contact #infrastructure-integrations-team or create an NFRPS ticket.
