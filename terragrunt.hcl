# terragrunt.hcl - Root Terragrunt configuration for InfoDocs Infrastructure

locals {
  # Global configuration
  project_name = "infodocs"
  company_name = "infodocs"

  # Read environment and region configuration with fallbacks
  environment_vars = try(read_terragrunt_config(find_in_parent_folders("environment.hcl")), { locals = { environment = "dev" } })
  region_vars      = try(read_terragrunt_config(find_in_parent_folders("region.hcl")), { locals = { aws_region = "af-south-1", data_residency = "af-south-1" } })

  # Environment-specific variables
  environment = local.environment_vars.locals.environment
  aws_region  = local.region_vars.locals.aws_region

  # Common tags applied to all resources
  common_tags = {
    Project      = local.project_name
    Company      = local.company_name
    Environment  = local.environment
    Region       = local.aws_region
    ManagedBy    = "opentofu"
    CreatedBy    = "infodocs-iac-team"
    CostCenter   = "infrastructure"
    Owner        = "infrastructure-integrations-team"
    BackupPolicy = local.environment == "prod" ? "daily" : "weekly"
  }

  # Account-level configuration
  account_name = get_env("TG_ACCOUNT_NAME", "infodocs-main")
  account_id   = get_aws_account_id()

  # Cloudflare configuration
  cloudflare_api_token = data.aws_ssm_parameter.cloudflare_api_token.value
}

# Remote state configuration
remote_state {
  backend = "s3"
  config = {
    encrypt        = true
    bucket         = "infodocs-terraform-state"
    key            = "${path_relative_to_include()}/terraform.tfstate"
    region         = local.aws_region
    dynamodb_table = "infodocs-terraform-locks" # Matches your single table
  }
  generate = {
    path      = "backend.tf"
    if_exists = "overwrite_terragrunt"
  }
}

# Generate provider configuration
generate "provider" {
  path      = "provider.tf"
  if_exists = "overwrite_terragrunt"
  contents  = <<EOF
provider "aws" {
  region = "${local.aws_region}"

  default_tags {
    tags = {
      Project     = "infodocs"
      ManagedBy   = "opentofu"
      Environment = "${local.environment}"
    }
  }
}

provider "cloudflare" {
  api_token = "${local.cloudflare_api_token}"
}

terraform {
  required_providers {
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "~> 4.0"
    }
    aws = {
      source  = "hashicorp/aws"
      version = ">= 5.0.0"
    }
  }

  extra_arguments "tofu_binary" {
    commands = ["init", "plan", "apply", "destroy", "validate"]
    env_vars = {
      TF_CLI_ARGS = "-no-color"
      TF_PLUGIN_CACHE_DIR = "${get_env("HOME", "/")}/.terraform.d/plugin-cache"
    }
  }
}
EOF
}

# Generate data block for Cloudflare API token
generate "data_provider" {
  path      = "data_provider.tf"
  if_exists = "overwrite_terragrunt"
  contents  = <<EOF
data "aws_ssm_parameter" "cloudflare_api_token" {
  name            = "/infodocs/cloudflare/api_token"
  with_decryption = true
}
EOF
}

# Cloudflare API token is accessed via data source in provider configuration

# Global inputs that apply to all modules
inputs = {
  tags = {
    Environment = local.environment
    Project     = "infodocs"
    ManagedBy   = "opentofu"
    CostCenter  = "infrastructure"
    Owner       = "devops-team"
    Application = "infodocs-platform"
  }

  environment = local.environment
  region      = local.aws_region

  enable_deletion_protection = local.environment == "prod" ? true : false
  backup_retention_days      = local.environment == "prod" ? 30 : 7

  enable_detailed_monitoring = local.environment == "prod" ? true : false
  log_retention_days         = local.environment == "prod" ? 90 : 30

  enable_cost_optimization = true

  data_residency_region = local.region_vars.locals.data_residency
  compliance_framework  = ["POPIA", "GDPR"]
}

# Terragrunt configuration
terraform {
  extra_arguments "retry_lock" {
    commands = [
      "init",
      "apply",
      "refresh",
      "import",
      "plan",
      "taint",
      "untaint"
    ]
    arguments = [
      "-lock-timeout=20m"
    ]
  }

  extra_arguments "disable_input" {
    commands = [
      "init",
      "apply",
      "destroy",
      "refresh",
      "import",
      "plan",
      "taint",
      "untaint"
    ]
    arguments = [
      "-input=false"
    ]
  }

  extra_arguments "parallelism" {
    commands = [
      "apply",
      "plan",
      "destroy"
    ]
    arguments = [
      "-parallelism=10"
    ]
  }
}

terraform_version_constraint  = ">= 1.8.0"
terragrunt_version_constraint = ">= 0.50"
