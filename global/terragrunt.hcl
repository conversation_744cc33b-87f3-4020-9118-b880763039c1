# Global Terragrunt configuration that applies to all environments

locals {
  # Global configuration
  project_name = "infodocs"
  company_name = "infodocs"

  # Common tags applied to all resources
  common_tags = {
    Project    = local.project_name
    Company    = local.company_name
    ManagedBy  = "opentofu"
    CreatedBy  = "infodocs-operations"
    CostCenter = "infrastructure"
    Owner      = "infodocs-operations"
  }
}

# Configure Terragrunt to automatically store tfstate files in S3
remote_state {
  backend = "s3"
  config = {
    bucket         = "infodocs-terraform-state"
    key            = "${path_relative_to_include()}/terraform.tfstate"
    region         = "af-south-1"
    encrypt        = true
    dynamodb_table = "infodocs-terraform-locks"
  }
  generate = {
    path      = "backend.tf"
    if_exists = "overwrite_terragrunt"
  }
}

# Generate provider configuration
generate "provider" {
  path      = "provider.tf"
  if_exists = "overwrite_terragrunt"
  contents  = <<EOF
provider "aws" {
  region = "af-south-1"

  default_tags {
    tags = {
      Project     = "infodocs"
      ManagedBy   = "opentofu"
      Environment = "operations"
    }
  }
}

provider "cloudflare" {
  # API token will be read from environment variable CLOUDFLARE_API_TOKEN
}
EOF
}

# Global inputs that apply to all modules
inputs = {
  # Common tags for billing and resource management
  tags = local.common_tags
}
