# Global Configuration

This directory contains global configuration files that apply to all environments in the InfoDocs infrastructure.

## Contents

- **terragrunt.hcl**: Global Terragrunt configuration including remote state, provider settings, and common inputs
- **variables.tf**: Global variable definitions used across all environments
- **outputs.tf**: Global outputs that can be referenced by other modules

## Usage

The global configuration is automatically included in all environment-specific Terragrunt configurations via:

```hcl
include {
  path = find_in_parent_folders()
}
```

## Global Variables

The global configuration defines several important variables:

- **Project name**: "infodocs"
- **Company name**: "infodocs"
- **Owner**: "infrastructure-integrations-team"
- **Cost center**: "infrastructure"
- **Supported environments**: operations, dev, prod
- **Supported regions**: af-south-1 (Africa/Cape Town)
- **VPC CIDR blocks**:
  - Operations: 10.1.0.0/16 (Cross-environment services)
  - Dev: ********/16 (Development applications)
  - Prod: ********/16 (Production applications)

## Remote State Configuration

The global configuration sets up S3 remote state storage with:

- **Bucket**: infodocs-terraform-state
- **DynamoDB table**: infodocs-terraform-locks
- **Encryption**: Enabled
- **Region**: af-south-1

## Provider Configuration

The global configuration generates AWS and Cloudflare provider configurations with:

- **AWS Region**: af-south-1
- **Default tags**: Project, ManagedBy, Environment
- **Cloudflare**: API token from environment variable

## Common Tags

All resources are tagged with:

- Project: infodocs
- Company: infodocs
- ManagedBy: terragrunt
- CreatedBy: infodocs-iac-team
- CostCenter: infrastructure
- Owner: infrastructure-integrations-team
- Environment: (environment-specific)
<!-- BEGINNING OF PRE-COMMIT-TERRAFORM DOCS HOOK -->
## Requirements

No requirements.

## Providers

No providers.

## Modules

No modules.

## Resources

No resources.

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_company"></a> [company](#input\_company) | Company name | `string` | `"infodocs"` | no |
| <a name="input_cost_center"></a> [cost\_center](#input\_cost\_center) | Cost center for billing purposes | `string` | `"infrastructure"` | no |
| <a name="input_default_tags"></a> [default\_tags](#input\_default\_tags) | Default tags to apply to all resources | `map(string)` | <pre>{<br/>  "CostCenter": "infrastructure",<br/>  "ManagedBy": "terragrunt",<br/>  "Owner": "infrastructure-integrations-team",<br/>  "Project": "infodocs"<br/>}</pre> | no |
| <a name="input_owner"></a> [owner](#input\_owner) | Team responsible for the infrastructure | `string` | `"infrastructure-integrations-team"` | no |
| <a name="input_project"></a> [project](#input\_project) | Project name | `string` | `"infodocs"` | no |
| <a name="input_supported_environments"></a> [supported\_environments](#input\_supported\_environments) | List of supported environments | `list(string)` | <pre>[<br/>  "dev",<br/>  "staging",<br/>  "prod"<br/>]</pre> | no |
| <a name="input_supported_regions"></a> [supported\_regions](#input\_supported\_regions) | List of supported AWS regions | `map(string)` | <pre>{<br/>  "af-south-1": "Africa (Cape Town)"<br/>}</pre> | no |
| <a name="input_vpc_cidr_blocks"></a> [vpc\_cidr\_blocks](#input\_vpc\_cidr\_blocks) | CIDR blocks for VPCs in each environment | `map(string)` | <pre>{<br/>  "dev": "********/16",<br/>  "prod": "********/16",<br/>  "staging": "********/16"<br/>}</pre> | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_company_name"></a> [company\_name](#output\_company\_name) | Company name |
| <a name="output_cost_center"></a> [cost\_center](#output\_cost\_center) | Cost center for billing purposes |
| <a name="output_default_tags"></a> [default\_tags](#output\_default\_tags) | Default tags to apply to all resources |
| <a name="output_owner"></a> [owner](#output\_owner) | Team responsible for the infrastructure |
| <a name="output_project_name"></a> [project\_name](#output\_project\_name) | Project name |
| <a name="output_supported_environments"></a> [supported\_environments](#output\_supported\_environments) | List of supported environments |
| <a name="output_supported_regions"></a> [supported\_regions](#output\_supported\_regions) | List of supported AWS regions |
| <a name="output_vpc_cidr_blocks"></a> [vpc\_cidr\_blocks](#output\_vpc\_cidr\_blocks) | CIDR blocks for VPCs in each environment |
<!-- END OF PRE-COMMIT-TERRAFORM DOCS HOOK -->
