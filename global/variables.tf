# Global variables used across all environments

variable "project" {
  description = "Project name"
  type        = string
  default     = "infodocs"
}

variable "company" {
  description = "Company name"
  type        = string
  default     = "infodocs"
}

variable "owner" {
  description = "Team responsible for the infrastructure"
  type        = string
  default     = "infrastructure-integrations-team"
}

variable "cost_center" {
  description = "Cost center for billing purposes"
  type        = string
  default     = "infrastructure"
}

variable "supported_environments" {
  description = "List of supported environments"
  type        = list(string)
  default     = ["dev", "prod", "operations"]
}

variable "supported_regions" {
  description = "List of supported AWS regions"
  type        = map(string)
  default = {
    "af-south-1" = "Africa (Cape Town)"
  }
}

variable "vpc_cidr_blocks" {
  description = "CIDR blocks for VPCs in each environment"
  type        = map(string)
  default = {
    "dev"        = "********/16"
    "prod"       = "********/16"
    "operations" = "********/16"
  }
}

variable "default_tags" {
  description = "Default tags to apply to all resources"
  type        = map(string)
  default = {
    Project    = "infodocs"
    ManagedBy  = "terragrunt"
    CostCenter = "infrastructure"
    Owner      = "infrastructure-integrations-team"
  }
}
