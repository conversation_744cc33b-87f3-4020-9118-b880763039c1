# Global outputs that can be referenced by other modules

output "project_name" {
  description = "Project name"
  value       = var.project
}

output "company_name" {
  description = "Company name"
  value       = var.company
}

output "owner" {
  description = "Team responsible for the infrastructure"
  value       = var.owner
}

output "cost_center" {
  description = "Cost center for billing purposes"
  value       = var.cost_center
}

output "supported_environments" {
  description = "List of supported environments"
  value       = var.supported_environments
}

output "supported_regions" {
  description = "List of supported AWS regions"
  value       = var.supported_regions
}

output "vpc_cidr_blocks" {
  description = "CIDR blocks for VPCs in each environment"
  value       = var.vpc_cidr_blocks
}

output "default_tags" {
  description = "Default tags to apply to all resources"
  value       = var.default_tags
}
