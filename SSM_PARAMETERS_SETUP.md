# 🔒 SSM Parameters Setup Guide - SECURE APPROACH

## **✅ Correct Approach: Secrets in Parameter Store**

**IMPORTANT**: Secrets should NEVER be hardcoded in Terragrunt files. Instead:

1. **Create secrets in AWS Parameter Store first** (using the script below)
2. **Modules reference them via data sources**
3. **Terragrunt files only contain environment-specific config**

## **🚀 Quick Setup**

Run this script to create all required global parameters:

```bash
# Run the setup script
./scripts/setup-ssm-secrets.sh
```

## **📋 What the Script Creates**

The script creates these global parameters (shared across all environments):

```bash
# 1. Cloudflare API Token (from your memory: /infodocs/cloudflare/api_token)
aws ssm put-parameter \
  --name "/infodocs/cloudflare/api_token" \
  --value "YOUR_CLOUDFLARE_API_TOKEN" \
  --type "SecureString" \
  --description "Cloudflare API token for DNS management"

# 2. IAC Credentials (from your memory: /infodocs/iac/credentials/*)
aws ssm put-parameter \
  --name "/infodocs/iac/credentials/access_key" \
  --value "YOUR_IAC_ACCESS_KEY" \
  --type "SecureString" \
  --description "Access key for infrastructure automation"

aws ssm put-parameter \
  --name "/infodocs/iac/credentials/secret_key" \
  --value "YOUR_IAC_SECRET_KEY" \
  --type "SecureString" \
  --description "Secret key for infrastructure automation"

# 3. KMS Credentials (from your memory: /infodocs/kms/credentials/*)
aws ssm put-parameter \
  --name "/infodocs/kms/credentials/access_key" \
  --value "YOUR_KMS_ACCESS_KEY" \
  --type "SecureString" \
  --description "Access key for KMS operations"

aws ssm put-parameter \
  --name "/infodocs/kms/credentials/secret_key" \
  --value "YOUR_KMS_SECRET_KEY" \
  --type "SecureString" \
  --description "Secret key for KMS operations"
```

### **🔄 Parameters to Update in Terragrunt Files**

Replace these placeholder values in `environments/dev/af-south-1/security/ssm/terragrunt.hcl`:

```bash
# Required values to replace:
YOUR_CLOUDFLARE_ZONE_ID_HERE          # Get from Cloudflare dashboard
YOUR_CLOUDFLARE_ACCOUNT_ID_HERE       # Get from Cloudflare dashboard
YOUR_WAZUH_ADMIN_PASSWORD_HERE        # Strong password (e.g., WazuhAdmin2024!Secure)
YOUR_WAZUH_API_KEY_HERE               # Generate: openssl rand -hex 32
YOUR_WAZUH_REGISTRATION_PASSWORD_HERE # Generate: openssl rand -base64 16
YOUR_OPENSEARCH_MASTER_PASSWORD_HERE  # Strong password (e.g., OpenSearch2024!Admin)
YOUR_OPENSEARCH_API_KEY_HERE          # Generate: openssl rand -hex 32
YOUR_SSH_PUBLIC_KEY_HERE              # Content of ~/.ssh/infodocs-dev-infrastructure.pub
YOUR_DATABASE_MASTER_PASSWORD_HERE    # Generate: openssl rand -base64 32
YOUR_DATABASE_APP_PASSWORD_HERE       # Generate: openssl rand -base64 32
```

### **🔐 Generate Secure Values**

Use these commands to generate secure values:

```bash
# Generate strong passwords
echo "Wazuh Admin Password: WazuhAdmin2024!$(openssl rand -base64 8)"
echo "OpenSearch Master Password: OpenSearch2024!$(openssl rand -base64 8)"
echo "Database Master Password: $(openssl rand -base64 32)"
echo "Database App Password: $(openssl rand -base64 32)"

# Generate API keys
echo "Wazuh API Key: $(openssl rand -hex 32)"
echo "OpenSearch API Key: $(openssl rand -hex 32)"
echo "Wazuh Registration Password: $(openssl rand -base64 16)"

# Get SSH public key (if exists)
cat ~/.ssh/infodocs-dev-infrastructure.pub 2>/dev/null || echo "SSH key not found - generate with: ssh-keygen -t rsa -b 4096 -f ~/.ssh/infodocs-dev-infrastructure"
```

### **📋 Verification Commands**

After updating the values, verify the parameters:

```bash
# List all InfoDocs parameters
aws ssm get-parameters-by-path \
  --path "/infodocs" \
  --recursive \
  --query 'Parameters[].Name' \
  --output table

# Test parameter access (replace with actual parameter name)
aws ssm get-parameter \
  --name "/infodocs/cloudflare/api_token" \
  --with-decryption \
  --query 'Parameter.Value' \
  --output text
```

### **✅ Checklist Before Testing**

- [ ] Global SSM parameters created (`/infodocs/cloudflare/api_token`, etc.)
- [ ] Cloudflare Zone ID and Account ID obtained
- [ ] Strong passwords generated for Wazuh and OpenSearch
- [ ] API keys generated for services
- [ ] SSH public key available
- [ ] All placeholder values replaced in terragrunt.hcl files
- [ ] Parameters verified with AWS CLI

### **🚨 Security Notes**

- All sensitive parameters use `SecureString` type with KMS encryption
- Never commit actual values to version control
- Rotate passwords quarterly
- Use strong, unique passwords for each service
- Monitor parameter access via CloudTrail

---

## **✅ Database Parameters Updated**

**IMPORTANT**: Database parameters have been renamed to avoid conflicts with your existing RDS:

- ❌ Old: `/infodocs/database/master_password`
- ✅ New: `/infodocs/infrastructure/database/master_password`
- ❌ Old: `/infodocs/database/app_password`
- ✅ New: `/infodocs/infrastructure/database/app_password`

These are for future infrastructure databases, completely separate from your existing RDS.

## **🔐 Access Your Credentials**

Use the helper script to get all login credentials:

```bash
./scripts/get-login-credentials.sh
```

This will show you:
- Wazuh admin credentials
- OpenSearch admin credentials
- Cloudflare configuration
- Infrastructure database passwords
- API keys for programmatic access

**The database parameters are now safely updated and won't conflict with your existing RDS!**
